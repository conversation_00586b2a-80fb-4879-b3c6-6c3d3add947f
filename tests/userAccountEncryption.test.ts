import { decryptUserInformation, encryptUserInformation, generateRandomString } from '../src/encryption';

import { describe, it } from 'node:test';
import assert from 'node:assert';

describe('encryptUserInformation', () => {
    it('returns the encrypted user information', () => {
        const username = 'test';
        const userId = '1';
        const password = 'password';

        const userInformation = {
            username: username,
            userId: userId,
            pwd: password,
        };

        const encryptedUserInformation = encryptUserInformation(JSON.stringify(userInformation));
        assert.notEqual(encryptedUserInformation, `${username}:${userId}:${password}`);

        const decryptedUserInformation = decryptUserInformation(encryptedUserInformation);
        const decryptedUserInformationObject = JSON.parse(decryptedUserInformation!);

        assert.equal(decryptedUserInformationObject.username, username);
        assert.equal(decryptedUserInformationObject.userId, userId);
        assert.equal(decryptedUserInformationObject.pwd, password);
    });
});

describe('generateRandomString', () => {
    it('returns a random string of the given length', () => {
        const length = 10;
        const randomString = generateRandomString(length);
        assert.equal(randomString.length, length);
    });
});
