import { Job, Worker } from 'bullmq';
import { pathToFileURL } from 'url';
import { Worker as ThreadWorker } from 'worker_threads';

import { WORKER_CONCURRENCY } from './config';
import { activeJobsCount, nodeUtilization } from './metrics';
import redis from './redis';
import { logging, JobData, LoginError } from 'shared';

const PROCESSOR_POOL = new Map();

export function initializeWorker() {
    // Initializing worker
    const worker = new Worker<JobData>(
        redis.QUEUE_NAME,
        async (job: Job<JobData>) => {
            const processor = new ThreadWorker(pathToFileURL(__dirname + '/processor.js'), {
                workerData: {
                    id: job.id,
                    name: job.name,
                    data: job.data,
                },
            });
            PROCESSOR_POOL.set(job.id, processor);
            updateMetrics();
            return new Promise((resolve, reject) => {
                processor.on('message', (message) => {
                    job.updateProgress(message);
                });
                processor.on('error', (error) => {
                    reject(error);
                });
                processor.on('exit', (code) => {
                    resolve(code);
                });
            });
        },
        {
            connection: redis.connection,
            maxStalledCount: Number.MAX_SAFE_INTEGER, // Unlimited recovery from stalled state
            stalledInterval: 10000,
            lockDuration: 10000,
            lockRenewTime: 5000,
            concurrency: WORKER_CONCURRENCY,
        },
    );

    worker.on('active', (job: Job<JobData>) => {
        const { id } = job;
        logging.info(`${id} job is active!`);
    });

    worker.on('completed', async (job: Job<JobData>) => {
        const { id } = job;
        PROCESSOR_POOL.delete(id);
        updateMetrics();
        logging.info(`${id} job has completed!`);
    });

    worker.on('failed', async (job: Job<JobData> | undefined, err: Error) => {
        const { id } = job || {};
        if (id) {
            PROCESSOR_POOL.delete(id);
            updateMetrics();
        }

        logging.error(
            `${id} job has failed with ${err.name}`,
            err,
            { stack: err.stack?.replace(/\s*\n\s*/g, ' | ') }
        );
    });

    const updateMetrics = () => {
        activeJobsCount.labels({ active: String(worker.isRunning()) }).set(PROCESSOR_POOL.size);
        nodeUtilization.labels({ active: String(worker.isRunning()) }).set(PROCESSOR_POOL.size / WORKER_CONCURRENCY);
    }

    worker.on('error', (err: Error) => {
        logging.error(`Worker errored with ${err.message}, stack: ${err.stack?.replace(/\s*\n\s*/g, ' | ')}`, err);
    });

    worker.on('closing', (msg: string) => {
        logging.info(`Worker is closing: ${msg}`);
        for (const [id, processor] of PROCESSOR_POOL.entries()) {
            logging.info(`Notifying processor ${id} to close...`);
            try {
                processor.postMessage({ type: 'closing' });
            } catch (err) {
                logging.warn(`Failed to notify processor ${id}:`, err);
            }
        }
    });

    return worker;
}
