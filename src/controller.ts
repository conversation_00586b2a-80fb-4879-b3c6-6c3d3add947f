import { addUserAccounts, dropUserAccounts } from './mongo';
import { UserAccountRequestBody } from './models/api';
import { generateRandomString } from './encryption';

export type AddUserAccountsResponse = {
    playerId: string;
};

export async function addUserAccountsAndPlayers(request: UserAccountRequestBody) {
    // Transactions are not supported since replica sets for Mongo are not enabled
    let players: AddUserAccountsResponse[] = [];

    // Add device id to user account request if missing
    request.userAccounts = request.userAccounts.map((userAccountRequest) => ({
        ...userAccountRequest,
        deviceId: userAccountRequest.deviceId || generateRandomString(32).toUpperCase(),
    }));

    try {
        players = await addUserAccounts(request);
    } catch (error) {
        if (players.length > 0) {
            const playerIds = players.map((player) => player.playerId);
            await dropUserAccounts(playerIds);
        }
        throw error;
    }
    return players;
}
