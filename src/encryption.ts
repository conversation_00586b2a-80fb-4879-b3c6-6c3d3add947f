import crypto from 'crypto';

const algorithm = 'aes-256-cbc';
// const key = crypto.randomBytes(32);
const key = Buffer.from('L7skvdSMZuCRn5p/tHzhlYAfnZdXQsm+6OW9JrnjT0o=', 'base64');
const iv = Buffer.from('CfpOW+vFgT9188NJediM2g==', 'base64');

export function encryptUserInformation(userInformationJson: string) {
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(userInformationJson, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
}

export function decryptUserInformation(userInformation: string) {
    const cipher = crypto.createDecipheriv(algorithm, key, iv);
    let decrypted = cipher.update(userInformation, 'base64', 'utf8');
    decrypted += cipher.final('utf8');
    return decrypted;
}

export function generateRandomString(length: number) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString('hex')
        .slice(0, length);
}
