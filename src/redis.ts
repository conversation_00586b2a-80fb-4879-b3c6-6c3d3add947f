import Redis from 'ioredis';
import { Queue } from 'bullmq';

const QUEUE_NAME = '{worker}';

const redisNode = {
    host: process.env.REDIS_HOST || 'localhost',
    port: Number(process.env.REDIS_PORT) || 6379,
};

const clusterOptions = {
    slotsRefreshTimeout: 2000,
};

// TODO: separate connection method
const redis =
    process.env.REDIS_CLUSTER_MODE === 'true'
        ? new Redis.Cluster([redisNode], clusterOptions)
        : new Redis({ ...redisNode, maxRetriesPerRequest: null });

const workerQueue = new Queue(QUEUE_NAME, { connection: redis });

const store = async (key: string, value: string, seconds: number = 300) => {
    await redis.set(key, value, 'EX', seconds);
}

const fetch = async (key: string): Promise<string | null> => {
    return redis.get(key);
}

const remove = async (key: string) => {
    await redis.del(key);
}

export default {
    connection: redis,
    store,
    fetch,
    remove,
    workerQueue,
    QUEUE_NAME
}
