import WebSocket from 'ws';
import { featureFlagValue, logging, MttConfigType } from 'shared';
import { MTT_CONFIG_MTTAPI, MTT_CONFIG_MTTGAME, MTT_CONFIG_MTTWORLD } from '../config';
import redis from '../redis';

const CONFIG_URL = process.env.MTT_CONFIG_URL || '';
const CONFIG_TTL_SECONDS = Number(process.env.MTT_CONFIG_TTL_SECONDS) || 8 * 60 * 60; // 8 hours, in seconds
const CHECK_TIMEOUT_MS = 2000;

type MttConfigUrls = {
    worlds: string[];
    games: string[];
    apis: string[];
};

const DEFAULT_MTT_CONFIG = {
    mttWorld: MTT_CONFIG_MTTWORLD,
    mttGame: MTT_CONFIG_MTTGAME,
    mttApi: MTT_CONFIG_MTTAPI,
};

async function fetchUrlConfig(): Promise<MttConfigUrls> {
    const res = await fetch(CONFIG_URL, { signal: AbortSignal.timeout(CHECK_TIMEOUT_MS) });
    if (!res.ok) throw new Error(`url_config fetch failed: ${res.status}`);
    const data = await res.json();
    const protoWs = data.ws || 'wss';
    const protoHttp = data.http || 'https';

    const normalizeAndCheck = async (urls: string[], path: string, isWs: boolean) => {
        const checks = urls.map(async (raw) => {
            const candidate = raw.match(/^[a-z]+:\/\//) ? raw : `${isWs ? protoWs : protoHttp}://${raw}`;
            const testUrl = candidate.endsWith(path) ? candidate : `${candidate}${path}`;
            const alive = isWs
                ? await checkWs(testUrl).catch(() => false)
                : await checkHttp(testUrl).catch(() => false);
            return alive ? candidate : null;
        });
        return (await Promise.all(checks)).filter((u): u is string => !!u);
    };

    const [worlds, games, apis] = await Promise.all([
        normalizeAndCheck(data.world || [], '/world', true),
        normalizeAndCheck(data.game || [], '/mtt/holdem', true),
        normalizeAndCheck(data.api || [], '/api/mtt/tournamentList', false),
    ]);

    if (!worlds.length || !games.length || !apis.length) {
        throw new Error('No healthy endpoints in url_config');
    }

    return { worlds, games, apis };
}

async function checkHttp(url: string): Promise<boolean> {
    const resp = await fetch(url, {
        method: 'HEAD',
        signal: AbortSignal.timeout(CHECK_TIMEOUT_MS),
    });
    return resp.ok;
}

async function checkWs(url: string): Promise<boolean> {
    return new Promise((resolve) => {
        const ws = new WebSocket(url);
        const t = setTimeout(() => {
            ws.close();
            resolve(false);
        }, CHECK_TIMEOUT_MS);
        ws.onopen = () => {
            clearTimeout(t);
            ws.close();
            resolve(true);
        };
        ws.onerror = () => {
            clearTimeout(t);
            resolve(false);
        };
    });
}

export async function getMttConfig(): Promise<MttConfigType> {
    if (!await featureFlagValue('enable-mtt-config-provider', {}, false)) {
        logging.info('Using default MTT config', DEFAULT_MTT_CONFIG);
        return DEFAULT_MTT_CONFIG;
    }

    let rawMttConfigUrls = await redis.fetch('mtt:config');

    if (!rawMttConfigUrls) {
        try {
            const urls = await fetchUrlConfig();
            await redis.store('mtt:config', JSON.stringify(urls), CONFIG_TTL_SECONDS);
            rawMttConfigUrls = JSON.stringify(urls);
        } catch (err) {
            logging.error('Failed to load mtt url config', err);
            return DEFAULT_MTT_CONFIG;
        }
    }

    const { worlds, games, apis } = JSON.parse(rawMttConfigUrls) as MttConfigUrls;
    const idx = Math.floor(Math.random() * Math.max(worlds.length, games.length, apis.length));
    return {
        mttWorld: worlds[idx % worlds.length],
        mttGame: games[idx % games.length],
        mttApi: apis[idx % apis.length],
    };
}
