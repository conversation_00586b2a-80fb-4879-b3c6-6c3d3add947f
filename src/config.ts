import process from 'node:process';

const getEnvVar = (name: string): string => {
    const value = process.env[name]
    if (!value) throw new Error(`${name} is not set`)
    return value
}

export const PORT = Number(process.env.PORT) || 3000;
export const WORKER_CONCURRENCY = Number(process.env.WORKER_CONCURRENCY) || 64;

// WPK
export const WPK_URL = getEnvVar('WPK_URL')

// WPTGO
export const WPTGO_URL = getEnvVar('WPTGO_URL')
export const WPTGO_WS_URL = getEnvVar('WPTGO_WS_URL')
export const WPTGO_APP_BUNDLE_ID = getEnvVar('WPTGO_APP_BUNDLE_ID')

// MTT
export const MTT_CONFIG_MTTWORLD = getEnvVar('MTT_CONFIG_MTTWORLD')
export const MTT_CONFIG_MTTGAME = getEnvVar('MTT_CONFIG_MTTGAME')
export const MTT_CONFIG_MTTAPI = getEnvVar('MTT_CONFIG_MTTAPI')

export const DEBUG_REBUY_THRESHOLD = Number(process.env.DEBUG_REBUY_THRESHOLD) || 0;

export let CONTAINER_ID = '';
if (process.env.ECS_CONTAINER_METADATA_URI_V4) {
    // Checking the last part which no not includes /
    const regex = /[^/]+$/;
    const match = process.env.ECS_CONTAINER_METADATA_URI_V4.match(regex);
    if (match) {
        CONTAINER_ID = match[0];
    }
}
