import { MttUserData, Balance, UserData, PlatformUserData } from 'shared';
import { TransferJobData } from '../models/model';

export interface PlatformStrategy {
    init: (proxyUrl?: string) => void;
    login: (user: UserData) => Promise<PlatformUserData>;
    fetchMttUserData: (platformUser: PlatformUserData) => Promise<MttUserData>;
    balance: (platformUser: PlatformUserData) => Promise<Balance>;
    transfer: (platformUser: PlatformUserData, params: TransferJobData) => Promise<void>;
    clearCache: (userId: number) => Promise<void>;
}

