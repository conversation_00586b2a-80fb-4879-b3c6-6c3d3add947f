import { JobDataHandler, JobType, JobData, LoginError, MttUserData } from 'shared';
import { MttMain } from 'mtt';
import { GamePlatformAdapter } from '../models/model';
import { getMttConfig } from '../providers/mttConfigProvider';
import redis from '../redis';

class MttAdapterClass implements GamePlatformAdapter {
    async init(proxyUrl?: string): Promise<void> {
        const envMttConfig = await getMttConfig();
        MttMain.init(saveDataToCache, fetchDataFromCache, envMttConfig, proxyUrl);
    }

    async start(
        userData: MttUserData,
        action: JobType,
        tournamentId: number,
        jobData: JobData,
        onMessage: JobDataHandler,
    ): Promise<void> {
        if (!userData.mtt?.token) {
            throw new LoginError('MTT token not found in userData');
        }

        const ticketId = jobData.ticketId ? Number(jobData.ticketId) : 0;
        return MttMain.run(
            userData.mtt.token,
            action,
            tournamentId,
            ticketId,
            onMessage,
            userData?.user?.nickname,
            jobData.profileName,
        );
    }

    async stop(_: JobType) {}

    isMtt(): boolean {
        return true;
    }
}

async function saveDataToCache(id: number, data: any) {
    redis.store(`mtt:${id}`, JSON.stringify(data));
}

async function fetchDataFromCache(id: number) {
    const result = await redis.fetch(`mtt:${id}`);
    if (result) {
        return JSON.parse(result);
    }
    return null;
}

export const MttAdapter = new MttAdapterClass();
