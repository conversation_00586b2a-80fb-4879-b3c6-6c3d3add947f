# Worker

To run locally execute this

```shell
    npm install
    npm run build:libs
    npm run start:local
```

### Branching rules
#### Branches and environments
- **main** represents the development environment.
- **staging** refers to the staging environment.
- **release** is the production environment.
##### Rules
It is better to use the squash strategy for feature branches. For merging from main to staging and from staging to release, it is better to use fast-forward.

#### VS Code debug config
```json
{
    "name": "Run Worker",
    "type": "node",
    "request": "launch",
    "program": "${workspaceFolder}/src/index.ts",
    "runtimeArgs": [],
    "envFile": "${workspaceFolder}/.env.local",
    "outFiles": [
        "${workspaceFolder}/**/*.js"
    ],
    "console": "integratedTerminal",
    "preLaunchTask": "tsc: build - tsconfig.json",
}
```
