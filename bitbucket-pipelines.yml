# Any base image required for service specific steps needs to have <PERSON><PERSON> and Git layer to run Image release
image: python:3.12

definitions:
  services:
    docker:
      type: docker
      memory: 2048

pipelines:

# ------------
# CI PIPELINES
# ------------
  pull-requests:
    '**':
      - step:
          name: Build & Test
          image: node:22
          script:
            - npm ci
            - npm run test:all

  branches:
    # VERSION RELEASE FOR DEV: Applies development tag release after merge to main branch
    main:
      - step:
          name: Development Image Version
          script:
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_version_dev.sh

  custom:
    # IMAGE VERSION RELEASE FOR TEST (MANUAL): Applies test tag release from any development branch
    test-tag:
      - variables:          
        - name: TAG_PREFIX
          description: "Prefix is set to specify a development tag release." 
          default: "test-"
          allowed-values:
            - "test-"
      - step:
          name: Test Image Version
          script:
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_version_test.sh

    # IMAGE VERSION RELEASE FOR STG (MANUAL): Applies staging tag release from main branch
    staging-tag:
      - variables:          
        - name: TAG_SUFFIX
          description: "Suffix is set to specify a staging tag release." 
          default: "-stg"
          allowed-values:
            - "-stg"
      - step:
          name: Staging Image Version
          script:
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_version_stg.sh
    
    # IMAGE VERSION RELEASE FOR PROD (MANUAL): Applies production tag release from main branch
    production-tag:
      - step:
          name: Production Image Version
          script:
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_version_prod.sh

    # CUSTOM IMAGE VERSION RELEASE (MANUAL): Applies custom tag release from main branch. Recommended for rollbacks.
    custom-tag:
      - variables:     
          - name: TAG_SUFFIX
            description: "Suffix must match the target environment branch. Leave empty for production." 
            default: "-dev"
            allowed-values:
              - "-dev"
              - "-stg"
              - ""
          - name: CUSTOM_TAG
            description: "Tag can be higher or lower than the current tag. Example: 0.1.0"
      - step:
          name: Custom Image Version
          script:
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_custom.sh

# -----------------------
# CD PIPELINES
# -----------------------

  # IMAGE RELEASE: Deploys image release based on tags.
  tags:
    "test-*":
      - step:
          name: Test Image Release
          deployment: Test
          size: 2x
          services:
            - docker
          script:
            - apt-get update && apt-get install -y awscli
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_release_test.sh

    "*.*.*-dev":
      - step:
          name: Development Image Release
          deployment: Development
          size: 2x
          services:
            - docker
          script:  
            - apt-get update && apt-get install -y awscli
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_release.sh

    "*.*.*-stg":
      - step:
          name: Staging Image Release
          deployment: Staging
          size: 2x
          services:
            - docker
          script:
            - apt-get update && apt-get install -y awscli
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_release.sh

    "*.*.*":
      - step:
          name: Production Image Release
          script:
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_release_notification.sh

      - step:
          name: Manual Approval Check
          trigger: manual
          script:
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_release_approval.sh
      - step:
          name: Proceed with Image Release
          deployment: Production
          size: 2x
          services:
            - docker
          script:
            - apt-get update && apt-get install -y awscli
            - <NAME_EMAIL>:aceguardian/bitbucket-pipelines.git
            - ./bitbucket-pipelines/docker-image-ecr-release/scripts/image_release.sh 
