{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "<PERSON> Worker (<PERSON>)",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/dist/src/index.js",
            "preLaunchTask": "tsc: build - tsconfig.json",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "runtimeArgs": [],
            "console": "integratedTerminal",
            "env": {
                "NODE_ENV": "local"
            }
        },
        {
            "name": "Run Worker (Alex)",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/dist/src/index.js",
            "runtimeArgs": [
                "-r",
                "ts-node/register"
            ],
            "envFile": "${workspaceFolder}/envfiles/dev.env",
            "outFiles": [
                "${workspaceFolder}/**/*.js"
            ],
            "console": "integratedTerminal",
            "preLaunchTask": "tsc: build - tsconfig.json"
        }
    ]
}