{"ServerErrorCode1": {"-value": "OK"}, "ServerErrorCode2": {"-value": "New version available, please update first"}, "ServerErrorCode3": {"-value": "Unable to find the player"}, "ServerErrorCode4": {"-value": "Logined by another equipment, please login again"}, "ServerErrorCode5": {"-value": "Unable to inquire token, please contact our customer service"}, "ServerErrorCode6": {"-value": "Unable to obtain data from global server"}, "ServerErrorCode7": {"-value": "Internal RPC error"}, "ServerErrorCode8": {"-value": "Internal RPC return value error"}, "ServerErrorCode17": {"-value": "Unable to create more room"}, "ServerErrorCode18": {"-value": "Created too many rooms"}, "ServerErrorCode19": {"-value": "Invalid parameters"}, "ServerErrorCode20": {"-value": "Unable to pay, please recharge"}, "ServerErrorCode21": {"-value": "Validation failure occurs"}, "ServerErrorCode22": {"-value": "The room has been dismissed"}, "ServerErrorCode23": {"-value": "Only the owner can dismiss the room"}, "ServerErrorCode24": {"-value": "Room full"}, "ServerErrorCode25": {"-value": "You are already in the room"}, "ServerErrorCode26": {"-value": "The player is not in the room"}, "ServerErrorCode27": {"-value": "The position has been taken"}, "ServerErrorCode28": {"-value": "Coins required if you want to sit down"}, "ServerErrorCode29": {"-value": "Table full"}, "ServerErrorCode30": {"-value": "Player seated"}, "ServerErrorCode31": {"-value": "Unable to sit during game"}, "ServerErrorCode32": {"-value": "Not enough coins"}, "ServerErrorCode33": {"-value": "Unable to sit randomly"}, "ServerErrorCode34": {"-value": "Unable to sit randomly2"}, "ServerErrorCode35": {"-value": "Position occupied"}, "ServerErrorCode36": {"-value": "Unable to be seated"}, "ServerErrorCode37": {"-value": "Unable to be seated2"}, "ServerErrorCode38": {"-value": "Unable to stand up and spectate"}, "ServerErrorCode39": {"-value": "The number of gold coins has reached the Maximum Limit."}, "ServerErrorCode39_usdt": {"-value": "The number of USD has reached the Maximum Limit."}, "ServerErrorCode40": {"-value": "Unable to multi Buy In"}, "ServerErrorCode41": {"-value": "Only the owner can respond"}, "ServerErrorCode42": {"-value": "Unable to pay the recording fee,please recharge"}, "ServerErrorCode43": {"-value": "Buy-in application timeout"}, "ServerErrorCode44": {"-value": "Invalid buy-in amount approval"}, "ServerErrorCode45": {"-value": "Only the owner can start the game"}, "ServerErrorCode46": {"-value": "Game is already started."}, "ServerErrorCode47": {"-value": "Not enough players to start the game."}, "ServerErrorCode48": {"-value": "Not your turn yet"}, "ServerErrorCode49": {"-value": "Incorrect bet amount"}, "ServerErrorCode50": {"-value": "Illegal action"}, "ServerErrorCode51": {"-value": "Not your turn yet"}, "ServerErrorCode52": {"-value": "Configuration file error"}, "ServerErrorCode53": {"-value": "Insufficient funds, please recharge"}, "ServerErrorCode54": {"-value": "Only seated player can chat with owner"}, "ServerErrorCode55": {"-value": "Invalid player ID to buy insurance"}, "ServerErrorCode56": {"-value": "Insurance purchase request time out"}, "ServerErrorCode57": {"-value": "Unable to buy insurance in the current game"}, "ServerErrorCode58": {"-value": "Insurance purchased"}, "ServerErrorCode59": {"-value": "Unable to find pot ID"}, "ServerErrorCode60": {"-value": "Exceed available OUTS amount"}, "ServerErrorCode61": {"-value": "Purchase amount exceeds 1/3 amount of pot"}, "ServerErrorCode62": {"-value": "the pot is not enough to purchase"}, "ServerErrorCode63": {"-value": "Invalid Outs purchase"}, "ServerErrorCode64": {"-value": "Invalid Outs purchase2"}, "ServerErrorCode65": {"-value": "Action only available to seated players"}, "ServerErrorCode66": {"-value": "It is time to leave, your position will be preserved"}, "ServerErrorCode67": {"-value": "Application sent, waiting for the owner to approve. Expire in 180 seconds"}, "ServerErrorCode68": {"-value": "Not in seat now"}, "ServerErrorCode69": {"-value": "Already left the table while preserving your position"}, "ServerErrorCode70": {"-value": "You are not in the status of leaving while your position preserved"}, "ServerErrorCode71": {"-value": "Invalid player ID"}, "ServerErrorCode72": {"-value": "Unable to raise now, you can All in or Call"}, "ServerErrorCode73": {"-value": "Unable to connect to world server"}, "ServerErrorCode74": {"-value": "Only club administrators can create club rooms"}, "ServerErrorCode75": {"-value": "The amount of room created has reached the ceiling"}, "ServerErrorCode76": {"-value": "Other errors occur while creating rooms"}, "ServerErrorCode77": {"-value": "Illegal buy-in amount"}, "ServerErrorCode78": {"-value": "Last player"}, "ServerErrorCode79": {"-value": "Insurance need to be brought back"}, "ServerErrorCode80": {"-value": "Owner not found"}, "ServerErrorCode81": {"-value": "Incorrect Outs amount"}, "ServerErrorCode82": {"-value": "Invalid purchase amount"}, "ServerErrorCode83": {"-value": "Insurance is required"}, "ServerErrorCode84": {"-value": "Incorrect agreement"}, "ServerErrorCode85": {"-value": "Unable to check the rest of the cards"}, "ServerErrorCode86": {"-value": "Unable to check the rest of the cards"}, "ServerErrorCode87": {"-value": "Administrator Only"}, "ServerErrorCode88": {"-value": "Game not started"}, "ServerErrorCode89": {"-value": "The player is in the blacklist to sitdown"}, "ServerErrorCode90": {"-value": "Owner refused you to sit down "}, "ServerErrorCode91": {"-value": "The player is not in the blacklist to sitdown"}, "ServerErrorCode92": {"-value": "Current game not started yet."}, "ServerErrorCode93": {"-value": "Too much extra time used"}, "ServerErrorCode94": {"-value": "Unable to obtain alliance club"}, "ServerErrorCode95": {"-value": "Game in process"}, "ServerErrorCode96": {"-value": "Unable to withdraw coins"}, "ServerErrorCode97": {"-value": "You are prohibited from sitting down by anti-gang-cheating system"}, "ServerErrorCode98": {"-value": "Not enough gems to buy in"}, "ServerErrorCode99": {"-value": "Club buy-in amount over the limit of the alliance"}, "ServerErrorCode100": {"-value": "Club buy-in prohibited by alliance"}, "ServerErrorCode101": {"-value": "Forced showdown prohibited for now"}, "ServerErrorCode102": {"-value": "Forced ShowDown Count Has Reached Maximum Limit"}, "ServerErrorCode103": {"-value": "the number of times has been used up"}, "ServerErrorCode104": {"-value": "Game Server Is Under Maintenance"}, "ServerErrorCode105": {"-value": "Because Your Account Has Been Settled From This Table"}, "ServerErrorCode106": {"-value": "You Have Launched <PERSON><PERSON> Account, You Are About To Leave The Table After This Round."}, "ServerErrorCode107": {"-value": "Current game is settled, please do not re-submit request"}, "ServerErrorCode108": {"-value": "You Have Not Buyin Current Game, No Need To Settle Account."}, "ServerErrorCode109": {"-value": "You Cannot Set Both Password At The Same Time"}, "ServerErrorCode110": {"-value": "Incorrect Password"}, "ServerErrorCode111": {"-value": "Player limit has been reached"}, "ServerErrorCode113": {"-value": "Player all in,  all mute..."}, "ServerErrorCode114": {"-value": "Balance is insufficient"}, "ServerErrorCode117": {"-value": "Unable to create club"}, "ServerErrorCode118": {"-value": "Unable to create more club"}, "ServerErrorCode119": {"-value": "Parameter error occurs"}, "ServerErrorCode120": {"-value": "Incorrect type of club"}, "ServerErrorCode121": {"-value": "Unable to find club ID"}, "ServerErrorCode122": {"-value": "Only administrator can dismiss the club"}, "ServerErrorCode123": {"-value": "Club full, unable to join in"}, "ServerErrorCode124": {"-value": "You are already in the club"}, "ServerErrorCode125": {"-value": "You have already applied to join this club"}, "ServerErrorCode126": {"-value": "The player is not in the club"}, "ServerErrorCode127": {"-value": "Club fund not cleared yet"}, "ServerErrorCode128": {"-value": "Club member not cleared yet"}, "ServerErrorCode129": {"-value": "Club administrator full"}, "ServerErrorCode130": {"-value": "Not enough gems, please recharge"}, "ServerErrorCode131": {"-value": "Useless club star ranking"}, "ServerErrorCode132": {"-value": "Unable to obtain club price"}, "ServerErrorCode133": {"-value": "Unable to purchase star club"}, "ServerErrorCode134": {"-value": "Community not found"}, "ServerErrorCode135": {"-value": "Authorization failure!"}, "ServerErrorCode136": {"-value": "Error"}, "ServerErrorCode137": {"-value": "Club name already exists"}, "ServerErrorCode138": {"-value": "Other administrators are already on the move"}, "ServerErrorCode139": {"-value": "Other administrators are already on the move"}, "ServerErrorCode149": {"-value": "Unable to create more alliance"}, "ServerErrorCode150": {"-value": "Alliance name exists"}, "ServerErrorCode151": {"-value": "Unable to create alliance"}, "ServerErrorCode152": {"-value": "Alliance member not cleared yet"}, "ServerErrorCode153": {"-value": "Fail to find alliance"}, "ServerErrorCode154": {"-value": "Alliance permission denied"}, "ServerErrorCode155": {"-value": "The club is not in the alliance"}, "ServerErrorCode156": {"-value": "Unable to find the alliance"}, "ServerErrorCode157": {"-value": "The club is already in the alliance"}, "ServerErrorCode158": {"-value": "Application sent"}, "ServerErrorCode159": {"-value": "Alliance full"}, "ServerErrorCode160": {"-value": "Other administrators are already on the move"}, "ServerErrorCode161": {"-value": "Other error occurs"}, "ServerErrorCode162": {"-value": "Server <PERSON><PERSON>rse failure"}, "ServerErrorCode163": {"-value": "Database failed to save"}, "ServerErrorCode164": {"-value": "Unable to join more alliance"}, "ServerErrorCode165": {"-value": "Unable to set club JackPot bonus rate"}, "ServerErrorCode166": {"-value": "Unable to obtain club JackPot bonus rate"}, "ServerErrorCode167": {"-value": "Game in process, unable to remove club from alliance"}, "ServerErrorCode168": {"-value": "Game in process, unable to set <PERSON><PERSON>ot"}, "ServerErrorCode169": {"-value": "Unable to dismiss the club when game is on"}, "ServerErrorCode170": {"-value": "Please dismiss the previous alliance created"}, "ServerErrorCode171": {"-value": "Please dismiss the previous alliance created"}, "ServerErrorCode172": {"-value": "Not enough coins"}, "ServerErrorCode173": {"-value": "Fail To Obtain Player's System Mail Time Stamp"}, "ServerErrorCode174": {"-value": "Fail To Obtain Player's Mail List"}, "ServerErrorCode175": {"-value": "Request Index Is Illegal"}, "ServerErrorCode176": {"-value": "Join the club to reach the Maximum Limit"}, "ServerErrorCode177": {"-value": "Unable to dismiss the club when game is on"}, "ServerErrorCode179": {"-value": "Fail To Obtain Player's Anounce List"}, "ServerErrorCode180": {"-value": "Mail Content Is Not Qualified."}, "ServerErrorCode181": {"-value": "Please Quit Previously Joined Union"}, "ServerErrorCode182": {"-value": "Club Member Is Not Found"}, "ServerErrorCode187": {"-value": "Because There Is Union Game Undergoing, Club Application For Quit Union Is Prohibited"}, "ServerErrorCode190": {"-value": "Mobile Or Area Code Should Not Be Empty"}, "ServerErrorCode191": {"-value": "Incorrect Mobile Format"}, "ServerErrorCode192": {"-value": "Incorrect EMail Address"}, "ServerErrorCode193": {"-value": "You Cannot Quit Club Because Jackpot Is Not Empty"}, "ServerErrorCode194": {"-value": "Club Cannot Be Disband Because Club Balance Is Negtive"}, "ServerErrorCode195": {"-value": "Fail To Set Personal Percent,Permission Required"}, "ServerErrorCode196": {"-value": "Fail To Set Personal Percent"}, "ServerErrorCode197": {"-value": "Your Account Has Been Banned，If You Have Any Questions, Please Contact Customer Service"}, "ServerErrorCode198": {"-value": "Fail To Set Personal Percent, Please Do Not Repeat Setting"}, "ServerErrorCode199": {"-value": "Setting Remarks Have Reach Maximum Limit"}, "ServerErrorCode200": {"-value": "Are you sure to retrieve account password?"}, "ServerErrorCode201": {"-value": "Please switch to login with game ID ,%sIf you forget your password, please contact with customer service。"}, "ServerErrorCode205": {"-value": "Your safe has insufficient coins"}, "ServerErrorCode207": {"-value": "Cannot Find User Record."}, "ServerErrorCode208": {"-value": "Cannot Find Lottery Winning Record."}, "ServerErrorCode209": {"-value": "Incorrect Secondary Password"}, "ServerErrorCode210": {"-value": "Drawing Lottery Failed"}, "ServerErrorCode211": {"-value": "Data Request Failed"}, "ServerErrorCode212": {"-value": "RedBag Event Has Been Closed"}, "ServerErrorCode213": {"-value": "RedBag Level Has Not Been Set"}, "ServerErrorCode214": {"-value": "RedBag Amount Is Not In Allowed Amount Range"}, "ServerErrorCode215": {"-value": "Gold Operation Failed"}, "ServerErrorCode216": {"-value": "Sending RedBag Failed"}, "ServerErrorCode217": {"-value": "All Red Bags Have Been Drawed"}, "ServerErrorCode219": {"-value": "RedBag Does Not Exist"}, "ServerErrorCode221": {"-value": "RedBag Has Expired"}, "ServerErrorCode222": {"-value": "Grabbing RedBag Too Often"}, "ServerErrorCode223": {"-value": "Failed to fetch the rank."}, "ServerErrorCode224": {"-value": "Your network is unstable, please login again."}, "ServerErrorCode225": {"-value": "System error"}, "ServerErrorCode226": {"-value": "In system maintenance, please log in again."}, "ServerErrorCode228": {"-value": "Secondary Password not set."}, "ServerErrorCode229": {"-value": "Please log in again"}, "ServerErrorCode230": {"-value": "Application Cancelled"}, "ServerErrorCode232": {"-value": "The community has changed this month"}, "ServerErrorCode233": {"-value": "Community creation is currently not allowed"}, "ServerErrorCode234": {"-value": "Current player cannot join other communities"}, "ServerErrorCode235": {"-value": "Current community players cannot join other communities"}, "ServerErrorCode238": {"-value": "Unable to modify the first community, please contact customer service"}, "ServerErrorCode251": {"-value": "Insufficient amount of USD"}, "ServerErrorCode252": {"-value": "The exchange is abnormal, please try again later"}, "ServerErrorCode253": {"-value": "The exchange limit is 20USD～100W USD"}, "ServerErrorCode254": {"-value": "Your safe has insufficient USD"}, "ServerErrorCode255": {"-value": "The room opens on time"}, "ServerErrorCode256": {"-value": "The room has been dismissed"}, "ServerErrorCode257": {"-value": "Please wait for %s minutes before the next (gold coins to USD) exchange"}, "ServerErrorCode280": {"-value": "Booster does not exist"}, "ServerErrorCode281": {"-value": "Booster expired"}, "ServerErrorCode282": {"-value": "Maximum booster received"}, "ServerErrorCode283": {"-value": "Already received a booster from the player"}, "ServerErrorCode284": {"-value": "Not enough boosters to open red packet"}, "ServerErrorCode285": {"-value": "Invalid Recipient"}, "ServerErrorCode286": {"-value": "Daily booster limit reached"}, "ServerErrorCode287": {"-value": "Can't help myself"}, "ServerErrorCode288": {"-value": "Please register a game account first"}, "ServerErrorCode291": {"-value": "You have used up this week's boosters"}, "ServerErrorCode292": {"-value": "Only accounts created for more than %s days are qualified"}, "ServerErrorCode293": {"-value": "Only deposited users can send boosters"}, "ServerErrorCode501": {"-value": "Service is not available in your region."}, "ServerErrorCode502": {"-value": " Cannot passed advanced IP/GPS check."}, "ServerErrorCode503": {"-value": "Can't sit down, please try another table"}, "ServerErrorCode504": {"-value": "Failed of Sit Out, this feature has reached today's usage limit"}, "ServerErrorCode505": {"-value": "GAMEUUID does not exist"}, "ServerErrorCode506": {"-value": "You will exit this game after the conclusion of this hand"}, "ServerErrorCode508": {"-value": "Special invited players will be on the table"}, "ServerErrorCode509": {"-value": "Do not support the narrator on the table"}, "ServerErrorCode512": {"-value": "Re-enter the room in %s"}, "ServerErrorCode513": {"-value": "Re-enter your seat in %s"}, "ServerErrorCode515": {"-value": "Buy-in has to be more than the chips you exited with, please add chips and try again."}, "ServerErrorCode1002": {"-value": "This room is about to be disbanded"}, "ServerErrorCode1003": {"-value": "Server is busy"}, "ServerErrorCode1201": {"-value": "You Will Leave This Room After This Hand."}, "ServerErrorCode1204": {"-value": "Exceeding Buyin Upper Limit of This Table, So Temporarily You Do Not Need To Buyin."}, "ServerErrorCode1206": {"-value": "Operation Failed, Please Try It Again"}, "ServerErrorCode1207": {"-value": "Guess the hand repeat bet"}, "ServerErrorCode1208": {"-value": "Guess hand bet timeout"}, "ServerErrorCode1209": {"-value": "Insufficient balance, guessing hand bet failed"}, "ServerErrorCode1210": {"-value": "Guess hand setting failed to vote"}, "ServerErrorCode1211": {"-value": "Guess hand bet option is invalid"}, "ServerErrorCode1212": {"-value": "Guess hand unknown error"}, "ServerErrorCode1213": {"-value": "Service is not available in your region"}, "ServerErrorCode1216": {"-value": "Exceeded <PERSON><PERSON><PERSON>.  Please try again later"}, "ServerErrorCode1249": {"-value": "New exception-Vietnamese players cannot enter"}, "ServerErrorCode1250": {"-value": "Table viewing limit has expired，Please try another table"}, "ServerErrorCode1251": {"-value": "No matching room found"}, "ServerErrorCode1252": {"-value": "Approved"}, "ServerErrorCode1254": {"-value": "This seat is reserved and will be released 5 - 10 minutes before the game"}, "ServerErrorCode1255": {"-value": "This seat is reserved, Star table will begin in %s"}, "ServerErrorCode1256": {"-value": "Special guests occupy the seat, If after %s the special guests is not online, Will be released"}, "ServerErrorCode1257": {"-value": "Gifting failed, player not a star member"}, "ServerErrorCode1258": {"-value": "Incorrect gift ID or quantity"}, "ServerErrorCode1262": {"-value": "Star player not seated, please send gifts later"}, "ServerErrorCode1263": {"-value": "Cannot gift yourself"}, "ServerErrorCode1302": {"-value": "Sorry, there is no suitable table at the level."}, "ServerErrorCode31121": {"-value": "Reserved seat, please choose another table."}, "ServerErrorCode31123": {"-value": "Game Server Is Under Maintenance"}, "SitDownErrorToast1": {"-value": "Same name in the room. Please change your name to sit down!"}, "ServerErrorCode1006": {"-value": "Unable to pay the recording fee,please recharge"}, "ServerErrorCode1274": {"-value": "Feature disabled by admin"}, "ServerErrorCode1276": {"-value": "Content too long"}, "ServerErrorCode1277": {"-value": "Seated players cannot send bullet comments"}, "ServerErrorCode1278": {"-value": "Commentators cannot send bullet comments"}, "ServerErrorCode1279": {"-value": "Bullet comment disabled"}, "ServerErrorCode1301": {"-value": "Hand limit or Sit down limit"}}