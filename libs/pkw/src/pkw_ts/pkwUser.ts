import { UserStatus } from "shared";

export enum UserActionEnum {
    default = 0,
    login,
    requestLobbyData,
    joinRoom,
    sitDown,
    buyin,
    rebuy,
    sitBack,
    buyOut,
    sitOut,
    leaveRoom,
    checkCurrentRoom,
}


export enum GameActionEnum {
    default = 0,
    check,
    fold,
    allin,
    bet,
    call,
}

export class PkwUser {
    pkwUid: number = -1;
    currentRoomId: number = -1;
    currentStatus: UserStatus = UserStatus.default;
    userActions: UserActionEnum[] = [];
    gameActions: GameActionEnum[] = [];
    constructor(pkwUid: number, userActions: UserActionEnum[], gameActions: GameActionEnum[]) {
        this.pkwUid = pkwUid;
        this.userActions = userActions;
        this.gameActions = gameActions;
    }

    // Add actions uniquely to the front of the queue
    public prependUserActions(...actions: UserActionEnum[]): void {
        const resultActions = [];
        for (let i = 0, j = 0; i < actions.length || j < this.userActions.length; ) {
            if (i < actions.length && j < this.userActions.length) {
                if (actions[i] === this.userActions[j]) {
                    resultActions.push(actions[i]);
                    i++;
                    j++;
                    continue;
                }
            }

            if (i < actions.length) {
                resultActions.push(actions[i]);
                i++;
                continue;
            }

            if (j < this.userActions.length) {
                resultActions.push(this.userActions[j]);
                j++;
            }
        }
        this.userActions = resultActions;
    }
}
