import { <PERSON><PERSON>and<PERSON> } from './data/DataHandler';
import { RoomManager } from './data/RoomManager';
import { DataNetWork } from './network/DataNetWork';
import { GameNetWork } from './network/GameNetWork';
import { HTTP } from './network/HTTP';
import { HttpHandler } from './network/HttpHandler';

import { NetWorkManager } from './network/NetWorkManager';
import { NetWork } from './network/NetWork';
import { WorldNetWork } from './network/WorldNetWork';

import { Config } from './tools/Config';

import * as Enums from './tools/Enum';
import * as ByteBuffer from './tools/bytebuffer';

import { MessageCenter } from './tools/MessageCenter';
import { NativeEvent } from './tools/NativeEvent';
import { Tools } from './tools/Tools';
import { logging } from 'shared';

import { RoomData } from './data/RoomData';

class cv {
    public gamePB: any;
    public worldPB: any;
    public dataPB: any;
    public gatePB: any;

    public native: NativeEvent;
    public tools: Tools;
    public http: HTTP;
    public httpHandler: HttpHandler;

    public Enum = Enums;
    public dataHandler: DataHandler;
    public MessageCenter: MessageCenter;
    public config: Config;

    //net works
    public netWork: NetWork;
    public worldNet: WorldNetWork;
    public gameNet: GameNetWork;
    public dataNet: DataNetWork;
    public roomManager: RoomManager;
    public netWorkManager: NetWorkManager;
    public GameDataManager = {
        tRoomData: new RoomData(),
    };

    public ByteBuffer: any;

    public initCV(proxyUrl?: string) {
        this.ByteBuffer = ByteBuffer;

        this.httpHandler = HttpHandler.getInstance();

        this.http = HTTP.getInstance();
        this.tools = Tools.getInstance();
        this.dataHandler = DataHandler.getInstance();
        this.netWork = NetWork.getInstance(proxyUrl);
        this.MessageCenter = MessageCenter.getInstance();
        this.config = Config.getInstance();
        this.netWorkManager = NetWorkManager.getInstance();
        this.roomManager = RoomManager.getInstance();
        this.native = NativeEvent.getInstance();
        this.worldNet = WorldNetWork.getInstance();
        this.gameNet = GameNetWork.getInstance();
        this.dataNet = DataNetWork.getInstance();
    }

    public ToastError(i32Error: number) {
        logging
            .withTag('SDK')
            .warn(
                "[SDK] ToastError, check 'ServerErrorCode" +
                    i32Error +
                    '` msg from  ServerErrorCodeTable.json',
            );
    }

    public Number(value: any): number {
        value = Number(value);
        value = isFinite(value) ? value : 0;
        return value;
    }
    public String(value: any): string {
        if (value === null || typeof value === 'undefined') value = '';
        else value = String(value);

        return value;
    }
    private static g_instance: cv;
    public static getInstance(): cv {
        if (!cv.g_instance) {
            cv.g_instance = new cv();
        }
        return cv.g_instance;
    }
}

let cv_instance: cv;
export default cv_instance = cv.getInstance();
