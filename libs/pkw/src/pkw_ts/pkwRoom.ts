import cv from './cv';
import ws_protocol from '../proto/ws_protocol';
import gs_protocol from '../proto/gs_protocol';
import { GameActionEnum, PkwUser, UserActionEnum } from './pkwUser';
import { CreateGameMode } from './tools/Enum';
import pkwGame from './pkwGame';
import { toTableData } from './lobby';
import { GameType, JobType, logging, LoginError, MttUserData, RoomStartParams, TableData, UserStatus } from 'shared';
import { UnrecoverableError } from 'bullmq';
import * as ServerErrorCodes from '../commoms/SeverErrorCodeTable.json';
import world_pb = ws_protocol.pb;
import game_pb = gs_protocol.protocol;

export type LobbyDataFunction = (data: TableData[]) => void;

export class pkwRoom {
    private _roomList: world_pb.IClubGameSnapshotV3[] = null;
    private _pkwUser: PkwUser;

    private _buyInAmount: number = 0;
    private _buyInMultiplier: number = 100;
    private _rebuyThreshold: number = null;
    private _rebuyEnabled: boolean = false;
    private _withdrawAmount: number = 0;
    private _withdrawThreshold: number = 0;
    maxRebuyCount: number = 100;

    private _handsPlayed: number = 0;
    private _rebuyCount: number = 0;
    private _totalBuyIn: number = 0;
    private _requestBuyInAmount: number = 0;

    private _roomSt: world_pb.IClubGameSnapshotV3 = null;
    private _gameSt: game_pb.NoticeGameSnapshot = null;

    private _loginServerCB: (data: Partial<MttUserData>) => void = null;
    private _lobbyDataCb: LobbyDataFunction = () => {};
    private _successCb: Function = () => {};
    private _errorCb: Function = () => {};
    private _leaveRoomCb: Function = () => {};
    private _joinRoomId: number = 0;

    private _leaveRoomAttempts: number = 0;
    private _currentError: any = null;
    private _gameId: world_pb.GameId = null;
    private _gameType: CreateGameMode = null;
    private _buyInIsProcessing: boolean = false;
    private _isSatDown: boolean = false;
    private _isBoughtIn: boolean = false;
    private _isRebuying: boolean = false;
    private _rebuyWasMade = false;

    public init() {
        this._regMsg();
    }

    public getWithdrawAmount() {
        return this._withdrawAmount;
    }

    public getWithdrawThreshold() {
        return this._withdrawThreshold;
    }

    public getRebuyWasMade() {
        return this._rebuyWasMade;
    }

    public getBuyInAmount() {
        return this._buyInAmount;
    }

    public getRequestBuyInAmount() {
        return this._requestBuyInAmount;
    }

    public getRebuyThreshold() {
        return this._rebuyThreshold;
    }

    public getRebuyEnabled() {
        return this._rebuyEnabled;
    }

    public getRebuyCount() {
        return this._rebuyCount;
    }

    public getTotalBuyIn() {
        return this._totalBuyIn;
    }

    public setTotalBuyIn(totalBuyIn: number) {
        this._totalBuyIn = totalBuyIn;
    }

    public getHandsPlayed() {
        return this._handsPlayed;
    }

    public setHandsPlayed(handsPlayed: number) {
        this._handsPlayed = handsPlayed;
    }

    public setRebuyWasMade(value: boolean) {
        logging.withTag('BUYIN').info(`[InGame] _rebuyWasMade changed:  ${this._rebuyWasMade} --> ${value}`);
        this._rebuyWasMade = value;
    }

    public setBuyInAmount(buyInAmount: number) {
        logging
            .withTag('BUYIN')
            .info(`[InGame] _buyInAmount changed:  ${this._buyInAmount} --> ${buyInAmount}`);
        this._buyInAmount = buyInAmount;
    }

    public setGameId(gameId: world_pb.GameId) {
        this._gameId = gameId;
    }

    public setGameTypeCode(gameTypeCode: GameType) {
        if (gameTypeCode == GameType.SHORTDECK) {
            this._gameType = CreateGameMode.CreateGame_Mode_Short;
        } else {
            this._gameType = CreateGameMode.CreateGame_Mode_Normal;
        }
    }

    public setPkwUserData(pkwUid: number, jobType: JobType) {
        const userActionsConfig = {
            [JobType.SCAN]: [UserActionEnum.login, UserActionEnum.requestLobbyData],
            [JobType.PLAY]: [
                UserActionEnum.login,
                UserActionEnum.requestLobbyData,
                UserActionEnum.checkCurrentRoom,
                UserActionEnum.leaveRoom,
                UserActionEnum.joinRoom,
                UserActionEnum.sitDown,
                UserActionEnum.buyin,
            ],
            [JobType.LOGIN]: [UserActionEnum.login],
        };
        this._pkwUser = new PkwUser(pkwUid, userActionsConfig[jobType], [GameActionEnum.default]);
    }

    public setJoinRoomId(joinRoomId: number) {
        this._joinRoomId = joinRoomId;
    }

    public setRoomParams(roomParams: RoomStartParams) {
        this._buyInMultiplier = roomParams.buyInMultiplier;
        this._rebuyThreshold = roomParams.rebuyThreshold;
        this._rebuyEnabled = roomParams.rebuyEnabled;
        this._withdrawAmount = roomParams.withdrawAmount;
        this._withdrawThreshold = roomParams.withdrawThreshold;
        this.maxRebuyCount = roomParams.maxRebuyCount;
    }

    public setLoginServerCB(loginServerCB: (data: MttUserData) => void) {
        this._loginServerCB = loginServerCB;
    }

    public setLobbyDataCb(lobbyDataCb: LobbyDataFunction) {
        this._lobbyDataCb = lobbyDataCb;
    }

    public setErrorCb(errorCb: Function) {
        this._errorCb = errorCb;
    }
    public setSuccessCb(successCb: Function) {
        this._successCb = successCb;
    }

    public stopGame() {
        this._successCb();
    }

    public async leaveRoom() {
        if (cv.roomManager.getCurrentRoomID() == 0) {
            logging.withTag('LEAVE_ROOM').info('[SDK] leaveRoom - current room id is 0, finishing job now');
            return;
        } else {
            logging
                .withTag('LEAVE_ROOM')
                .info('[SDK] leaveRoom called - current status: ' + this._pkwUser.currentStatus);
            return new Promise((resolve) => {
                this._leaveRoomCb = resolve;
                this._requestLeaveRoom();
            });
        }
    }

    public requestWithdraw() {
        this._pkwUser.prependUserActions(UserActionEnum.buyOut);
        this._doNextUserAction();
    }

    private _regMsg() {
        cv.MessageCenter.register('onLoginServerError', this._onLoginServerError.bind(this), this);
        cv.MessageCenter.register('onGetTables', this._onGetTables.bind(this), this);

        cv.MessageCenter.register('onJoinRoomResp', this._onJoinRoomResp.bind(this), this);
        cv.MessageCenter.register('roomDismissed', this._onRoomDismissed.bind(this), this);

        cv.MessageCenter.register('sitDownDone', this._onSitDown.bind(this), this);
        cv.MessageCenter.register('sitDownUncaughtError', this._onSitDownUncaughtError.bind(this), this);
        cv.MessageCenter.register('alreadySitDown', this._onAlreadySitDown.bind(this), this);
        cv.MessageCenter.register('seatOccupied', this._onSeatOccupied.bind(this), this);
        cv.MessageCenter.register('tableFull', this._onTableFull.bind(this), this);
        cv.MessageCenter.register('needBuyin', this._needBuyInAfterSitDownAttempt.bind(this), this);
        cv.MessageCenter.register('verificationNeeded', this._onVerificationNeeded.bind(this), this);

        cv.MessageCenter.register('onBoughtIn', this._onBoughtIn.bind(this), this);
        cv.MessageCenter.register('onNoticeBuyin', this._onNoticeBuyin.bind(this), this);
        cv.MessageCenter.register('onBuyInFailed', this._onBuyInFailed.bind(this), this);

        cv.MessageCenter.register('onLeaveSeat', this._OnLeaveSeat.bind(this), this);
        cv.MessageCenter.register('onBackSeatSuccess', this._onBackSeatSuccess.bind(this), this);
        cv.MessageCenter.register('onBackSeatNeedBuyIn', this._onBackSeatNeedBuyIn.bind(this), this);
        cv.MessageCenter.register('onBackSeatNoBuyIn', this._onBackSeatNoBuyIn.bind(this), this);
        cv.MessageCenter.register('onBackSeatFailed', this._onBackSeatFailed.bind(this), this);

        cv.MessageCenter.register('onStandupResp', this._onStandupResp.bind(this), this);
        cv.MessageCenter.register('onNoticeGameSnapShot', this._onNoticeGameSnapShot.bind(this), this);

        cv.MessageCenter.register('leaveRoomSuccess', this._onLeaveRoomSuccess.bind(this), this);
        cv.MessageCenter.register('leaveRoomFailed', this._onLeaveRoomFailed.bind(this), this);
        cv.MessageCenter.register('quick_leave_notice', this._onLeaveRoomSuccess.bind(this), this);

        cv.MessageCenter.register('onLoggedInWorldServer', this._onLoggedInWorldServer.bind(this), this);
        cv.MessageCenter.register('onNoticeLoginServer', this._onNoticeLoginServer.bind(this), this);

        cv.MessageCenter.register('on_standup_succ', this.onNoticeStandup.bind(this), this);
        cv.MessageCenter.register('on_back_seat', this._onBackSeat.bind(this), this);
    }

    private _onBackSeat(msg: game_pb.NoticeBackPosition) {
        logging.withTag('PKW_GAME').info('[SDK] on_back_seat', {
            payload: msg,
        });
    }

    private onNoticeStandup(msg: game_pb.INoticeStandup) {
        if (msg.target_uid !== pkwGame.getSelfUid()) {
            return;
        }

        this._gameSt.roomid = cv.roomManager.getCurrentRoomID();
        logging.withTag('PKW_GAME').info('[SDK] onNoticeStandup, requesting join room again', {
            joinRoomId: this._joinRoomId,
            currentRoom: cv.roomManager.getCurrentRoomID(),
            payload: msg,
        });
        this._pkwUser.prependUserActions(UserActionEnum.sitBack);
        this._doNextUserAction();
    }

    private _onLoginServerError(error: number) {
        const errorString = ServerErrorCodes['ServerErrorCode' + error]?.['-value'] ?? error;
        this._errorCb(new LoginError(errorString));
    }

    private _onSitDownUncaughtError(resp: game_pb.ResponseSitDown) {
        logging.withTag('SDK').info('[SDK] _onSitDownUncaughtError, errorcode:' + resp.error);
        if (this._isSatDown && this._isBoughtIn) return;
        this._currentError = resp.error;
        this._requestLeaveRoom();
    }

    private _onVerificationNeeded() {
        logging.withTag('SDK').info('[SDK] _onVerificationNeeded');
        cv.worldNet.requestAuthVerify(0);
        if (this._pkwUser.currentStatus != UserStatus.satDown) {
            logging.withTag('SDK').info('[SDK] _onVerificationNeeded, request sit down');
            setTimeout(() => {
                this._requestSitDown();
            }, 1000);
        }
    }

    private _onRoomDismissed() {
        this._currentError = new UnrecoverableError('The room has been dismissed');
        this._requestLeaveRoom();
    }

    private _onTableFull() {
        if (this._isSatDown && this._isBoughtIn) return;
        this._currentError = new UnrecoverableError('The table is full');
        this._requestLeaveRoom();
    }

    private _needBuyInAfterSitDownAttempt() {
        this._onUserActionDone(UserActionEnum.sitDown);
        this._pkwUser.prependUserActions(UserActionEnum.buyin, UserActionEnum.sitDown);
        this._doNextUserAction(true);
    }

    private _OnLeaveSeat(playerId: any) {
        logging
            .withTag('SDK')
            .info('[InGame] OnLeaveSeat:' + Number(playerId) + '. Self uid:', pkwGame.getSelfUid());

        if (Number(playerId) === pkwGame.getSelfUid()) {
            this.setUserStatus(UserStatus.satOut);
            pkwGame.updateStatus(UserStatus.satOut);
            // Calculate the time to sit back
            const waitTime = Math.floor(Math.random() * 8) + 3;
            logging.withTag('SDK').info('[InGame] Wait time to sit back: ' + waitTime + 's');
            setTimeout(() => {
                this._pkwUser.prependUserActions(UserActionEnum.sitBack);
                this._doNextUserAction();
            }, waitTime * 1000);
        }
    }

    private _onBackSeatSuccess() {
        logging.withTag('SDK').info('[SDK] _onBackSeatSuccess');
        this._onUserActionDone(UserActionEnum.sitBack);
        this.setUserStatus(UserStatus.satDown);
        pkwGame.updateStatus(UserStatus.satDown);
        this._doNextUserAction();
    }

    private _onBackSeatNeedBuyIn() {
        logging.withTag('SDK').info('[SDK] _onBackSeatNeedBuyIn');
        this._isBoughtIn = false;
        if (!pkwGame.leaveRoomIfRebuyLimitReached(true)) {
            this._pkwUser.prependUserActions(UserActionEnum.rebuy, UserActionEnum.sitBack);
            this._doNextUserAction();
        }
    }

    private _onBackSeatNoBuyIn() {
        // TODO: what is the error 66: It is time to leave, your position will be preserved
        logging.withTag('SDK').info('[SDK] _onBackSeatNoBuyIn');
    }

    private _onBackSeatFailed(error: number) {
        logging.withTag('SDK').info('[SDK] _onBackSeatFailed, errorcode:' + error);
        this._currentError = new UnrecoverableError(
            ServerErrorCodes['ServerErrorCode' + error]?.['-value'] ?? error,
        );
        this._requestLeaveRoom();
    }

    private _onBuyInFailed(error: number) {
        logging.withTag('SDK').info('[SDK] _onBuyInFailed, errorcode:' + error);
        const unrecoverableErrorMessages: Record<number, string> = {
            39: 'The number of gold coins has reached the Maximum Limit.',
            114: 'Balance insufficient',
            515: 'Buy-in has to be more than the chips you exited with, please add chips and try again.',
            1301: 'Hand limit or Sit down limit',
            1006: 'Unable to pay the recording fee, please recharge',
        };
        this._currentError = unrecoverableErrorMessages[error]
            ? new UnrecoverableError(unrecoverableErrorMessages[error])
            : error;

        this._requestLeaveRoom();
    }

    private _onLoggedInWorldServer(msg: ws_protocol.pb.IResponseLogon) {
        logging.withTag('SDK').info('[SDK] on logged in world server', msg);
        //   cv.gameNet.requestLoginServer();
        this.setUserStatus(UserStatus.loggedOn);
        pkwGame.updateStatus(this._pkwUser.currentStatus);
        if (this._loginServerCB) {
            this._loginServerCB({ mtt: { token: msg?.mttData?.token } });
        }
    }

    private _onNoticeLoginServer() {
        logging.withTag('SDK').info('[SDK] on notice login server');

        this._onUserActionDone(UserActionEnum.login);
        // this._doNextUserAction();
        // return;
        if (cv.roomManager.getCurrentRoomID()) {
            this.setUserStatus(UserStatus.loggedOnJoinRoom);
            this._pkwUser.prependUserActions(UserActionEnum.joinRoom);
            cv.roomManager.RequestJoinRoom(
                cv.roomManager.getCurrentGameID(),
                cv.roomManager.getCurrentRoomID(),
            );
        } else {
            this._doNextUserAction();
        }
        //if(cv.roomManager.getCurrentRoomID() &&cv.roomManager.setCurrentGameID(logon.gameid);)
    }

    //-----------------------------------------------------------------//

    private _doNextUserAction(needBuyin: boolean = true) {
        const action = this._pkwUser.userActions[0];
        const actionName = this._getEnumName(UserActionEnum, action);
        logging.setLastActionTaken(actionName);
        logging.withTag('SDK').info(`[SDK] _doNextUserAction: ${actionName}, needBuyin: ${needBuyin}`);

        switch (action) {
            case UserActionEnum.requestLobbyData:
                cv.worldNet.requestCurrentBoardList();
                break;
            case UserActionEnum.joinRoom:
                this._requestJoinRoom(this._joinRoomId);
                break;
            case UserActionEnum.sitDown:
                this._asyncSitDown();
                break;
            case UserActionEnum.buyin:
                if (needBuyin && this._pkwUser.currentStatus != UserStatus.boughtIn) {
                    this._asyncBuyin();
                } else {
                    this._onBoughtIn();
                }
                break;
            case UserActionEnum.rebuy:
                this.rebuy();
                break;
            case UserActionEnum.buyOut:
                this._buyOut();
                break;
            case UserActionEnum.sitBack:
                cv.gameNet.RequestBackPosition(this._gameSt.roomid);
                break;
            case UserActionEnum.sitOut:
                cv.gameNet.RequestStandup(this._gameSt.roomid);
                break;
            case UserActionEnum.leaveRoom:
                this._requestLeaveRoom();
                break;
            case UserActionEnum.checkCurrentRoom:
                this._checkCurrentRoom();
                break;
            default:
                logging.withTag('SDK').info('[SDK] no more valid user action');
                break;
        }
    }

    /**
     * Check if the user is in the desired room, and skip redundant actions if so
     */
    private _checkCurrentRoom() {
        logging.withTag('SDK').info(`[SDK] _checkCurrentRoom`);

        this._onUserActionDone(UserActionEnum.checkCurrentRoom);
        if (
            this._pkwUser.currentStatus === UserStatus.inRoom &&
            cv.roomManager.getCurrentRoomID() == this._joinRoomId &&
            cv.roomManager.getCurrentGameID() == this._gameId
        ) {
            if (!this._roomList) {
                throw new Error('Incorrect state, room list is missing');
            }
            this._roomSt = this._roomList.find((r) => r.room_id === this._joinRoomId);
            if (!this._roomSt) {
                logging.withTag('SDK').warn(`[SDK] _checkCurrentRoom, room not found: ${this._joinRoomId}`);
                throw new Error('Room not found');
            }

            this._onUserActionDone(UserActionEnum.leaveRoom);
            this._onUserActionDone(UserActionEnum.joinRoom);

            let currentPlayer = this._gameSt.tstate?.players.find((p) => p.playerid == this._pkwUser.pkwUid);
            if (currentPlayer) {
                pkwGame.updateInfo(this._roomSt, this._gameSt);
                this._onUserActionDone(UserActionEnum.sitDown);
                this._onUserActionDone(UserActionEnum.buyin);
                if (currentPlayer.inStay) {
                    this._pkwUser.prependUserActions(UserActionEnum.sitBack);
                } else {
                    this.setUserStatus(UserStatus.boughtIn);
                }
            }
        }
        this._doNextUserAction();
    }

    private _buyOut() {
        // buy out amount, can get a reasonable amount by doing some calculation
        let buyOutAmount = this._withdrawAmount * this._roomSt.big_blind;
        logging.withTag('SDK').info(`[SDK] buy out amount: ${buyOutAmount}`);
        cv.gameNet.RequestBuyout(this._roomSt.room_id, buyOutAmount);
    }

    private _onUserActionDone(action: UserActionEnum) {
        if (this._pkwUser.userActions[0] == action) {
            logging.withTag('SDK').info('[Action]: ' + this._getEnumName(UserActionEnum, action) + ' done ');
            this._pkwUser.userActions.shift();
        } else {
            logging
                .withTag('SDK')
                .info(
                    'Done action: ' +
                        this._getEnumName(UserActionEnum, action) +
                        ', action in queue: ' +
                        this._getEnumName(UserActionEnum, this._pkwUser.userActions[0]),
                );
        }
    }

    private _onSeatOccupied(resp: game_pb.ResponseSitDown) {
        logging
            .withTag('SEAT_DOWN')
            .info(
                `_onSeatOccupied - resp:  ${JSON.stringify(resp)}; ${this._isSatDown}; ${this._isBoughtIn}`,
            );
        if (this._isSatDown && this._isBoughtIn) return;
        logging.withTag('SEAT_DOWN').info('_onSeatOccupied, looking for availabe seat');
        let nextAvaSeatId = this._getAvailableSeatId();
        if (nextAvaSeatId === -1) {
            logging.withTag('SEAT_DOWN').warn('_onSeatOccupied no available seats found');
            this._currentError = new UnrecoverableError('No available seats found');
            this._requestLeaveRoom();
            return;
        }
        logging.withTag('SEAT_DOWN').info('_onSeatOccupied requesting sitdown to seatid: ' + nextAvaSeatId);
        cv.gameNet.RequestSitdown(cv.roomManager.getCurrentRoomID(), nextAvaSeatId, false);
    }

    private _onJoinRoomResp(error: number) {
        if (this._pkwUser.currentStatus === UserStatus.loggedOnJoinRoom) {
            logging.withTag('SDK').info('[SDK] _onJoinRoomResp: loggedOnJoinRoom  ');

            this._onUserActionDone(UserActionEnum.joinRoom);
            this.setUserStatus(UserStatus.inRoom);
            pkwGame.updateStatus(this._pkwUser.currentStatus);
            this._doNextUserAction();
        } else {
            switch (error) {
                case 1:
                    {
                        // normal join room success
                        logging.withTag('SDK').info('[SDK] _onJoinRoomResp : normal join room success  ');
                        if (this._roomList) {
                            this._roomSt = this._roomList.find((r) => r.room_id === this._joinRoomId);
                        } else {
                            logging.warn("'[SDK] _onJoinRoomResp: room list is empty");
                        }

                        this._onUserActionDone(UserActionEnum.joinRoom);
                        this.setUserStatus(UserStatus.inRoom);
                        this._doNextUserAction();
                    }
                    break;
                case 1250:
                    {
                        // kicked by server cause exceed time limit, try other room
                        logging.withTag('SDK').error(' _onJoinRoomRespError, errorcode:' + error, error);
                        this.setUserStatus(UserStatus.viewingTimeExpired);
                        pkwGame.updateStatus(this._pkwUser.currentStatus);
                        this._errorCb(
                            new UnrecoverableError(
                                'Table viewing limit has expired，Please try another table',
                            ),
                        );
                    }
                    break;
                case 1260:
                    logging
                        .withTag('SDK')
                        .error(' _onJoinRoomRespError - verification, errorcode:' + error, error);
                    this._onVerificationNeededJoinRoom();
                    break;
                case 22:
                    {
                        logging.withTag('SDK').warn('[SDK] _onJoinRoomRespError, errorcode:' + error);
                        this._errorCb(new UnrecoverableError('The room has been dismissed'));
                    }
                    break;
                default:
                    {
                        logging.withTag('SDK').warn('[SDK] _onJoinRoomRespError, errorcode:' + error);
                        this._errorCb(error);
                    }
                    break;
            }
        }
    }

    private _onVerificationNeededJoinRoom() {
        logging
            .withTag('SDK')
            .info(`[SDK] _onVerificationNeededJoinRoom, status: ${this._pkwUser.currentStatus}`);
        cv.worldNet.requestAuthVerify(0);
        if (this._pkwUser.currentStatus != UserStatus.loggedOnJoinRoom) {
            logging.withTag('SDK').info('[SDK] _onVerificationNeededJoinRoom, request join room again');
            setTimeout(() => {
                this._requestJoinRoom(this._joinRoomId);
            }, 1000);
        }
    }

    private _onAlreadySitDown() {
        if (this._isSatDown && this._isBoughtIn) return;
        logging.withTag('SDK').info('[SDK] already sit down');
        this._onSitDown(false);
    }

    private _requestLeaveRoom() {
        logging.withTag('SDK').info('[SDK] _requestLeaveRoom roomId: ' + cv.roomManager.getCurrentRoomID());
        // TODO handle the case when user is already in desired room (no need to leave room)
        if (cv.roomManager.getCurrentRoomID() == 0) {
            logging.withTag('SDK').warn('[SDK] invalid room id or this user not in room');
            this._onUserActionDone(UserActionEnum.leaveRoom);
            this._doNextUserAction();
        } else {
            cv.roomManager.RequestLeaveRoom();
        }
    }

    private _onLeaveRoomSuccess() {
        this._onUserActionDone(UserActionEnum.leaveRoom);
        this.setUserStatus(UserStatus.inLobby);
        this._leaveRoomCb();
        if (this._currentError) {
            this._errorCb(this._currentError);
        } else {
            this._doNextUserAction();
        }
    }

    private _onLeaveRoomFailed(error: number) {
        if (error == 506) {
            logging.withTag('SDK').info('[SDK] You will exit this game after the conclusion of this hand');
            // do nothing, wait for the hand to finish
            return;
        }

        logging
            .withTag('SDK')
            .error(
                '[SDK] _onLeaveRoomFailed, errorcode:' + error,
                ServerErrorCodes['ServerErrorCode' + error]?.['-value'] ?? error,
            );

        this._leaveRoomAttempts++;
        if (this._leaveRoomAttempts < 3) {
            logging.withTag('SDK').info('[SDK] _onLeaveRoomFailed, trying to leave room again');
            this._requestLeaveRoom();
        } else {
            this._errorCb(
                new UnrecoverableError(ServerErrorCodes['ServerErrorCode' + error]?.['-value'] ?? error),
            );
        }
    }

    private _onGetTables(roomList: world_pb.IClubGameSnapshotV3[]) {
        this._roomList = roomList;

        logging.withTag('SDK').info(`[SDK] got Tables: roomList length: ${roomList.length}`);
        this._lobbyDataCb(toTableData(roomList));

        this._onUserActionDone(UserActionEnum.requestLobbyData);

        // if there is no user action, we just want to observe the lobby
        if (this._pkwUser.userActions.length == 0) {
            setTimeout(() => {
                this._pkwUser.prependUserActions(UserActionEnum.requestLobbyData);
                this._doNextUserAction();
            }, 10000);
        } else {
            this._doNextUserAction();
        }
    }

    //194813 : 50~500 0.2/0.5
    private _requestJoinRoom(roomId: number) {
        logging.withTag('SDK').info(`[SDK] _requestJoinRoom, roomId: ${roomId}`);
        if (
            cv.roomManager.getCurrentRoomID() == roomId &&
            cv.roomManager.getCurrentGameID() == this._gameId
        ) {
            console.info('[SDK] no need to leave room');
            this._onUserActionDone(UserActionEnum.joinRoom);
            this._doNextUserAction();
        } else if (roomId <= 0) {
            logging.error('[SDK] invalid room id: ', roomId);
            this._errorCb(new UnrecoverableError('Invalid room id'));
        } else {
            cv.roomManager.RequestJoinRoom(this._gameId, roomId);
        }
    }

    /**
     * tstate : table state, infos
     public tstate?: (protocol.ITableStates|null);
     * @param gameSt {
     players: [
     ],
     pots: [
     ],
     public_card: [
     ],
     curr_action_player_seatid: 0,
     curr_action_left_time: 0,
     curr_dealer_seatid: 0,
     curr_bb_seatid: 0,
     curr_sb_seatid: 0,
     curr_straddle_seatid: 0,
     bb_amount: 4,
     sb_amount: 2,
     }
     */
    // private _onJoinRoom(gameSt:game_pb.NoticeGameSnapshot) {
    //     console.log("[SDK] current room id:" + cv.roomManager.getCurrentRoomID())
    //     this._gameSt = gameSt;
    //     this._onUserActionDone(UserActionEnum.joinRoom);
    //     this._setUserStatus(UserStatus.inRoom);
    //     this._doNextUserAction();
    // }
    private gameStPromise: Promise<void>;
    private resolveGameStPromise: () => void;

    private _onNoticeGameSnapShot(gameSt: game_pb.NoticeGameSnapshot) {
        this._gameSt = gameSt;
        const { roomid } = gameSt;
        logging.withTag('PKW_ROOM').info(`[SDK] _onNoticeGameSnapShot; room: ${roomid}`);
        logging.setRoomId(roomid);

        if (this.resolveGameStPromise) {
            this.resolveGameStPromise();
            this.resolveGameStPromise = null;
        }
    }

    private waitForGameSt(): Promise<void> {
        if (this._gameSt) {
            return Promise.resolve();
        }

        this.gameStPromise = new Promise<void>((resolve) => {
            this.resolveGameStPromise = resolve;
        });

        return this.gameStPromise;
    }

    async _asyncSitDown() {
        logging.withTag('SDK').info('[SDK] _asyncSitDown');
        await this.waitForGameSt();
        if (this._gameSt) {
            this._requestSitDown();
        }
    }

    private _onNoticeBuyin(msg: gs_protocol.protocol.NoticeBuyin) {
        // This event happens for every buy in on the table
        logging
            .withTag('ON_NOTICE_BUYIN')
            .info('[PKW_ROOM] _onNoticeBuyin', { payload: msg, selfId: pkwGame.getSelfUid() });

        const isCurrentUser = msg.playerid == pkwGame.getSelfUid();
        if (!isCurrentUser) {
            return;
        }

        const { buyin_amount, self_stake, playerid, is_auto } = msg;
        pkwGame.updateCurrentStack(self_stake, 'OnNoticeBuyin');

        if (is_auto) {
            logging
                .withTag('SDK')
                .info(
                    `[PKW_ROOM] _onNoticeBuyin, automatic buyin happened. Buyin amount: ${buyin_amount}, User Id: ${playerid}`,
                );

            if (typeof msg.buyin_amount === 'string') {
                this._buyInAmount = Number(msg.buyin_amount);
            } else if (typeof msg.buyin_amount === 'number') {
                this._buyInAmount = msg.buyin_amount;
            } else if (typeof msg.buyin_amount === 'object') {
                this._buyInAmount = Number(Object.values(msg.buyin_amount)[0]);
            }
            this._rebuyCount++;
        }
    }

    private _requestSitDown() {
        logging.withTag('SDK').info('[SDK] _requestSitDown');

        let availableSeatId = this._getAvailableSeatId();
        if (availableSeatId == -1) {
            logging.withTag('SDK').warn('[SDK] no available seat id, leaving room');
            this._currentError = new UnrecoverableError('No available seats');
            this._requestLeaveRoom();
        } else {
            cv.gameNet.RequestSitdown(cv.roomManager.getCurrentRoomID(), availableSeatId, false);
        }
    }

    private _getAvailableSeatId(): number {
        let seatId = -1;
        let occupiedSeats = [];
        for (let player of this._gameSt.tstate.players) {
            if (player.playerid == this._pkwUser.pkwUid && player.seatid != -1) {
                logging
                    .withTag('SDK')
                    .info(`_getAvailableSeatId - user is already sitting on seatid: ${player.seatid}`);
                return player.seatid;
            }
            occupiedSeats.push(player.seatid);
        }

        for (let sid = 0; sid < this._roomSt.player_count_max; ++sid) {
            if (occupiedSeats.indexOf(sid) == -1) {
                seatId = sid;
                break;
            }
        }
        if (seatId == -1) {
            logging
                .withTag('SDK')
                .warn(
                    `_getAvailableSeatId - No available seatid, occupiedSeats: ${occupiedSeats}, player_count_max: ${this._roomSt.player_count_max}`,
                );
        }
        return seatId;
    }

    private _onStandupResp(resp) {
        logging.withTag('SDK').info('[SDK] _onStandup Resp: ' + resp.error);

        this._onUserActionDone(UserActionEnum.sitOut);
        this.setUserStatus(UserStatus.inRoom);
        this._doNextUserAction();
    }

    private _onSitDown(needBuyin: boolean) {
        logging
            .withTag('SDK')
            .info(`[SDK] _onSitDown`, { isSatDown: this._isSatDown, isBoughtIn: this._isBoughtIn });
        if (this._isSatDown && this._isBoughtIn) return;

        this._onUserActionDone(UserActionEnum.sitDown);
        this.setUserStatus(UserStatus.satDown);
        pkwGame.updateStatus(UserStatus.satDown);
        this._doNextUserAction(needBuyin);
        this._isSatDown = true;
    }

    async _asyncBuyin() {
        await this.waitForGameSt();
        logging.info('[SDK] _asyncBuyin');
        if (this._gameSt) {
            this._requestBuyin();
        }
    }

    public rebuy() {
        logging.info('[SDK] rebuy');
        if (this._buyInIsProcessing) {
            logging.info('[SDK] Cannot rebuy, buyin is processing');
            return;
        }
        this._isRebuying = true;
        this._requestBuyin();
    }

    private _requestBuyin() {
        if (this._buyInIsProcessing) {
            logging.info('[SDK] Cannot request buyin, buyin is processing');
            return;
        }
        this._buyInIsProcessing = true;
        this._requestBuyInAmount = this.calculateBuyInAmount();

        logging.setRoomId(this._gameSt.roomid);
        logging.withTag('SDK').info('[SDK] _requestBuyin RESULTS', {
            calculateBuyInAmount: this._requestBuyInAmount,
            currentStack: pkwGame._currentStack,
            buyinMultiplier: this._buyInMultiplier,
            ante: this._roomSt?.ante,
            bigBlind: this._roomSt?.big_blind,
            maxBuyIn: this._roomSt?.buyin_max,
            minBuyIn: this._roomSt?.buyin_min,
        });

        if (this._requestBuyInAmount > 0) {
            cv.gameNet.RequestBuyin(this._gameSt.roomid, this._requestBuyInAmount);
        } else {
            logging.withTag('PKW_ROOM').warn('[PKW_ROOM] _requestBuyin, buyIn amount is 0');
            this._onBoughtIn();
        }
    }

    private calculateBuyInAmount(): number {
        const baseValue =
            this._gameType == CreateGameMode.CreateGame_Mode_Short
                ? this._roomSt?.ante || 0
                : this._roomSt?.big_blind || 0;

        if (!baseValue) {
            logging.withTag('PKW_ROOM').warn('[PKW_ROOM] _calculateBuyInAmount, baseValue is 0');
            return 0;
        }

        if (!this._buyInMultiplier) {
            logging.withTag('PKW_ROOM').warn('[PKW_ROOM] _calculateBuyInAmount, buyInMultiplier is 0');
            return 0;
        }

        const initial = baseValue * this._buyInMultiplier;
        if (this._roomSt?.buyin_min == null) {
            logging.info('!!! this._roomSt?.buyin_min is not set');
        }
        const buyInMin = this._roomSt?.buyin_min || 0;
        if (this._roomSt?.buyin_max == null) {
            logging.info('!!! this._roomSt?.buyin_max is not set');
        }
        if (buyInMin > 0 && initial < buyInMin) {
            logging
                .withTag('PKW_ROOM')
                .warn(`[PKW_ROOM] _calculateBuyInAmount, buyIn amount increased up to buyInMin ${buyInMin}`);
            return buyInMin;
        }

        const buyInMax = this._roomSt?.buyin_max || 0;
        if (buyInMax > 0) {
            const buyInLeft = Math.max(buyInMax - pkwGame._currentStack, 0);
            if (buyInLeft > 0) {
                const result = Math.min(initial, buyInLeft);
                logging
                    .withTag('PKW_ROOM')
                    .warn(
                        `[PKW_ROOM] _calculateBuyInAmount, buyIn amount reduced according to buyInLeft: ${buyInLeft}; result: ${result}`,
                    );
                return result;
            }
            return 0;
        }

        return initial;
    }

    private _onBoughtIn() {
        logging
            .withTag('SDK')
            .info(
                `[SDK] _onBoughtIn, game name=${this._gameSt.params.game_name}, requested buyin=${this._requestBuyInAmount}`,
            );

        this._isBoughtIn = true;
        this._buyInIsProcessing = false;
        this._buyInAmount = this._requestBuyInAmount;
        this._requestBuyInAmount = 0;

        if (this._isRebuying) {
            logging.info(`[SDK] _onRebought, amount: ${(this._buyInAmount)}`);
            this._isRebuying = false;
            if (this._buyInAmount > 0) {
                this._rebuyCount++;
                // we add this to check later if not all amount of rebuy chips was added to the player's stack
                // it can happen in R5, where our stak is limited to maximum buy in size
                this.setRebuyWasMade(true);
                this._requestBuyInAmount = 0;
                this._onUserActionDone(UserActionEnum.rebuy);
            }
        } else {
            pkwGame.updateInfo(this._roomSt, this._gameSt);
            this._onUserActionDone(UserActionEnum.buyin);
        }

        this.setUserStatus(UserStatus.boughtIn);
        pkwGame.updateStatus(UserStatus.boughtIn);
        this._doNextUserAction();
    }

    //Utilities
    private _getEnumName(enumType: any, enumValue: number): string {
        return enumType[enumValue];
    }

    public setUserStatus(s: UserStatus) {
        logging.info(`[UserStatus Update]: ${this._pkwUser.currentStatus} -> ${s}`);
        logging.setStatus(s);
        this._pkwUser.currentStatus = s;
    }

    private static g_instance: pkwRoom;

    public static getInstance(): pkwRoom {
        if (!pkwRoom.g_instance) {
            pkwRoom.g_instance = new pkwRoom();
        }
        return pkwRoom.g_instance;
    }

    public getUserStatus() {
        return this._pkwUser.currentStatus;
    }
}

let pkwRoom_instance: pkwRoom;
export default pkwRoom_instance = pkwRoom.getInstance();
