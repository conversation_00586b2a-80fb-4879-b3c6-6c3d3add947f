import { userData } from './userData';

export class DataHandler {
    private userData: userData = null;
    private static instance: DataHandler;

    public static getInstance(): DataHandler {
        if (!this.instance) {
            this.instance = new DataHandler();
        }
        return this.instance;
    }

    public getUserData(): userData {
        if (!this.userData) {
            this.userData = userData.getInstance();
        }
        return this.userData;
    }

    public clearData(): void {
        if (this.userData) this.userData = userData.clearData();
    }
}
