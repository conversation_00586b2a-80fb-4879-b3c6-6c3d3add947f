import cv from '../cv';
import ws_protocol from '../../proto/ws_protocol';
import world_pb = ws_protocol.pb;
import { ecdhHandler } from '../tools/ecdhHandler';
import { logging } from 'shared';
import EncryptUtils from '../../commoms/utils/encryptUtils';

/**
 * 所有游戏服房间管理模块
 * 主要功能：记录和清理当前玩家所在的游戏服ID，游戏服房间ID
 * 统一进入房间接口和回调（便于跟踪记录玩家游戏状态）
 * 统一退出房间接口和回调（便于跟踪记录玩家游戏状态）
 */
export class RoomManager {
    private static instance: RoomManager;
    private current_gameId: number = 0; // ！当前游戏服ID：只标记游戏服，不会等于world服
    private current_roomId: number = 0; // ！当前游戏服房间ID
    private roomPassWord: string; // ！当前游戏服房间密码            (现仅德州)
    private isQuickRoom: boolean; // ！当前游戏服房间是否快速入座     (现仅德州)
    private isNeedPassword: boolean; // ！当前游戏服房间是否需要密码     (现仅德州)
    private _LeaveCowboyToShop: boolean = false;

    public isEnterMTT = false;
    public mtt_id: number = 0;
    public mtt_time: number = 0;
    public mtt_name: string = '';
    public mtt_backTime: number = 0;

    public lastPre: number = 0;
    private isSpectatorRevealEnabled: boolean = false;

    public static getInstance(): RoomManager {
        if (!this.instance) {
            this.instance = new RoomManager();
            this.instance.reset();
        }
        return this.instance;
    }

    private _checkShowLoadingByGameID(gameId: number) {
        const curSceneName: string = cv.config.getCurrentScene();
        if (curSceneName != cv.Enum.SCENE.HALL_SCENE && curSceneName != cv.Enum.SCENE.ROOM_SCENE) return;
        const password: string = this.getRoomPassWord();

        switch (gameId) {
            case world_pb.GameId.Bet:
            case world_pb.GameId.Jackfruit:
                {
                }
                break;
            case world_pb.GameId.Texas:
            case world_pb.GameId.StarSeat:
            case world_pb.GameId.Allin:
            case world_pb.GameId.PLO:
                {
                    if (this.isNeedPassword) {
                        if (password.length == 0) {
                            break;
                        }
                    }
                }
                break;
            case world_pb.GameId.CowBoy:
            case world_pb.GameId.VideoCowboy:
            case world_pb.GameId.HumanBoy:
            case world_pb.GameId.PokerMaster:
                {
                }
                break;
            default:
                {
                }
                break;
        }
    }

    public RequestJoinRoom(gameId: number = this.current_gameId, gameRoomId: number = this.current_roomId) {
        logging.withTag('SDK').info(`[SDK] RequestJoinRoom gameRoomId: ${gameRoomId}  gameId: ${gameId}`);
        this.setCurrentGameID(gameId);
        this.setCurrentRoomID(gameRoomId);

        this.setIsQuickRoom(false);

        if (cv.netWork.isEncrypt(gameId) && !ecdhHandler.getInstance().ecdh_getNeedGenKeyState()) {
            logging.withTag('SDK').info('[SDK] need ClientPubkey to send to Server');

            const client_pubX = ecdhHandler.getInstance().ecdh_getClientPubX();
            const client_pubY = ecdhHandler.getInstance().ecdh_getClientPubY();

            ecdhHandler.getInstance().ecdh_setNeedGenKeyState(true);

            cv.worldNet.RequestSetEcdhKey(0, client_pubX, client_pubY);
        } else {
            logging.withTag('SDK').info('[SDK] already logged in pkw server, no need key');
            this._doJoinRoomRequest();
        }
    }

    public onSecretResponse(): void {
        this._doJoinRoomRequest();
    }

    // 设置ecdh
    public onEcdhSecretResponse(data: any): void {
        const secret_type = data.secret_type;
        const server_pub_x = data.svr_public_key_x;
        const server_pub_y = data.svr_public_key_y;

        ecdhHandler.getInstance().ecdh_genClientKey(server_pub_x, server_pub_y);
        let _secretkey = '';
        if (secret_type == cv.Enum.ECDH_SECRET_TYPE.UseX) {
            _secretkey = ecdhHandler.getInstance().ecdh_getClientSecretX();
        } else if (secret_type == cv.Enum.ECDH_SECRET_TYPE.UseY) {
            _secretkey = ecdhHandler.getInstance().ecdh_getClientSecretY();
        } else if (secret_type == cv.Enum.ECDH_SECRET_TYPE.UseXY) {
            _secretkey = ecdhHandler.getInstance().ecdh_getClientSecretXY();
        } else {
            console.log('onEcdhSecretResponse secretType error.');
            return;
        }
        cv.dataHandler.getUserData().secretKey = EncryptUtils.MD5(_secretkey);
        this._doJoinRoomRequest();
    }

    private _doJoinRoomRequest() {
        logging
            .withTag('SDK')
            .info(`[SDK] in join room request ${this.current_gameId} ${this.current_roomId}`);
        const gameRoomId: number = this.getCurrentRoomID();
        cv.gameNet.RequestJoinRoom(gameRoomId, this.current_gameId, false);
    }

    public onJoinRoomResponse(data: any) {
        // cc.vv.loading.hide();
        logging.withTag('SDK').info('[SDK] onJoinRoomResponse data: ' + JSON.stringify(data));
        const error = this.getErrorCode(data);
        if (error == 1) {
            cv.roomManager.setCurrentRoomID(data.roomid);
            this.isSpectatorRevealEnabled = data.spectatorRevealEnabled;

            this.resetRoomCache();
            //   cv.MessageCenter.send("onJoinRoomSuccess");
        } else {
            // error : 1250 =  kicked cause overtime
            console.log('onJoinRoomResponse error: ' + error);
            // cv.Switch.hide();
            cv.roomManager.reset();
            //   cv.netWorkManager.closeGameHeart();
            cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
            //  cv.MessageCenter.send("onJoinRoomRespError", error);
        }
        cv.MessageCenter.send('onJoinRoomResp', error);
    }

    /**
     * @function 统一退出房间接口
     */
    public RequestLeaveRoom(LeaveCowboyToShop: boolean = false) {
        const gameID: number = this.getCurrentGameID();
        logging
            .withTag('SDK')
            .info(`RequestLeaveRoom`, { gameID, isGameZoom: this.checkGameIsZoom(gameID) });
        switch (gameID) {
            case world_pb.GameId.Bet:
            case world_pb.GameId.Texas:
            case world_pb.GameId.StarSeat:
            case world_pb.GameId.PLO: {
                cv.gameNet.RequestLeaveRoom(this.getCurrentRoomID());

                break;
            }
            case world_pb.GameId.CowBoy: {
                this._LeaveCowboyToShop = LeaveCowboyToShop;

                //  cv.getCowboyNet().RequestLeaveRoom();

                break;
            }
            case world_pb.GameId.VideoCowboy:
                {
                    // cv.videoCowboyNet.RequestLeaveRoom();
                }
                break;
            case world_pb.GameId.PokerMaster: {
                //  cv.getPokerMasterNet().requestLeaveRoom();

                break;
            }
            case world_pb.GameId.Jackfruit: {
                // cv.jackfruitNet.requestLeave();

                break;
            }
            case world_pb.GameId.HumanBoy: {
                //   cv.humanboyNet.requestLeaveRoom();

                break;
            }
            default: {
                if (this.currentGameIsZoom()) {
                    // 极速游戏必定弹出带入框
                    cv.gameNet.RequestQuickLeave(this.getCurrentRoomID());
                }

                break;
            }
        }
    }

    /**
     * 退出房间回调处理
     */
    public onResponse_LeaveRoom(data: any) {
        logging.withTag('SDK').info('onResponse_LeaveRoom');
        const error = this.getErrorCode(data);
        const gameID: number = this.getCurrentGameID();
        if (error == 1) {
            this.LeaveRoomSuccess();
            logging.withTag('SDK').info('[SDK] leaveroom success');
            cv.MessageCenter.send('leaveRoomSuccess');
            return;
            let leaveType = 0;

            if (typeof data.user_leave_type === 'number') {
                leaveType = data.user_leave_type;
            }

            if (cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.CowboyWeb) {
                //   cv.action.switchScene(cv.Enum.SCENE.ROOM_SCENE);

                return;
            }

            switch (gameID) {
                case world_pb.GameId.Jackfruit: {
                    //   JackfruitMgr.tRoomData.reset();

                    const sceneData = {
                        scene: 'pkwgame',
                        data: null,
                    };

                    (<any>window).SceneMgr.setDataFromLastScene(sceneData);

                    //  cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE);

                    break;
                }
                case world_pb.GameId.Allin:
                case world_pb.GameId.Bet:
                case world_pb.GameId.Texas:
                case world_pb.GameId.StarSeat:
                case world_pb.GameId.PLO: {
                    cv.GameDataManager.tRoomData.reset();

                    const sceneData = { scene: 'pkwgame', data: null };

                    (<any>window).SceneMgr.setDataFromLastScene(sceneData);

                    if (leaveType == 1) {
                        // 围观超时被T

                        const curSceneName: string = cv.config.getCurrentScene();

                        if (curSceneName != cv.Enum.SCENE.HALL_SCENE) {
                            //   cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene): void => {
                            //   });
                        } else {
                            cv.roomManager.setCurrentRoomID(-1);

                            return;
                        }
                    } else if (leaveType == 2) {
                        // 客服操作强制离开


                        const curSceneName: string = cv.config.getCurrentScene();

                        if (curSceneName != cv.Enum.SCENE.HALL_SCENE) {
                            //   cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE, (scene: cc.Scene): void => {
                            //     cv.TP.showMsg(str, cv.Enum.ButtonStyle.GOLD_BUTTON, null);
                            //   });
                        } else {
                            cv.roomManager.setCurrentRoomID(-1);

                            return;
                        }
                    } else {
                        // cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE);
                    }

                    break;
                }
                case world_pb.GameId.VideoCowboy: // 回到牛仔房间列表界面
                case world_pb.GameId.HumanBoy: // 回到百人德州房间列表界面
                case world_pb.GameId.CowBoy: // 回到牛仔房间列表界面
                case world_pb.GameId.PokerMaster: {
                    // 回到扑克大师房间列表界面
                    cv.roomManager.reset();
                    //cv.action.createShieldLayer(null, "shieldLayer-switchScene", cv.Enum.ZORDER_TYPE.ZORDER_LOADING);

                    // 原生版本扑克大师,牛仔在下注过程中，充值退出会失败，此参数表示是否成功充值的方式退出
                    const isPokerMasterExitWithRechargeSuccess = (<any>window).HMFAppSetting
                        .isPokerMasterExitWithRechargeSuccess;

                    let exitWhere = (<any>window).HMFAppSetting.pokerMasterExitWhere;

                    if (isPokerMasterExitWithRechargeSuccess) {
                        exitWhere = 3;
                    }

                    let scene: string;

                    if (exitWhere == 1) {
                        scene = 'pkwgame'; // 大厅
                    } else if (exitWhere == 2) {
                        scene = 'openWalletSet'; // 打开钱包
                    } else if (exitWhere == 3) {
                        scene = 'rechargeInCowboy'; // 打开钱包并充值
                    } else {
                        scene = 'littleGame'; // 小游戏列表
                    }

                    const sceneData = { scene };

                    (<any>window).SceneMgr.setDataFromLastScene(sceneData);

                    //  cv.action.switchScene(cv.Enum.SCENE.HALL_SCENE);

                    break;
                }
                default:
                    break;
            }

            if (!this.currentGameIsZoom()) {
                this.LeaveRoomSuccess();
            } else {
                cv.GameDataManager.tRoomData.reset();
                //  cv.GameDataManager.tGiftData.reset();

                // 如果是急速房间，自己还没落座，直接退出  --temp add
                if (cv.GameDataManager.tRoomData.i32SelfSeat == -1) {
                    cv.MessageCenter.send('on_leave_room_succ');
                }
            }
        } else {
            cv.ToastError(data.error);
            const scene = cv.config.getCurrentScene();

            if (
                this.isEnterMTT &&
                (scene == cv.Enum.SCENE.GAME_SCENE || scene == cv.Enum.SCENE.JACKFRUIT_SCENE)
            ) {
                return;
            }

            switch (gameID) {
                case world_pb.GameId.CowBoy:
                case world_pb.GameId.VideoCowboy:
                case world_pb.GameId.HumanBoy:
                case world_pb.GameId.PokerMaster: {
                    if (error == 31024 || error == 41031 || error == 41032) {
                        // 下注不能退出
                        //  cv.TP.hideTipsPanel();
                        // cv.getPokerMasterNet()._postError(100);
                        // cv.ToastGameError(gameID, error);

                        (<any>window).HMFAppSetting.isPokerMasterExitWithRechargeSuccess = false;
                    } else {
                        //  cv.ToastGameError(gameID, error);
                    }

                    break;
                }
                case world_pb.GameId.Allin: {
                    cv.ToastError(data.error);

                    break;
                }
                default: {
                    if (this.currentGameIsZoom()) {
                        if (data.Error === 22 || data.Error === 1201) {
                            // 22 - rooms changed
                            // 1201 - leaving, waiting for the hand to finish
                            // do nothing
                        } else if (data.Error === 1 || data.Error === 1203) {
                            this.LeaveRoomSuccess();
                            cv.MessageCenter.send('leaveRoomSuccess');
                        } else {
                            cv.MessageCenter.send('leaveRoomFailed', data.Error);
                        }
                    } else {
                        cv.MessageCenter.send('leaveRoomFailed', data.error);
                    }

                    break;
                }
            }
        }
    }

    /**
     * 离开房间清理房间缓存信息
     * 小游戏因为需要gameID来拉取房间列表，从房间退出时gameId不清除
     */
    public LeaveRoomSuccess() {
        switch (this.getCurrentGameID()) {
            case world_pb.GameId.Bet:
            case world_pb.GameId.Texas:
            case world_pb.GameId.StarSeat:
            case world_pb.GameId.PLO:
            case world_pb.GameId.Jackfruit:
            case world_pb.GameId.Allin:
                {
                    this.reset();
                }
                break;

            case world_pb.GameId.CowBoy:
            case world_pb.GameId.VideoCowboy:
            case world_pb.GameId.HumanBoy:
            case world_pb.GameId.PokerMaster:
                {
                    this.resetRoomCache();
                    this.setCurrentRoomID(0);
                }
                break;

            default:
                if (this.currentGameIsZoom()) {
                    this.reset();
                }
                break;
        }
        //  cv.netWorkManager.closeGameHeart();
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
    }

    /**
     * 一次性退出到HallScene场景
     */
    public LeaveRoomAndGame() {
        this.LeaveRoomSuccess();
        this.setCurrentGameID(0);
    }

    /**
     *
     * @param GameId 通过"gameid"获取场景名称
     */
    public getSceneNameByGameId(gameid: number): string {
        console.log('Saad gettng poker scene by game id ' + gameid);
        let sceneName = '';
        switch (gameid) {
            case world_pb.GameId.Texas:
            case world_pb.GameId.StarSeat:
            case world_pb.GameId.PLO:
                sceneName = cv.Enum.SCENE.GAME_SCENE;
                break;
            //  case world_pb.GameId.CowBoy: sceneName = CowboyUtils.getSceneNameByUIStyle(); break;
            case world_pb.GameId.VideoCowboy:
                sceneName = cv.Enum.SCENE.VIDEOCOWBOY_SCENE;
                break;
            case world_pb.GameId.HumanBoy:
                sceneName = cv.Enum.SCENE.HUMANBOY_SCENE;
                break;
            case world_pb.GameId.PokerMaster:
                if ((<any>window).CurrentUserInfo.user.wasUserInDiamondGame) {
                    //    sceneName = cv.native.IsPad() ? "PokerMasterScenePad" : cv.Enum.SCENE.POKERMASTER_SCENE;
                } else {
                    //   sceneName = cv.native.IsPad() ? "PokerMasterScenePadHall" : cv.Enum.SCENE.POKERMASTER_SCENE_HALL;
                }

                break;
            case world_pb.GameId.Allin:
                sceneName = cv.Enum.SCENE.GAME_SCENE;
            case world_pb.GameId.Bet:
                sceneName = cv.Enum.SCENE.GAME_SCENE;
                break;
            case world_pb.GameId.Jackfruit:
                sceneName = cv.Enum.SCENE.JACKFRUIT_SCENE;
                break;

            default:
                if (this.checkGameIsZoom(gameid)) {
                    sceneName = cv.Enum.SCENE.GAME_SCENE;
                }
                break;
        }

        return sceneName;
    }

    public getErrorCode(data: any): number {
        let nRet: number = -1;
        const gameID: number = this.getCurrentGameID();
        switch (gameID) {
            case world_pb.GameId.Texas:
            case world_pb.GameId.StarSeat:
            case world_pb.GameId.PLO:
            case world_pb.GameId.Bet:
            case world_pb.GameId.Allin:
                {
                    nRet = data.error;
                }
                break;

            case world_pb.GameId.CowBoy:
            case world_pb.GameId.VideoCowboy:
            case world_pb.GameId.HumanBoy:
            case world_pb.GameId.Jackfruit:
            case world_pb.GameId.PokerMaster:
                {
                    nRet = data.code;
                }
                break;

            default:
                if (this.checkGameIsZoom(gameID)) {
                    return data.error;
                }
                break;
        }
        return nRet;
    }

    public setIsSpectatorRevealEnabled(enabled: boolean) {
        this.isSpectatorRevealEnabled = enabled;
    }

    public getIsSpectatorRevealEnabled() {
        return this.isSpectatorRevealEnabled;
    }

    public setCurrentGameID(id: number) {
        if (cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.CowboyWeb) {
            this.current_gameId = world_pb.GameId.CowBoy;
        } else {
            this.current_gameId = id;
        }
    }

    public getCurrentGameID(): number {
        if (cv.config.GET_CLIENT_TYPE() == cv.Enum.ClientType.CowboyWeb) {
            return world_pb.GameId.CowBoy;
        }

        return this.current_gameId;
    }

    public setCurrentRoomID(id: number) {
        logging.setRoomId(id);
        this.current_roomId = id;
    }

    public getCurrentRoomID(): number {
        return this.current_roomId;
    }

    public checkGameIsZoom(gameId: number): boolean {
        return gameId >= world_pb.GameId.ZoomTexas && gameId <= world_pb.GameId.ZoomTexasMax;
    }

    public currentGameIsZoom(): boolean {
        return this.checkGameIsZoom(this.getCurrentGameID());
    }

    public setRoomPassWord(roomPassWord: string) {
        this.roomPassWord = roomPassWord;
    }

    public getRoomPassWord(): string {
        return this.roomPassWord;
    }

    public setIsNeedPassword(isNeedPassword: boolean) {
        this.isNeedPassword = isNeedPassword;
    }

    public getIsNeedPassword(): boolean {
        return this.isNeedPassword;
    }

    public setIsQuickRoom(IsQuickRoom: boolean) {
        this.isQuickRoom = IsQuickRoom;
    }

    public getIsQuickRoom(): boolean {
        return this.isQuickRoom;
    }

    //! 当进入房间成功后都应该调用此方法清掉缓存（有用到这些参数的地方）
    public resetRoomCache() {
        this.roomPassWord = '';
        // this.isQuickRoom = false;
        this.isNeedPassword = false;
    }

    public reset() {
        this.current_gameId = 0;
        this.current_roomId = 0;
        this.roomPassWord = '';
        this.isQuickRoom = false;
        this.isNeedPassword = false;
        this.isSpectatorRevealEnabled = false;
    }

    private IsEligibleForFeaturedHandSubmission(gameId: number): boolean {
        return (
            this.checkGameIsZoom(gameId) ||
            gameId === world_pb.GameId.Texas ||
            gameId === world_pb.GameId.StarSeat ||
            gameId === world_pb.GameId.PLO
        );
    }

    public isH5StarSeatGame() {
        return false;
        //return cc.sys.isBrowser && this.getCurrentGameID() == cv.Enum.GameId.StarSeat;
    }

    public closeScheduleForMTT() {
        // 按照需求，开始时间固定，不进行倒计时
    }

    public startScheduleForMTT() {
        // 按照需求，开始时间固定，不进行倒计时
    }
}
