import { NetWorkProxy } from './NetWorkProxy';
import ws_protocol from '../../proto/ws_protocol';
import world_pb = ws_protocol.pb;
import cv from '../cv';
import { Tools } from '../tools/Tools';
import { logging } from 'shared';

/**
 *
 * 世界服消息处理模块
 *
 */
const AUTO_REQUEST_BOARD_LIST_SECS = 90;
export class WorldNetWork extends NetWorkProxy {
    public static instance: WorldNetWork;

    // private _thresholdController = new MsgThresholdController();
    private _autoRequestBoardTime: Date = null;

    public init() {
        // ！ 世界服消息注册
        this.registerMsg(world_pb.MSGID.MsgID_DupLogin_Notice, this.NoticeDupLogin.bind(this));
        this.registerMsg(world_pb.MSGID.MsgID_HeartBeat_Response, this.responseHeartBeat.bind(this));
        this.registerMsg(world_pb.MSGID.MsgID_GetUserData_Response, this.responseGetUserData.bind(this));
        this.registerMsg(world_pb.MSGID.MsgID_GetUserData_Notice, this.noticeGetUserData.bind(this));
        this.registerMsg(world_pb.MSGID.MsgID_NotifyUserGoldNum_Notice, this.NoticeCoinChanged.bind(this));
        this.registerMsg(world_pb.MSGID.MsgID_Logon_Response, this.responseLoginServer.bind(this));
        this.registerMsg(world_pb.MSGID.MsgID_Login_Notice, this.NoticeLoginServer.bind(this));

        this.registerMsg(world_pb.MSGID.MsgID_SearchClubInfo_Response, this.responseCommon.bind(this));

        this.registerMsg(world_pb.MSGID.MsgID_GameStatusV2_Response, this.HandleGameStatusMessage.bind(this)); // MsgID_GameStatus_Response
        this.registerMsg(world_pb.MSGID.MsgID_SetSecretKey_Response, this.ResponseSetSecretKey.bind(this));
        this.registerMsg(
            world_pb.MSGID.MsgID_SetSecretKeyEx_Response,
            this.ResponseSetEcdhSecretKey.bind(this),
        );

        this.registerMsg(world_pb.MSGID.MsgID_AuthApi_Response, this.ResponseAuthApi.bind(this));
        this.registerMsg(world_pb.MSGID.MsgID_AuthApi_Notice, this.NoticeAuthApi.bind(this));
        //  this.registerMsg(world_pb.MSGID.MsgID_GameMaintainStatus_Notice, this.NoticeMTTStatus.bind(this));

        //this.registerMsg(world_pb.MSGID.MsgID_UsdtExchange_Config_Notice, this.ExchangeGetUsdtConfigNotice.bind(this));
        // Diamond Table switch
        this.registerMsg(
            world_pb.MSGID.MsgID_BoardVisibleSwitch_Response,
            this._onResponseBoardVisibleSwitch.bind(this),
        );

        this._autoRequestBoardTime = new Date();
    }

    public onConnectOpen() {
        //  cv.netWorkManager.StartWorldHeartBeat();
        this.requestLoginServer();
    }

    public registerMsg(msgid: number, fn: any): void {
        this.registerMessage(msgid, fn, cv.Enum.GameId.World);
    }

    public sendWorldMsg(pbbuf: any, msgid: number, Roomid: number): boolean {
        return this.sendMsg(pbbuf, msgid, 0, cv.Enum.SeverType.SeverType_World, cv.Enum.GameId.World);
    }

    public onMiniGamesAtmosphereNotice(pbuf) {
        const msg = this.decodePB('MiniGamesAtmosphereNotice', pbuf);
        if (msg) {
            cv.MessageCenter.send('updateMiniGamesAtmosphere', msg);
        }
    }

    public requestMinigamesAtmosphere() {
        const msg = new world_pb.MiniGamesAtmosphereMessagesRequest();
        const puf = this.encodePB('MiniGamesAtmosphereMessagesRequest', msg);
        this.sendWorldMsg(puf, world_pb.MSGID.MsgID_MiniGamesAtmosphereRequest, 0);
        // MockMiniGameLogic.getInstance().mockMiniGameAtmosphereMessage();
    }

    public onMiniGamesAtmosphereResponse(pbuf) {
        const msg = this.decodePB('MiniGamesAtmosphereMessagesResponse', pbuf);
        if (msg) {
            cv.MessageCenter.send('MiniGamesAtmosphereMessagesResponse', msg);
        }
    }

    public NoticeCoinChanged(puf) {
        const msg: world_pb.NoticeNotifyUserGoldNum = this.decodePB('NoticeNotifyUserGoldNum', puf);
        if (msg) {
            if (msg.uid == cv.dataHandler.getUserData().u32Uid) {
                cv.dataHandler.getUserData().u32Chips = msg.goldNum;
                cv.dataHandler.getUserData().game_coin = msg.game_coin;
                cv.dataHandler.getUserData().total_amount = msg.total_amount;
                cv.dataHandler.getUserData().user_points = msg.total_points;
                cv.dataHandler.getUserData().usdt = msg.usdt;

                cv.MessageCenter.send('update_gold');
            }
        }
    }

    public requestLoginServer() {
        const device_info: string = cv.dataHandler.getUserData().deviceInfo;
        const msg: world_pb.RequestLogon = new world_pb.RequestLogon();
        msg.version = '99.99.99'; // temporarily solution due to minor versioning request from wpk team, currently only support x.y.z instead of w.x.y.z on backend side
        msg.token = cv.dataHandler.getUserData().user_token;
        msg.device_info = device_info;
        msg.invitation_code = cv.dataHandler.getUserData().invitation_code || '';
        msg.client_type = cv.config.GET_CLIENT_TYPE();
        msg.CurrentLanguage = cv.config.getCurrentLanguage();
        msg.os = 'web';
        msg.os_version = '';
        msg.third_version = '5.7.27.11';

        const puf: any = this.encodePB('RequestLogon', msg);
        this.sendWorldMsg(puf, world_pb.MSGID.MsgID_Logon_Request, 0);
    }

    public responseLoginServer(puf) {
        const msg = this.decodePB('ResponseLogon', puf);
        if (msg) {
            if (msg.blSpinStatus) {
                cv.httpHandler._updateSpinStatus(msg.blSpinStatus);
            }
            if (msg.bl_mtt_status) {
                const preNum = cv.config.HAVE_MTT;
                if (msg.bl_mtt_status == 2) {
                    cv.config.HAVE_MTT = false;
                } else if (msg.bl_mtt_status == 1) {
                }
                if (preNum != cv.config.HAVE_MTT) {
                    cv.MessageCenter.send('update_mtt_state');
                }
            }

            if (msg.blackJackStatus) {
                const preNumJack = cv.config.HAVE_BLACKJACK;
                if (msg.blackJackStatus == 2) {
                    // 21点，2是维护，1是开启
                    cv.config.setBlackJack(false);
                } else if (msg.blackJackStatus == 1) {
                }
                if (preNumJack != cv.config.HAVE_BLACKJACK) {
                    // 如果状态有改变
                    cv.MessageCenter.send('update_blackJack_state');
                }
            } else if (msg.blackJackData) {
                //  BJPVPConnector.instance.onAuthBlackJackSucc(msg.blackJackData);
            } else {
                //  BJPVPConnector.instance.onAuthBlackJackError(BJPVPConnector.instance.keyConfig.tokenErrorMsg.NO_TOKEN);
            }

            const error = msg.error;

            let bMaintain = false;
            if (error == 226) {
                bMaintain = true;
            }
            cv.MessageCenter.send('sendShowMaintainMsg', bMaintain);

            if (error != 1) {
                if (
                    error != 2 &&
                    error != 3 &&
                    error != 4 &&
                    error != 5 &&
                    error != 6 &&
                    error != 7 &&
                    error != 8 &&
                    error != 197 &&
                    error != 229 &&
                    error != 31123
                ) {
                    logging.info('ServerErrorCode' + error);
                } else {
                    cv.netWorkManager.OnNeedRelogin(error);
                }
            } else {
                cv.dataHandler.getUserData().firstClubId = msg.firstClubId;
                cv.dataHandler.getUserData().firstAlliId = msg.firstAlliId;
                cv.dataHandler.getUserData().isEncrypt = msg.swtichList;
                // this.requestGetUserData();
                // this.MiniGamesListRequest();
                // this.requestSnapshotList();
                this.requestBoardVisibleSwitch();
                cv.netWorkManager.OnWorldServerLogin(error);
                if (!cv.dataHandler.getUserData().bGetTHands) {
                    cv.worldNet.GetTexasHandsRequest();
                }

                // if (cv.roomManager.getCurrentGameID() != cv.Enum.GameId.GameId_Dummy) {
                //     cv.roomManager.RequestJoinRoom();
                // }
            }
            cv.MessageCenter.send('onLoggedInWorldServer', msg);
        }
    }

    public NoticeLoginServer(puf) {
        const logon = this.decodePB('NoticeLogin', puf);
        if (logon) {
            if (logon.gameid != 0 && logon.roomid != 0) {
                cv.roomManager.setCurrentRoomID(logon.roomid);
                cv.roomManager.setCurrentGameID(logon.gameid);
            }
            cv.MessageCenter.send('onNoticeLoginServer');
        }
    }

    public requestDeviceInfoReport(channel) {
        const tempModule = cv.worldPB.lookupType('RequestDeviceInfoReport');
        if (tempModule) {
            const Ip =
                cv.dataHandler.getUserData().user_ip == null
                    ? '127.0.0.1'
                    : cv.dataHandler.getUserData().user_ip;
            const device_info = cv.dataHandler.getUserData().deviceInfo;
            const msg: object = { device_info, report_channel: channel, Ip };
            const pbbuf = tempModule.encode(msg).finish();

            this.sendWorldMsg(pbbuf, world_pb.MSGID.MsgID_DeviceInfo_Report_Request, 0);
            this.registerMsg(
                world_pb.MSGID.MsgID_DeviceInfo_Report_Response,
                this.responseDeviceInfoReport.bind(this),
            );
        }
    }

    public responseDeviceInfoReport(puf) {
        const msg = this.decodePB('ResponseDeviceInfoReport', puf);
        logging.info('[SDK] responseDeviceInfoReport', msg);
    }

    public requestGetAllRemarks() {
        const tempModule = cv.worldPB.lookupType('RequestGetAllRemarks');
        if (tempModule) {
            const msg: object = { playerid: cv.dataHandler.getUserData().u32Uid };
            const pbbuf = tempModule.encode(msg).finish();

            this.sendWorldMsg(pbbuf, world_pb.MSGID.MsgID_GetAllRemarks_Request, 0);
            this.registerMsg(world_pb.MSGID.MsgID_GetAllRemarks_Response, this.responseCommon.bind(this));
            this.registerMsg(world_pb.MSGID.MsgID_GetAllRemarks_Notice, this.noticeGetAllRemarks.bind(this));
        }
    }

    public requestGetAllRemarksByUid(uid: number) {
        const tempModule = cv.worldPB.lookupType('RequestGetAllRemarks');
        if (tempModule) {
            const msg: object = { playerid: uid };
            const pbbuf = tempModule.encode(msg).finish();

            this.sendWorldMsg(pbbuf, world_pb.MSGID.MsgID_GetAllRemarks_Request, 0);
            this.registerMsg(world_pb.MSGID.MsgID_GetAllRemarks_Response, this.responseCommon.bind(this));
            this.registerMsg(world_pb.MSGID.MsgID_GetAllRemarks_Notice, this.noticeGetAllRemarks.bind(this));
        }
    }

    public noticeGetAllRemarks(puf: any) {
        // console.log(puf);
        // const msg = this.decodePB("NoticeGetAllRemarks", puf);
        // if (msg && msg.remarks_data != "{}" && msg.remarks_data != "") {
        //     const remarks_data = JSON.parse(msg.remarks_data);
        //     if (remarks_data != null) {
        //         for (let i = 0; i < remarks_data.length; i++) {
        //             remarks_data[i].avatar = Tools.checkAvatar(remarks_data[i].avatar);
        //             cv.dataHandler.getUserData().addRemark(remarks_data[i].uid, remarks_data[i].type, remarks_data[i].remark, remarks_data[i].nickname, remarks_data[i].avatar, remarks_data[i].plat);
        //         }
        //         cv.MessageCenter.send("update_remarks");
        //     }
        // }
    }

    public requestCurrentBoardList() {
        const tempModule = cv.worldPB.lookupType('RequestClubCurrentBoardV3');
        if (tempModule) {
            const msg: object = {};
            const pbbuf = tempModule.encode(msg).finish();
            this.sendWorldMsg(pbbuf, world_pb.MSGID.MsgID_ClubCurrentBoardV2_Request, 0);
            this.registerMsg(
                world_pb.MSGID.MsgID_ClubCurrentBoardV2_Response,
                this.responseCommon.bind(this),
            );
            this.registerMsg(
                world_pb.MSGID.MsgID_ClubCurrentBoardV2_Notice,
                this.noticeCurrentBoardList.bind(this),
            );
            this._autoRequestBoardTime = new Date();
            // cv.MessageCenter.send('clearDiamondPlayerCount');
        }
    }

    public requestBoardVisibleSwitch() {
        const msg = {};
        const puf = this.encodePB('RequestBoardVisibleSwitch', msg);
        this.sendWorldMsg(puf, world_pb.MSGID.MsgID_BoardVisibleSwitch_Request, 0);
    }

    private _onResponseBoardVisibleSwitch(puf: any) {
        const msg: world_pb.ResponseBoardVisibleSwitch = this.decodePB('ResponseBoardVisibleSwitch', puf);
        if (msg) {
            const error = msg.error;
            if (error === 1) {
                cv.dataHandler.getUserData().diamondLobbyFlag = msg;
            }
        }
        cv.MessageCenter.send('onResponseBoardVisibleSwitch'); // Send response in either cases to disable loading
    }

    public noticeCurrentBoardList(puf: any) {
        const noti: world_pb.NoticeClubCurrentBoardV3 = this.decodePB('NoticeClubCurrentBoardV3', puf);
        if (!noti) return;

        let tables: world_pb.IClubGameSnapshotV3[] = [];
        cv.dataHandler.getUserData().shortDeckFlags = noti.flags as world_pb.FeatureFlags;
        for (let i = 0; i < noti.list.length; ++i) {
            const item = noti.list[i];
            tables.push(item);
        }

        if (tables.length === noti.total) {
            // cv.MessageCenter.send('noticeCurrentBoardList');
            cv.MessageCenter.send('onGetTables', tables);
        }
    }

    public responseCommon(puf) {
        const msg = this.decodePB('ResponseGetAllRemarks', puf);
        if (msg) {
            const error = msg.error;
            // console.log("error::.>" + error);
            if (error !== 1) {
                logging.info('ServerErrorCode' + error);
                // cv.MessageCenter.send('hideTopSend');
            }
        }
    }

    public responseHeartBeat(puf) {
        const msg = this.decodePB('ResponseHeartBeat', puf);
        if (msg) {
            const error = msg.uid;
        }
    }

    public NoticeDupLogin(puf) {
        const msg = this.decodePB('DupLoginNotice', puf);
        // // console.log("recev world heartbeat====================");
        if (!msg) {
            return;
        }

        if (msg.error == 224) {
            cv.netWorkManager.OnNeedRelogin(224);
        } else {
            cv.netWorkManager.OnNeedRelogin(4);
        }
    }

    public requestGetUserData() {}

    public responseGetUserData(puf) {
        const msg = this.decodePB('ResponseGetUserData', puf);
        if (msg) {
            const error = msg.error;

            // console.log("error::.>" + error);
        }
    }

    public noticeGetUserData(puf) {
        const data: world_pb.NoticeGetUserData = this.decodePB('NoticeGetUserData', puf);
        // let NoticeGetUserData = cv.worldPB.lookupType("NoticeGetUserData");
        if (data) {
            // let recvMsg = new Uint8Array(puf);
            // let data = NoticeGetUserData.decode(recvMsg);
            data.avatar = Tools.checkAvatar(data.avatar);
            cv.dataHandler.getUserData().nick_name = data.nick_name;
            cv.dataHandler.getUserData().u32Chips = data.user_gold;
            cv.dataHandler.getUserData().total_amount = data.total_amount;
            cv.dataHandler.getUserData().game_coin = data.game_coin;
            cv.dataHandler.getUserData().user_points = data.user_points;
            cv.dataHandler.getUserData().points_ratio = data.ratio;
            cv.dataHandler.getUserData().headUrl = data.avatar;
            cv.dataHandler.getUserData().u32Uid = data.user_id;
            cv.dataHandler.getUserData().mobile = data.mobile;
            cv.dataHandler.getUserData().gender = data.gender;
            cv.dataHandler.getUserData().user_marks = data.user_marks;
            cv.dataHandler.getUserData().clubs_max = data.clubs_max;
            cv.dataHandler.getUserData().current_clubs = data.current_clubs;
            cv.dataHandler.getUserData().u32CardType = data.card_type;
            cv.dataHandler.getUserData().u32Deposit_gold = data.deposit_gold;
            cv.dataHandler.getUserData().usdt = data.usdt;
            cv.dataHandler.getUserData().deposit_usdt = data.deposit_usdt;
            cv.dataHandler.getUserData().priorityareaCode = data.areaCode;
            cv.dataHandler.getUserData().prioritymobile = data.mobile2;

            cv.dataHandler.getUserData().cur_system_time = data.system_time; // 当前系统时间
            cv.dataHandler.getUserData().calm_down_deadline_time = data.calm_down_deadline_time; // 小游戏冷静截至时间戳

            cv.MessageCenter.send('update_info');
            if (!cv.dataHandler.getUserData().bGetTHands) {
                cv.worldNet.GetTexasHandsRequest();
            }
        }
    }

    GetTexasHandsRequest() {
        if (cv.dataHandler.getUserData().bGetTHands) return;
        if (cv.dataHandler.getUserData().isTouristUser) return;
        // if (!cv.dataHandler.getActivityData().isSystemAvatar()) return; //Commeneted as if user changes  from custom avatar to system avatar, there would be needed total hands
        const msg = {};
        const puf = this.encodePB('GetTexasHandsRequest', msg);
        this.sendWorldMsg(puf, world_pb.MSGID.MsgId_GetTexasTotalHands_Request, 0);
    }

    GetTexasHandsResponse(puf: any) {
        const msg: ws_protocol.pb.GetTexasHandsResponse = this.decodePB('GetTexasHandsResponse', puf);
        if (msg) {
            if (msg.error == 1) {
                cv.dataHandler.getUserData().totalHands = msg.totalHands;
                cv.dataHandler.getUserData().bGetTHands = true;
            } else {
                cv.ToastError(msg.error);
            }
        }
    }

    public static getInstance(): WorldNetWork {
        if (!this.instance) {
            this.instance = new WorldNetWork();
            this.instance.init();
        }
        return this.instance;
    }

    /**
     * 请求验证结果(0.成功 1.失败)
     * @param result
     */
    requestAuthVerify(result: number): void {
        const msg: world_pb.AuthVerifyRequest = world_pb.AuthVerifyRequest.create();
        msg.result = result;

        const puf: any = this.encodePB('AuthVerifyRequest', msg);
        this.sendWorldMsg(puf, world_pb.MSGID.MsgID_AuthVerify_Request, 0);
        this.registerMsg(world_pb.MSGID.MsgID_AuthVerify_Response, this._responseAuthVerify.bind(this));
    }

    private _responseAuthVerify(puf: any): void {
        const resp: world_pb.AuthVerifyResponse = this.decodePB('AuthVerifyResponse', puf);
        if (!resp) return;

        cv.MessageCenter.send('on_update_slider_verify_result', resp);
    }

    HandleGameStatusMessage(puf) {
        const msg = this.decodePB('GameStatusV3Response', puf); // GameStatusResponse

        if (msg) {
            if (msg.status == 1) {
                cv.worldNet.RequestRoomList(msg.id);
            } else if (msg.status == 2) {
                cv.roomManager.setCurrentGameID(cv.Enum.GameId.GameId_Dummy);
                logging.info('ServerErrorCode104');
                cv.MessageCenter.send('MiniGames_gameStateError', msg.id);
            }
        }
    }

    /**
     * 请求当前游戏房间列表
     */
    RequestRoomList(gameID: number) {
        const msg: any = {};
        let puf: any = null;
        let RequestID: number = 0;

        switch (gameID) {
            case cv.Enum.GameId.VideoCowboy:
                RequestID = world_pb.MSGID.MsgID_VideoCowboy_List_Request;
                puf = this.encodePB('VideoCowboyGameListRequest', msg);
                break;
            case cv.Enum.GameId.CowBoy:
                {
                    RequestID = world_pb.MSGID.MsgID_CowBoy_List_Request;
                    puf = this.encodePB('CowBoyGameListRequest', msg);
                }
                break;

            case cv.Enum.GameId.HumanBoy:
                {
                    RequestID = world_pb.MSGID.MsgID_HumanBoy_List_Request;
                    puf = this.encodePB('HumanBoyGameListRequest', msg);
                }
                break;

            case cv.Enum.GameId.PokerMaster:
                {
                    RequestID = world_pb.MSGID.MsgID_PokerMaster_List_Request;
                    puf = this.encodePB('PokerMasterGameListRequest', msg);
                }
                break;

            default:
                break;
        }

        if (RequestID != 0) {
            this.sendWorldMsg(puf, RequestID, 0);
        }
    }

    // 发送加密secretKEy
    RequestSetSecretKey(secretKey: string) {
        const msg = {
            Secret_key: secretKey,
        };
        const puf = this.encodePB('SetSecretKeyRequest', msg);
        this.sendWorldMsg(puf, world_pb.MSGID.MsgID_SetSecretKey_Request, 0);
    }

    ResponseSetSecretKey(puf: any): void {
        const msg = this.decodePB('SetSecretKeyResponse', puf);
        if (msg) {
            if (msg.error == 1) {
                cv.roomManager.onSecretResponse();
            } else {
                console.log('need relogin');
                // cv.netWorkManager.OnNeedRelogin(msg.error);
            }
        }
    }

    // 发送ecdh加密算法
    RequestSetEcdhKey(secretKey: number, cli_public_key_x: string, cli_public_key_y: string) {
        const msg = {
            secret_type: secretKey,
            cli_public_key_x,
            cli_public_key_y,
        };
        const puf = this.encodePB('SetSecretKeyExRequest', msg);
        this.sendWorldMsg(puf, world_pb.MSGID.MsgID_SetSecretKeyEx_Request, 0);
    }

    ResponseSetEcdhSecretKey(puf: any): void {
        const msg = this.decodePB('SetSecretKeyExResponse', puf);
        if (msg) {
            if (msg.error == 1) {
                cv.roomManager.onEcdhSecretResponse(msg);
            } else {
                cv.netWorkManager.OnNeedRelogin(msg.error);
            }
        }
    }

    lastRequestAuthApiTime: number = 0;
    public RequestAuthApi(): boolean {
        console.log('============== >>>> RequestAuthApi 1  ====' + new Date().getTime());
        const nowTime = new Date().getTime();
        // console.log("MTTTest RequestAuthApi", this.lastRequestAuthApiTime, nowTime, nowTime - this.lastRequestAuthApiTime);
        if (nowTime - this.lastRequestAuthApiTime > 2000) {
            console.log('============== >>>> RequestAuthApi 2  ====');
            this.lastRequestAuthApiTime = nowTime;
            const msg = {
                platform: 1,
                language: cv.config.getCurrentLanguage(),
            };
            const puf = this.encodePB('RequestAuthApi', msg);
            // console.log("MTTTest RequestAuthApi 2", msg);
            return this.sendWorldMsg(puf, world_pb.MSGID.MsgID_AuthApi_Request, 0);
        }
        return false;
    }

    public ResponseAuthApi(puf: any): void {
        const Access_BL_Server: number = 231;
        const msg = this.decodePB('ResponseAuthApi', puf);
        console.log('MTTTest ResponseAuthApi', msg);
    }

    public NoticeAuthApi(puf: any): void {
        const msg = this.decodePB('NoticeAuthApi', puf);
        console.log('MTTTest NoticeAuthApi', msg);
        if (msg) {
            const url = msg.url;
            cv.dataHandler.getUserData().mtt_url = url;
            const index1 = url.indexOf('token=');
            const tempUrl = url.substring(index1);
            const index2 = tempUrl.indexOf('&');
            const token = tempUrl.substr(6, index2 - 6);
            // console.log("######################### token ====" + token);
            cv.dataHandler.getUserData().mtt_token = token;
            // console.log("match url = ", url);
            // cv.MessageCenter.send("onAuthMttSucc", msg);
        } else {
            // cv.MessageCenter.send("onAuthMttError", MTTConnector.instance.config.tokenErrorMsg.EMPTY_RESPONSE);
        }
    }

    // 自动带入中自动dsdt兑换通知
    public BuyinEventUsdtChanageNotice(puf: any) {
        const msg: ws_protocol.pb.BuyinEventUsdtChanageNotice = this.decodePB(
            'BuyinEventUsdtChanageNotice',
            puf,
        );
        if (msg) {
            const formactStr: string =
                msg.game_id == cv.Enum.GameId.Jackfruit
                    ? 'USDTView_usdt_chanage_2'
                    : 'USDTView_usdt_chanage_1';
        }
    }

    QuickRaiseRequest(value: number | string[], game_id: number, isPreFlop: boolean) {
        const msg: world_pb.QuickRaiseRequest = world_pb.QuickRaiseRequest.create();

        if (Array.isArray(value)) {
            msg.changeVals = value.slice();
            msg.whichRaise = msg.changeVals.length;
        } else {
            msg.whichRaise = value;
        }

        msg.game_id = game_id;
        msg.isPreFlop = isPreFlop;

        const puf = this.encodePB('QuickRaiseRequest', msg);
        this.sendWorldMsg(puf, world_pb.MSGID.MsgID_QuickRaise_Request, 0);
    }

    QuickRaiseResponse(puf: any) {
        const msg: world_pb.QuickRaiseResponse = this.decodePB('QuickRaiseResponse', puf);
        if (msg) {
            if (msg.error == 1) {
                cv.MessageCenter.send('quickraise', msg);
            } else {
                cv.ToastError(msg.error);
            }
        }
    }

    public requestHeartBeat(): boolean {
        //console.log("[HeartBeat] send wroldserver");
        let ip: string = cv.String(cv.dataHandler.getUserData().user_ip);
        if (ip.length <= 0) ip = '127.0.0.1';

        const kLocation: any = cv.native.GetLocation();
        const msg: world_pb.RequestHeartBeat = new world_pb.RequestHeartBeat();
        msg.position = world_pb.PositionInfo.create();
        msg.position.longtitude = kLocation.longitude;
        msg.position.latitude = kLocation.latitude;
        msg.position.ip = ip;
        msg.uid = cv.dataHandler.getUserData().u32Uid;

        const puf: any = this.encodePB('RequestHeartBeat', msg);
        return this.sendWorldMsg(puf, world_pb.MSGID.MsgID_HeartBeat_Request, 0);
    }
}
