import cv from '../cv';
import data_protocol from '../../proto/data';
import data_pb = data_protocol.data_proto;

import { aesHandler } from '../tools/aesHandler';
import { CurrencyType } from '../tools/Enum';
import { Bb100Info } from '../data/userData';
import { logging } from 'shared';
import { domainManager } from './DomainManager';
import EncryptUtils from '../../commoms/utils/encryptUtils';

export class HttpHandler {
    private static instence: HttpHandler;
    private valueCache: any = null;
    /**
     * 初始化登录信息
     */

    /**
     * 获取用户公用 token 字段
     */
    private _getUserTokenField(): object {
        let oRetValue: object = null;
        let sUID: string = cv.dataHandler.getUserData().user_id;
        let sToken: string = cv.dataHandler.getUserData().user_token;

        if (!sToken || sToken === '') {
            // sToken = cc.sys.localStorage.getItem('user_token');
            if (!sToken || sToken === '') return oRetValue;
        }

        if (!sUID || sUID === '') {
            // sUID = cc.sys.localStorage.getItem('user_id');
            if (!sUID || sUID === '') return oRetValue;
        }

        oRetValue = { token: sToken, user_id: sUID };
        return oRetValue;
    }

    /**
     * 游客登录成功
     * @param value
     */
    public onTouristLoginSuccess(value: any) {
        if (value && value.data) {
            cv.dataHandler.getUserData().download_url = value.data.download_url;
        }
        console.log('onTouristLoginSuccess****** ' + cv.config.getCurrentScene());
        let msgCode = value.msg_code;
        if (msgCode === '0') {
            let data = value.data;
            console.log(data);
            cv.dataHandler.getUserData().user_id = data.user_id;
            cv.dataHandler.getUserData().u32Uid = parseInt(data.user_id);
            cv.dataHandler.getUserData().user_token = data.token;
            this.md5token();
            cv.dataHandler.getUserData().user_ip = data.ip;
            cv.dataHandler.getUserData().mobile = cv.String(data.mobile);
            cv.dataHandler.getUserData().user_safe = data.safe;
            cv.dataHandler.getUserData().areaCode = data.areaCode;
            cv.dataHandler.getUserData().pay_type = data.pay_type;
            cv.dataHandler.getUserData().vipTool_url = data.vipTool_url;
            cv.dataHandler.getUserData().button_1 = data.button_1;
            cv.dataHandler.getUserData().button_1_english = data.button_1_english;
            cv.dataHandler.getUserData().button_2 = data.button_2;
            cv.dataHandler.getUserData().button_2_english = data.button_2_english;
            cv.dataHandler.getUserData().button_3 = data.button_3;
            cv.dataHandler.getUserData().button_3_english = data.button_3_english;
            cv.dataHandler.getUserData().shopUrl = data.shop;
            cv.dataHandler.getUserData().isEncrypt = data.encry_switch || [];
            cv.dataHandler.getUserData().club_head = data.club_head;
            cv.dataHandler.getUserData().bk_img = data.bk_img;
            cv.dataHandler.getUserData().isvpn = Boolean(data.is_vpn);
            cv.dataHandler.getUserData().isban = Boolean(data.is_ban);
            cv.dataHandler.getUserData().is_allow_update_name = Boolean(data.is_allow_update_name);
            cv.dataHandler.getUserData().file_upload_url = data.file_upload_url;
            //标记为游客登录
            cv.dataHandler.getUserData().isTouristUser = true;
            this._updateSpinStatus(data.blSpinStatus);
            let preNum = cv.config.HAVE_MTT;
            if (data.mtt_status == 2) {
                //2是维护中 1是开启
                cv.config.HAVE_MTT = false;
            } else if (data.mtt_status == 1) {
                cv.config.setMTT();
            }

            if (preNum != cv.config.HAVE_MTT) {
                cv.MessageCenter.send('update_mtt_state');
            }

            let preNumJack = cv.config.HAVE_BLACKJACK;
            if (data.black_jack_status == 2) {
                //21点，2是维护，1是开启
                cv.config.setBlackJack(false);
            } else if (data.black_jack_status == 1) {
                cv.config.setBlackJack(false);
            }
            if (preNumJack != cv.config.HAVE_BLACKJACK) {
                //如果状态有改变
                cv.MessageCenter.send('update_blackJack_state');
            }

            for (const ite of data.domain) {
                domainManager.addDomain(ite);
            }

            cv.MessageCenter.send('onLoginSuccess');

            if (this.valueCache) {
                this.valueCache = null;
            }
        } else {
            if (cv.config.getCurrentScene() == cv.Enum.SCENE.HOTUPDATE_SCENE) {
                this.valueCache = value;

                return;
            }

            this.doLoginFaile(value);
        }
    }

    public onUserNameLoginSuccess(value: any) {
        // if (value.is_upgrade && value.is_upgrade == 1) {         临时屏蔽 yyx 0910
        //     cv.MessageCenter.send("OnHttplogin");
        //     return;
        // }
        if (value && value.data) {
            cv.dataHandler.getUserData().download_url = value.data.download_url;
        }
        console.log('onUserNameLoginSuccess****** ' + cv.config.getCurrentScene());
        let msgCode = value.msg_code;
        if (msgCode === '0') {
            let data = value.data;
            console.log(data);
            console.log('onUserNameLoginSuccess');
            cv.dataHandler.getUserData().user_id = data.user_id;
            cv.dataHandler.getUserData().u32Uid = parseInt(data.user_id);
            cv.dataHandler.getUserData().user_token = data.token;
            this.md5token();
            cv.dataHandler.getUserData().user_ip = data.ip;
            cv.dataHandler.getUserData().mobile = cv.String(data.mobile);
            cv.dataHandler.getUserData().user_safe = data.safe;
            cv.dataHandler.getUserData().areaCode = data.areaCode;
            cv.dataHandler.getUserData().vipTool_url = data.vipTool_url;
            cv.dataHandler.getUserData().pay_type = data.pay_type;
            cv.dataHandler.getUserData().button_1 = data.button_1;
            cv.dataHandler.getUserData().button_1_english = data.button_1_english;
            cv.dataHandler.getUserData().button_2 = data.button_2;
            cv.dataHandler.getUserData().button_2_english = data.button_2_english;
            cv.dataHandler.getUserData().button_3 = data.button_3;
            cv.dataHandler.getUserData().button_3_english = data.button_3_english;
            cv.dataHandler.getUserData().shopUrl = data.shop;
            cv.dataHandler.getUserData().isEncrypt = data.encry_switch || [];
            cv.dataHandler.getUserData().club_head = data.club_head;
            cv.dataHandler.getUserData().bk_img = data.bk_img;
            cv.dataHandler.getUserData().isvpn = Boolean(data.is_vpn);
            cv.dataHandler.getUserData().isban = Boolean(data.is_ban);
            cv.dataHandler.getUserData().isBanDelay = false;
            cv.dataHandler.getUserData().is_allow_update_name = Boolean(data.is_allow_update_name);
            cv.dataHandler.getUserData().file_upload_url = data.file_upload_url;
            //标记为正常玩家登录
            cv.dataHandler.getUserData().isTouristUser = false;

            this._updateSpinStatus(data.blSpinStatus);
            let preNum = cv.config.HAVE_MTT;
            if (data.mtt_status == 2) {
                //2是维护中 1是开启
                cv.config.HAVE_MTT = false;
            } else if (data.mtt_status == 1) {
                cv.config.setMTT();
            }

            if (preNum != cv.config.HAVE_MTT) {
                cv.MessageCenter.send('update_mtt_state');
            }

            let preNumJack = cv.config.HAVE_BLACKJACK;
            if (data.black_jack_status == 2) {
                //21点，2是维护，1是开启
                cv.config.setBlackJack(false);
            } else if (data.black_jack_status == 1) {
                cv.config.setBlackJack(false);
            }
            if (preNumJack != cv.config.HAVE_BLACKJACK) {
                //如果状态有改变
                cv.MessageCenter.send('update_blackJack_state');
            }

            for (const ite of data.domain) {
                domainManager.addDomain(ite);
            }

            cv.MessageCenter.send('onLoginSuccess');
            if (this.valueCache) {
                this.valueCache = null;
            }
            //cv.worldNet.connectServer();
            //console.log("onLoginSuccess===》》" + "userId:" + cv.dataHandler.getUserData().user_id + " kToken:" + cv.dataHandler.getUserData().user_token + " kIp:" + cv.dataHandler.getUserData().user_ip);
        } else {
            if (cv.config.getCurrentScene() == cv.Enum.SCENE.HOTUPDATE_SCENE) {
                this.valueCache = value;

                return;
            }

            this.doLoginFaile(value);
        }
    }

    hasFaileCache(): boolean {
        return this.valueCache != null;
    }
    getFaileCache() {
        return this.valueCache;
    }
    doLoginFaile(values) {
        let value = values;
        let msgCode = value.msg_code;
        if (msgCode == '200000') {
            this.tipsMsg(value);
            return;
        }
        console.log('onUserNameLoginSuccess===》》fail');
        if (msgCode == '100033') {
            let msg;
            if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.zh_CN) {
                msg = value['msg'];
            } else {
                msg = value['message'];
            }
            cv.MessageCenter.send('login_Lock', msg);
        } else if (msgCode == '100070') {
            if (value && value.data) {
                let result = value.data;
                cv.dataHandler.getUserData().user_id = result.user_id;
                cv.dataHandler.getUserData().mobile = cv.String(result.mobile);
                cv.dataHandler.getUserData().areaCode = result.areaCode;

                cv.MessageCenter.send('goTobindDevice');
            }
        } else {
            this.tipsMsg(value);
            if (value.is_upgrade && Math.abs(value.is_upgrade) === 1) {
                return;
            }
        }
    }

    onSetAccountSuccess(value: any) {
        let msgCode = value.msg_code;
        if (msgCode === '0') {
            cv.MessageCenter.send('onSetAccountSuccess');
        }
    }

    responseCheckNickName(msg) {
        if (msg.msg_code == '0') {
            cv.MessageCenter.send('register_to_set_account');
        }
    }

    responseCheckUserName(msg) {
        if (msg.msg_code == '0') {
            cv.MessageCenter.send('register_to_user_account');
        } else {
            logging.info(msg.value);
        }
    }

    public onGetRegisterVCodeSuccess(value) {
        if (value.msg_code === '0') {
            logging.info(value.msg);
            console.log('onGetRegisterVCodeSuccess===》》');
        } else {
            console.log('onGetRegisterVCodeSuccess===》》fail');
        }

        cv.MessageCenter.send('onGetRegisterVCodeSuccess', value);
    }

    public onGetForgetVCodeSuccess(value) {
        if (value.msg_code === '0') {
            logging.info(value.msg);
            console.log('onGetForgetVCodeSuccess===》》');
        } else {
            console.log('onGetForgetVCodeSuccess===》》fail');
        }

        cv.MessageCenter.send('onGetForgetVCodeSuccess', value);
    }

    public onCheckForgetPsdSuccess(value) {
        if (value.msg_code === '0') {
            let data = value.data;
            //cv.dataHandler.getHttpData() = data;
            cv.MessageCenter.send('onCheckForgetPsdSuccess');

            console.log('onCheckForgetPsdSuccess===》》');
        } else {
            console.log('onCheckForgetPsdSuccess===》》fail');
        }
    }

    public onCheckNewPsdSuccess(value) {
        if (value.msg_code === '0') {
            let data = value.data;
            //cv.dataHandler.getHttpData() = data;
            cv.MessageCenter.send('onCheckNewPsdSuccess');

            console.log('onCheckNewPsdSuccess===》》');
        } else {
            console.log('onCheckNewPsdSuccess===》》fail');
        }
    }

    public onResetPsdSuccess(value) {
        if (value.msg_code === '0') {
            logging.info(value.msg);
            cv.MessageCenter.send('onResetPsdSuccess');
        } else {
            logging.info('modify password failed');
        }
    }

    public onTwoLevelSubmitSucc(value) {
        if (value.msg_code === '0') {
            let data = value.data;
            logging.info(value.msg);
            //cv.dataHandler.getHttpData() = data;
            // cv.MessageCenter.send("onTwoLevelSubmitSucc");
        } else {
            logging.info('onTwoLevelSubmitSucc===》》fail');
        }
    }

    public onCheckRegisterIdCodeSuccess(value) {
        this.tipsMsg(value);
        if (value.msg_code === '0') {
            // let data = value.data;
            // cv.dataHandler.getUserData().invitation_code = data.invitation_code;
            cv.MessageCenter.send('onCheckRegisterIdCodeSuccess');

            console.log('onCheckRegisterIdCodeSuccess===》》');
        } else {
            console.log('onCheckRegisterIdCodeSuccess===》》fail');
        }
    }

    public onFinishRegisterSuccess(value: any) {
        // if (value.is_upgrade && value.is_upgrade == 1) {         临时屏蔽 yyx 0910
        //     cv.MessageCenter.send("OnHttplogin");
        //     return;
        // }

        let msgCode = value.msg_code;
        if (msgCode === '0') {
            let data = value.data;
            console.log('onUserNameLoginSuccess');
            cv.dataHandler.getUserData().user_id = data.user_id;
            cv.dataHandler.getUserData().u32Uid = parseInt(data.user_id);
            cv.dataHandler.getUserData().user_token = data.token;
            this.md5token();
            cv.dataHandler.getUserData().user_ip = data.ip;
            cv.dataHandler.getUserData().mobile = cv.String(data.mobile);
            cv.dataHandler.getUserData().user_safe = data.safe;
            cv.dataHandler.getUserData().areaCode = data.areaCode;

            cv.dataHandler.getUserData().pay_type = data.pay_type;
            cv.dataHandler.getUserData().vipTool_url = data.vipTool_url;
            cv.dataHandler.getUserData().button_1 = data.button_1;
            cv.dataHandler.getUserData().button_1_english = data.button_1_english;
            cv.dataHandler.getUserData().button_2 = data.button_2;
            cv.dataHandler.getUserData().button_2_english = data.button_2_english;
            cv.dataHandler.getUserData().button_3 = data.button_3;
            cv.dataHandler.getUserData().button_3_english = data.button_3_english;
            cv.dataHandler.getUserData().shopUrl = data.shop;
            cv.dataHandler.getUserData().isEncrypt = data.encry_switch || [];
            cv.dataHandler.getUserData().download_url = data.download_url;
            cv.dataHandler.getUserData().club_head = data.club_head;
            cv.dataHandler.getUserData().bk_img = data.bk_img;
            cv.dataHandler.getUserData().isvpn = Boolean(data.is_vpn);
            cv.dataHandler.getUserData().isban = Boolean(data.is_ban);
            cv.dataHandler.getUserData().is_allow_update_name = Boolean(data.is_allow_update_name);
            cv.dataHandler.getUserData().file_upload_url = data.file_upload_url;

            this._updateSpinStatus(data.blSpinStatus);
            let preNum = cv.config.HAVE_MTT;
            if (data.mtt_status == 2) {
                //2是维护中 1是开启
                cv.config.HAVE_MTT = false;
            } else if (data.mtt_status == 1) {
                cv.config.setMTT();
            }

            if (preNum != cv.config.HAVE_MTT) {
                cv.MessageCenter.send('update_mtt_state');
            }

            let preNumJack = cv.config.HAVE_BLACKJACK;
            if (data.black_jack_status == 2) {
                //21点，2是维护，1是开启
                cv.config.setBlackJack(false);
            } else if (data.black_jack_status == 1) {
                cv.config.setBlackJack(false);
            }
            if (preNumJack != cv.config.HAVE_BLACKJACK) {
                //如果状态有改变
                cv.MessageCenter.send('update_blackJack_state');
            }

            let activity = data.activity;

            let kData = activity.act;

            for (const ite of data.domain) {
                domainManager.addDomain(ite);
            }


            //cv.worldNet.connectServer();
            //console.log("onLoginSuccess===》》" + "userId:" + cv.dataHandler.getUserData().user_id + " kToken:" + cv.dataHandler.getUserData().user_token + " kIp:" + cv.dataHandler.getUserData().user_ip);
        } else {
            console.log('onFinishRegisterSuccess===》》fail');
            this.tipsMsg(value);
        }
    }

    sureToLoginSuccess(): void {
        cv.MessageCenter.send('onLoginSuccess');
    }

    //确认重新登录
    sureToRelogin(): void {
        cv.MessageCenter.send('toRelogin');
    }


    private _onLogoutSuccess(value: any): void {
        if (value.msg_code === '0') {
            // 弹出提示
            let sTipsMsg: string = '';
            let language: string = cv.config.getCurrentLanguage();
            switch (language) {
                case cv.Enum.LANGUAGE_TYPE.zh_CN:
                    {
                        sTipsMsg = value['msg'];
                    }
                    break;
                case cv.Enum.LANGUAGE_TYPE.en_US:
                    {
                        sTipsMsg = value['message'];
                    }
                    break;

                default:
                    break;
            }

            cv.netWorkManager.Logout();
        }
    }

    public onGetNoticeSuccess(value: any) {
        if (value.msg_code === '0') {
            cv.MessageCenter.send('onGetNoticeSuccess', value);

            console.log('onGetNoticeSuccess===》》');
        } else {
            this.tipsMsg(value);
            console.log('onGetNoticeSuccess===》》 fail');
        }
    }

    public _onSetDefaultHead(value: any) {
        if (value.msg_code != '0') {
            this.tipsMsg(value);
        } else {
            let kDataRoot = value.data;
            cv.dataHandler.getUserData().headUrl = kDataRoot.avatar;
            cv.MessageCenter.send('update_info');
        }
    }

    public onGetPlayerInfoSuccess(value: any) {
        if (value.msg_code === '0') {
            let data = value.data;
            console.log('====> onGetPlayerInfoSuccess:data =' + data);
            cv.dataHandler.getUserData().user_id = data.user_id;
            cv.dataHandler.getUserData().mobile = cv.String(data.mobile);
            cv.dataHandler.getUserData().nick_name = data.nick_name;
            cv.dataHandler.getUserData().gender = cv.Number(data.gender);
            cv.dataHandler.getUserData().user_marks = data.user_marks;
            cv.dataHandler.getUserData().headUrl = data.avatar;
            cv.dataHandler.getUserData().diamond_num = cv.Number(data.diamond_num);
            cv.dataHandler.getUserData().u32Chips = cv.Number(data.user_gold);
            cv.dataHandler.getUserData().games_max = cv.Number(data.games_max);
            cv.dataHandler.getUserData().clubs_max = cv.Number(data.clubs_max);
            cv.dataHandler.getUserData().current_games = cv.Number(data.current_games);
            cv.dataHandler.getUserData().current_clubs = cv.Number(data.current_clubs);
            cv.dataHandler.getUserData().u32CardType = cv.Number(data.card_type);
            cv.dataHandler.getUserData().card_expire = cv.Number(data.card_expire);
            cv.dataHandler.getUserData().user_area = data.user_area;

            cv.MessageCenter.send('onGetPlayerInfoSuccess', data);

            console.log('onGetPlayerInfoSuccess===》》');
        } else {
            console.log('onGetPlayerInfoSuccess===》》fail');
        }
    }

    public requestUserData(
        mode: Number,
        gameid: Number,
        blind: Number = 0,
        ante: Number = 0,
        identity: number = 0,
        currency_Type: number = 0,
    ) {
        let token = cv.dataHandler.getUserData().user_token;
        let u32Uid = cv.dataHandler.getUserData().u32Uid;
        if (token == null || token == undefined) {
            console.log('请求战绩、胜率数据失败，user_token  is null');
            return;
        }
        if (cv.config.getCurrentScene() === cv.Enum.SCENE.HALL_SCENE) {
            // Use USER prefernce currency to get data
            if (cv.tools.GetStringByCCFile('USER_PREFERNCE_CURRENCY') === CurrencyType.DIAMOND.toString())
                currency_Type = CurrencyType.DIAMOND;
            else
                currency_Type =
                    cv.tools.GetStringByCCFile('USER_PREFERNCE_CURRENCY') === CurrencyType.USD.toString()
                        ? CurrencyType.USD
                        : CurrencyType.GOLD;
        }
        let obj = {
            token: token,
            uid: u32Uid,
            mode: mode,
            gameid: gameid,
            blind: blind,
            ante: ante,
            identity: identity,
            currencyType: currency_Type,
        };
        // cv.http.sendRequest(url, obj, this.responseUserData.bind(this), cv.http.HttpRequestType.POST, cv.http.HttpParseType.BOTH_ZIP);
        cv.dataNet.RequestGetData(data_pb.CMD.GET_DATA_REQ, obj, this.responseUserData.bind(this), true);
    }

    /**
     * @function 处理用户数据
     */
    public responseUserData(Value: any): void {
        const { data, jfdata, star_duration } = Value;

        if (data && data.trim().length > 0) {
            // 普通牌局
            if (data === 'decode error') {
                // 解码失败
                this.tipsMsg(Value);
            } else if (data === 'load data error') {
                // 加载数据失败；没有数据，重置所有
                cv.MessageCenter.send('RecetRoleInfoView');
            } else {
                const result = JSON.parse(data);

                if (Object.keys(result).length > 0) {
                    cv.dataHandler.getUserData().pokerdata = result;
                    cv.dataHandler.getUserData().pokerdata.star_duration = star_duration;

                    if (result.Total_win_money !== 0 && result.Total_hand_card_count !== 0) {
                        cv.dataHandler.getUserData().pokerdata.Fight_100 =
                            (result.Total_win_money / result.Total_hand_card_count) * 100;
                    } else {
                        cv.dataHandler.getUserData().pokerdata.Fight_100 = 0;
                    }

                    if (result.Total_end_room_count > 0) {
                        cv.dataHandler.getUserData().pokerdata.Fight_average =
                            result.Total_win_money / result.Total_end_room_count;
                        cv.dataHandler.getUserData().pokerdata.Buyin_average =
                            result.Total_buyin / result.Total_end_room_count;
                    } else {
                        cv.dataHandler.getUserData().pokerdata.Fight_average = 0;
                        cv.dataHandler.getUserData().pokerdata.Buyin_average = 0;
                    }

                    cv.dataHandler.getUserData().pokerdata.Bb100s = [];
                    for (let i = 0; i < result.bb_100_s.length; i++) {
                        let data: Bb100Info = new Bb100Info();
                        data.bb_value = result.bb_100_s[i].bb_value;
                        data.total_win_bb_count = result.bb_100_s[i].total_win_bb_count;
                        data.bb_100 = result.bb_100_s[i].bb_100;
                        cv.dataHandler.getUserData().pokerdata.Bb100s.push(data);
                    }
                }

                const pokerData = cv.dataHandler.getUserData().pokerdata;

                for (var i in pokerData) {
                    if (!pokerData[i] || Number.isNaN(pokerData[i])) {
                        pokerData[i] = 0;
                    }
                }

                cv.MessageCenter.send('update_userPokerData');
            }
        } else {
            // 菠萝蜜
            if (jfdata === 'decode error') {
                // 解码失败
                this.tipsMsg(Value);
            } else if (jfdata === 'load data error') {
                // 加载数据失败；没有数据，重置所有
                cv.MessageCenter.send('RecetRoleInfoView');
            } else {
                const result = JSON.parse(jfdata);

                if (Object.keys(result).length > 0) {
                    cv.dataHandler.getUserData().pokerdata = result;
                }

                const pokerData = cv.dataHandler.getUserData().pokerdata;

                for (var i in pokerData) {
                    if (!pokerData[i] || Number.isNaN(pokerData[i])) {
                        pokerData[i] = 0;
                    }
                }

                cv.MessageCenter.send('updateUserJackfruitData');
            }
        }
    }

    public static getInstance(): HttpHandler {
        if (!this.instence) {
            this.instence = new HttpHandler();
        }
        return this.instence;
    }

    private _getCurrencyType(backend_Currency_Value: number): CurrencyType {
        if (backend_Currency_Value === CurrencyType.USD || backend_Currency_Value === CurrencyType.DIAMOND)
            return backend_Currency_Value;
        return CurrencyType.GOLD; // Default value will be for GCurrencyType.GOLD   // Workaround: Backend may send 0 or 2, both are mean to gold currency
    }

    /**
     * 请求大"Pot"牌局"game uuid"
     * @param room_uuid_js
     * @param game_id_js
     * @param callBack
     */
    requestBigPotGameUUIDs(room_uuid_js: string, game_id_js: number, callBack: (value: any) => void): void {
        let uid: number = cv.dataHandler.getUserData().u32Uid;
        let token: string = cv.dataHandler.getUserData().user_token;

        let obj = {
            uid: uid,
            token: token,
            room_uuid_js: room_uuid_js,
            game_id: game_id_js,
        };
        cv.dataNet.RequestGetData(data_pb.CMD.GAME_BIG_POT_LIST_REQ, obj, callBack, true);
    }

    GetPubliceData(
        u32Uid: number,
        umode: number,
        uGameid: number,
        blind: number,
        ante: number,
        identity: number = 0,
        requestuid: number,
        currency_Type: number = 0,
    ) {
        let Token: string = cv.dataHandler.getUserData().user_token;
        if (Token.length <= 0) {
            Token = cv.tools.GetStringByCCFile('user_token');
            if (Token.length <= 0) return;
        }

        let obj = {
            uid: u32Uid,
            mode: umode,
            gameid: uGameid,
            blind: blind,
            ante: ante,
            identity: identity,
            req_uid: requestuid,
            currencyType: currency_Type,
        };
        // cv.http.sendRequest("DATA_GETPUBLICDATA", obj, this.responsePubliceData.bind(this), cv.http.HttpRequestType.POST, cv.http.HttpParseType.SEND_ZIP);
        cv.dataNet.RequestGetData(
            data_pb.CMD.GET_PUBLIC_DATA_REQ,
            obj,
            this.responsePubliceData.bind(this),
            false,
        );
    }

    responsePubliceData(Value: any) {
        if (Value['data'] == 'decode error') {
            this.tipsMsg(Value);
        } else if (Value['data'] == 'load data error') {
            //have not any data, so reset all
            console.log('have not any data, so reset all');
            //log("uid:::", kValue["UID"]);
            cv.MessageCenter.send('RecetRoleInfoView');
        } else {
            let data = Value['data'];
            let kValue = JSON.parse(data);
            let u32Uid: number = 0;

            if (kValue) {
                u32Uid = kValue['UID'];
                cv.dataHandler.getUserData().pokerdata.Total_win_money = kValue['Total_win_money'];
                cv.dataHandler.getUserData().pokerdata.Total_hand_card_count =
                    kValue['Total_hand_card_count'];
                cv.dataHandler.getUserData().pokerdata.Vpip_rate = kValue['Vpip_rate'];
                cv.dataHandler.getUserData().pokerdata.Win_rate = kValue['Win_rate'];
                cv.dataHandler.getUserData().pokerdata.Pfr_rate = kValue['Pfr_rate'];
                cv.dataHandler.getUserData().pokerdata.Af_rate = kValue['Af_rate'];
                cv.dataHandler.getUserData().pokerdata.Sb_rate = kValue['Sb_rate'];
                cv.dataHandler.getUserData().pokerdata.Etf_rate = kValue['Etf_rate'];
                cv.dataHandler.getUserData().pokerdata.Wsf_rate = kValue['Wsf_rate'];
                cv.dataHandler.getUserData().pokerdata.Wsd_rate = kValue['Wsd_rate'];
                cv.dataHandler.getUserData().pokerdata.Rate_3bet = kValue['Rate_3bet'];
                cv.dataHandler.getUserData().pokerdata.Rate_fold_to_3bet = kValue['Rate_fold_to_3bet'];
                cv.dataHandler.getUserData().pokerdata.Cbet_rate = kValue['Cbet_rate'];
                cv.dataHandler.getUserData().pokerdata.Total_enter_game_count =
                    kValue['Total_enter_game_count'];
                cv.dataHandler.getUserData().pokerdata.Enter_rate = kValue['Enter_rate'];
                cv.dataHandler.getUserData().pokerdata.star_duration = Value['star_duration'];
                cv.dataHandler.getUserData().pokerdata.liked_count = kValue['liked_count'];
                cv.dataHandler.getUserData().pokerdata.has_liked = kValue['has_liked'];
                cv.dataHandler.getUserData().pokerdata.intimacy = kValue['intimacy'];
                if (kValue['Total_hand_card_count'] != 0 && kValue['Total_win_money'] != 0) {
                    cv.dataHandler.getUserData().pokerdata.Fight_100 =
                        ((kValue['Total_win_money'] * 1.0) / kValue['Total_hand_card_count']) * 100.0;
                } else {
                    cv.dataHandler.getUserData().pokerdata.Fight_100 = 0;
                }
                cv.dataHandler.getUserData().pokerdata.Total_buyin = kValue['Total_buyin'];
                cv.dataHandler.getUserData().pokerdata.Total_end_room_count = kValue['Total_end_room_count'];
                cv.dataHandler.getUserData().pokerdata.Wtsd_rate = kValue['Wtsd_rate'];
                cv.dataHandler.getUserData().pokerdata.level_hands = kValue['level_hands'];

                if (cv.dataHandler.getUserData().pokerdata.Total_end_room_count > 0) {
                    cv.dataHandler.getUserData().pokerdata.Fight_average =
                        (cv.dataHandler.getUserData().pokerdata.Total_win_money * 1.0) /
                        cv.dataHandler.getUserData().pokerdata.Total_end_room_count;
                    cv.dataHandler.getUserData().pokerdata.Buyin_average =
                        (cv.dataHandler.getUserData().pokerdata.Total_buyin * 1.0) /
                        cv.dataHandler.getUserData().pokerdata.Total_end_room_count;
                } else {
                    cv.dataHandler.getUserData().pokerdata.Fight_average = 0;
                    cv.dataHandler.getUserData().pokerdata.Buyin_average = 0;
                }
                cv.dataHandler.getUserData().pokerdata.UID = kValue['UID'];
            }

            let pokerdata = cv.dataHandler.getUserData().pokerdata;
            for (var i in pokerdata) {
                if (!pokerdata[i] || Number.isNaN(pokerdata[i])) {
                    pokerdata[i] = 0;
                }
            }
            cv.MessageCenter.send('update_userPokerData');
        }
    }

    tipsMsg(Value: any, bFilteringCorrectHints: boolean = false) {
        if (bFilteringCorrectHints && Value['msg_code'] == '0') return;

        if (Value['msg_code'] == '100033') return;

        let msg: string = '';
        if (cv.config.getCurrentLanguage() == cv.Enum.LANGUAGE_TYPE.zh_CN) {
            msg = Value['msg'];
        } else {
            msg = Value['message'];
        }

        if (Value['msg_code'] == '100049' || Value['msg_code'] == '100028') {
        } else {
            logging.info(msg);
        }
    }


    responseCaptchaSucc(Value: any) {
        if (Value['msg_code'] == 0) {
            let kDataRoot = Value['data'];
            if (kDataRoot['type'] == 0) {
                //  0为不要图形验证码模式 1为url模式，2为base64模式
                cv.MessageCenter.send('responseCaptchaUrlSucc', 0);
            } else if (kDataRoot['type'] == 1) {
                //  1为url模式，2为base64模式
                if (kDataRoot['captcha_url'].length > 0) {
                    console.log('===>@@@@ ' + kDataRoot['captcha_url']);
                    let resource = kDataRoot['captcha_url'];
                    cv.MessageCenter.send('responseCaptchaUrlSucc', resource);
                }
            } else {
                if (kDataRoot['captcha'].length > 0) {
                    console.log('===>@@@@ ' + kDataRoot['captcha']);
                    let resource = kDataRoot['captcha'];
                    cv.MessageCenter.send('responseCaptchaSucc', resource);
                }
            }
        }
    }


    responseCheckSafe(Value: any) {
        if (Value['msg_code'] == '0') {
            cv.MessageCenter.send('SecondaryPassword_checkSafeSuccess');
        }
    }


    responseGetTwoLevelVCode(Value: any) {
        if (Value.msg_code == '0') {
            logging.info(Value.msg);
            cv.MessageCenter.send('ModifyPassword_vcode_succ');
        }
    }


    responseResetPassVCodeSucc(Value: any) {
        if (Value.msg_code == '0') {
            logging.info(Value.msg);
            cv.MessageCenter.send('ModifyPassword_vcode_succ');
        }
    }


    responseTwoLevelSubmitSucc(Value: any) {
        if (Value.msg_code == '0') {
            logging.info(Value.msg);
            cv.MessageCenter.send('ModifyPassword_OnClear', 'TwoLevelSubmitSucc');
        }
    }


    responseResetPasswordSucc(Value: any) {
        this.tipsMsg(Value);

        if (Value.msg_code == '0') {
            logging.info(Value.msg);
            cv.MessageCenter.send('ModifyPassword_OnClear', 'ResetPasswordSucc');
        }
    }


    OnModifyInfoSucc(Value: any) {
        if (Value.msg_code == '0') {
            this.tipsMsg(Value);
            // g_pkTool->ClearPng();
            let kDataRoot = Value['data'];
            if (kDataRoot['user_id']) {
                cv.dataHandler.getUserData().u32Uid = cv.Number(kDataRoot['user_id']);
            }
            if (kDataRoot['nick_name']) {
                cv.dataHandler.getUserData().nick_name = kDataRoot['nick_name'];
            }
            if (kDataRoot['gender']) {
                cv.dataHandler.getUserData().gender = kDataRoot['gender'];
            }
            if (kDataRoot['user_marks']) {
                cv.dataHandler.getUserData().user_marks = kDataRoot['user_marks'];
            } else {
                cv.dataHandler.getUserData().user_marks = '';
            }
            if (kDataRoot['avatar']) {
                cv.dataHandler.getUserData().headUrl = kDataRoot['avatar'];
            }
            if (kDataRoot['user_area']) {
                cv.dataHandler.getUserData().user_area = kDataRoot['user_area'];
            }

            cv.dataHandler.getUserData().is_allow_update_name = Boolean(kDataRoot['is_allow_update_name']);
            cv.MessageCenter.send('modify_info_succ');
        }
    }

    onUploadVarSucc(value: any) {
        console.log('==============onUploadVarSucc');
        if (value.code == '0') {
            let data: any = value['data'];
            console.log('==============');
            console.log(data.filename);
            cv.MessageCenter.send('UploadVarSuccess', data.filename);
        } else {
            logging.info(value.msg);
        }
    }

    md5token() {
        let token = cv.dataHandler.getUserData().user_token;
        let key: string = '@lnFi8' + '<eIKYazt:$_;' + 'MX9T/d(gk[JW3{Upcw';
        key = key.substring(0, 32);
        token = aesHandler.DecryptBase64(token, key);
        cv.dataHandler.getUserData().user_token = EncryptUtils.MD5(EncryptUtils.MD5(token));
    }

    getDeviceType(): string {
        let deviceType = '';
        deviceType = 'win32';

        return deviceType;
    }


    public responseGetVCodeByDevice(value) {
        this.tipsMsg(value);
        if (value.msg_code === '0') {
            logging.info(value.msg);
            cv.MessageCenter.send('BindDevice_vcode_succ');
            logging.info('responseGetVCodeByDevice===》》');
        } else {
            logging.info('responseGetVCodeByDevice===》》fail');
        }
    }


    public responseBindSafeDevice(value) {
        if (value.msg_code === '0') {
            cv.MessageCenter.send('goToLoginAccount');
            console.log('responseBindSafeDevice===》》');
        } else {
            console.log('responseBindSafeDevice===》》fail');
        }

        // cv.MessageCenter.send("responseGetVCodeByDevice", value);
    }


    //战绩列表请求成功
    private _onResponseMTTListSucced(respone: any) {
        //console.log("_onResponseMTTListSucced = " + respone);
        cv.MessageCenter.send('sendMttMatchListData', respone);
    }
    //战绩列表请求失败
    private _onResponseMTTListFailed(respone: any) {
        //console.log("_onResponseMTTListFailed = " + respone);
    }


    //战绩详情数据请求成功
    private _onResponseMTTDetailSucced(respone: any) {
        cv.MessageCenter.send('responseMTTDataDetailSuccess', respone);
        console.log('_onResponseMTTDetailSucced = ' + respone);
    }
    //战绩详情数据请求失败
    private _onResponseDetailListFailed(respone: any) {
        console.log('_onResponseMTTListFailed = ' + respone);
    }


    //战绩详情数据请求成功
    private _onResponseMTTUserInfoSucced(respone: any) {
        cv.MessageCenter.send('ResponseMTTUserInfoData', respone);
        console.log('_onResponseMTTUserInfoSucced = ' + respone);
    }
    //战绩详情数据请求失败
    private _onResponseMTTUserInfoFailed(respone: any) {
        console.log('_onResponseMTTUserInfoFailed = ' + respone);
    }

    public requestFeatureHandSubmit(
        gameUUid: number,
        game_uuid_js: string,
        gameid: number,
        roomUUId: number,
    ): void {
        const token: string = cv.dataHandler.getUserData().user_token;
        const userId = cv.dataHandler.getUserData().u32Uid;
        const obj = {
            user_id: userId,
            game_id: gameid,
            game_uuid: gameUUid,
            game_uuid_js: game_uuid_js,
            room_uuid: roomUUId,
            token: token,
        };
        cv.dataNet.RequestGetData(
            data_pb.CMD.SUBMIT_HAND_RECORD_REQ,
            obj,
            this._onFeatureHandResponse.bind(this),
        );
    }

    private _onFeatureHandResponse(response: any) {
        cv.MessageCenter.send('OnSumbitHadnResponseRecieved', response.status_code);
    }

    public requestFeatureHandConfig(gameid: number, game_uuid: number, game_uuid_js: string): void {
        const token: string = cv.dataHandler.getUserData().user_token;
        const userId: number = cv.dataHandler.getUserData().u32Uid;
        const obj = {
            user_id: userId,
            game_id: gameid,
            game_uuid: game_uuid,
            game_uuid_js: game_uuid_js,
            token: token,
        };
        cv.dataNet.RequestGetData(
            data_pb.CMD.SUBMIT_HAND_RECORD_MATCHED_RULE_REQ,
            obj,
            this._onFeatureHandConfigResponse.bind(this),
        );
    }

    private _onFeatureHandConfigResponse(response: any) {
        cv.MessageCenter.send('OnFeatureHandConfigResponse', response.status_code);
    }

    public _updateSpinStatus(status: number): void {
        const preState = cv.config.HAVE_SPIN;
        cv.config.setSPINStatus(status === 1);
        if (preState !== cv.config.HAVE_SPIN) {
            cv.MessageCenter.send('update_spin_state');
        }
    }
}
