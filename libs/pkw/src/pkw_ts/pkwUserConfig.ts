import { GameActionEnum, UserActionEnum } from './pkwUser';

interface UserConfig {
    [username: string]: {
        userActions: UserActionEnum[];
        gameActions: GameActionEnum[];
    };
}

/**
 * action example:
 * 
 * 1. [UserActionEnum.requestLobbyData, UserActionEnum.leaveRoom,
      UserActionEnum.joinRoom, UserActionEnum.sitDown, UserActionEnum.buyin],

   2. [UserActionEnum.requestLobbyData, UserActionEnum.leaveRoom],

   3. [UserActionEnum.requestLobbyData, UserActionEnum.joinRoom, UserActionEnum.sitDown, UserActionEnum.buyin],

 */
let userActionExamples = {
    buyin: [
        UserActionEnum.login,
        UserActionEnum.requestLobbyData,
        UserActionEnum.leaveRoom,
        UserActionEnum.joinRoom,
        UserActionEnum.sitDown,
        UserActionEnum.buyin,
    ],
    getLobbyData: [UserActionEnum.login, UserActionEnum.requestLobbyData],
    leaveRoom: [UserActionEnum.login, UserActionEnum.requestLobbyData, UserActionEnum.leaveRoom],
    sitDown: [
        UserActionEnum.login,
        UserActionEnum.requestLobbyData,
        UserActionEnum.leaveRoom,
        UserActionEnum.joinRoom,
        UserActionEnum.sitDown,
    ],

    // make sure user is already in room and sit down.
    sitOut: [UserActionEnum.login, UserActionEnum.requestLobbyData, UserActionEnum.sitOut],
};

export const userConfig: UserConfig = {
    a5team005: {
        userActions: userActionExamples.buyin,
        gameActions: [GameActionEnum.default],
    },
    a5team006: {
        userActions: userActionExamples.buyin,
        gameActions: [GameActionEnum.default],
    },
    a5team007: {
        userActions: userActionExamples.buyin,
        gameActions: [GameActionEnum.default],
    },
};
