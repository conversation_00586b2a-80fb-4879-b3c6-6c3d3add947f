'use strict';
var $protobuf = require('protobufjs/minimal');
var $Reader = $protobuf.Reader,
    $Writer = $protobuf.Writer,
    $util = $protobuf.util;
var $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {});
$root.gate_proto = (function () {
    var gate_proto = {};
    gate_proto.CMD = (function () {
        var valuesById = {},
            values = Object.create(valuesById);
        values[(valuesById[0] = 'CMD_DUMMY')] = 0;
        values[(valuesById[1003] = 'CONNECT_SERVER_FAILED_NOTIFY')] = 1003;
        values[(valuesById[1006] = 'SERVER_CLOSE_NOTIFY')] = 1006;
        values[(valuesById[1007] = 'SERVER_EXCEPT_NOTIFY')] = 1007;
        return values;
    })();
    gate_proto.ConnectServerFailedReason = (function () {
        var valuesById = {},
            values = Object.create(valuesById);
        values[(valuesById[0] = 'Null')] = 0;
        values[(valuesById[1] = 'NotFound')] = 1;
        values[(valuesById[2] = 'DialFailed')] = 2;
        return values;
    })();
    gate_proto.ErrorCode = (function () {
        var valuesById = {},
            values = Object.create(valuesById);
        values[(valuesById[0] = 'ErrorCode_DUMMY')] = 0;
        values[(valuesById[1] = 'OK')] = 1;
        return values;
    })();
    gate_proto.ConnectServerFailedNotify = (function () {
        function ConnectServerFailedNotify(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null) this[ks[i]] = p[ks[i]];
        }
        ConnectServerFailedNotify.prototype.ServerType = 0;
        ConnectServerFailedNotify.prototype.ServerId = 0;
        ConnectServerFailedNotify.prototype.Reason = 0;
        ConnectServerFailedNotify.create = function create(properties) {
            return new ConnectServerFailedNotify(properties);
        };
        ConnectServerFailedNotify.encode = function encode(m, w) {
            if (!w) w = $Writer.create();
            if (m.ServerType != null && Object.hasOwnProperty.call(m, 'ServerType'))
                w.uint32(8).uint32(m.ServerType);
            if (m.ServerId != null && Object.hasOwnProperty.call(m, 'ServerId'))
                w.uint32(16).uint32(m.ServerId);
            if (m.Reason != null && Object.hasOwnProperty.call(m, 'Reason')) w.uint32(24).int32(m.Reason);
            return w;
        };
        ConnectServerFailedNotify.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };
        ConnectServerFailedNotify.decode = function decode(r, l) {
            if (!(r instanceof $Reader)) r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l,
                m = new $root.gate_proto.ConnectServerFailedNotify();
            while (r.pos < c) {
                var t = r.uint32();
                switch (t >>> 3) {
                    case 1:
                        m.ServerType = r.uint32();
                        break;
                    case 2:
                        m.ServerId = r.uint32();
                        break;
                    case 3:
                        m.Reason = r.int32();
                        break;
                    default:
                        r.skipType(t & 7);
                        break;
                }
            }
            return m;
        };
        ConnectServerFailedNotify.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader)) reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };
        ConnectServerFailedNotify.verify = function verify(m) {
            if (typeof m !== 'object' || m === null) return 'object expected';
            if (m.ServerType != null && m.hasOwnProperty('ServerType')) {
                if (!$util.isInteger(m.ServerType)) return 'ServerType: integer expected';
            }
            if (m.ServerId != null && m.hasOwnProperty('ServerId')) {
                if (!$util.isInteger(m.ServerId)) return 'ServerId: integer expected';
            }
            if (m.Reason != null && m.hasOwnProperty('Reason')) {
                switch (m.Reason) {
                    default:
                        return 'Reason: enum value expected';
                    case 0:
                    case 1:
                    case 2:
                        break;
                }
            }
            return null;
        };
        ConnectServerFailedNotify.fromObject = function fromObject(d) {
            if (d instanceof $root.gate_proto.ConnectServerFailedNotify) return d;
            var m = new $root.gate_proto.ConnectServerFailedNotify();
            if (d.ServerType != null) {
                m.ServerType = d.ServerType >>> 0;
            }
            if (d.ServerId != null) {
                m.ServerId = d.ServerId >>> 0;
            }
            switch (d.Reason) {
                case 'Null':
                case 0:
                    m.Reason = 0;
                    break;
                case 'NotFound':
                case 1:
                    m.Reason = 1;
                    break;
                case 'DialFailed':
                case 2:
                    m.Reason = 2;
                    break;
            }
            return m;
        };
        ConnectServerFailedNotify.toObject = function toObject(m, o) {
            if (!o) o = {};
            var d = {};
            if (o.defaults) {
                d.ServerType = 0;
                d.ServerId = 0;
                d.Reason = o.enums === String ? 'Null' : 0;
            }
            if (m.ServerType != null && m.hasOwnProperty('ServerType')) {
                d.ServerType = m.ServerType;
            }
            if (m.ServerId != null && m.hasOwnProperty('ServerId')) {
                d.ServerId = m.ServerId;
            }
            if (m.Reason != null && m.hasOwnProperty('Reason')) {
                d.Reason =
                    o.enums === String ? $root.gate_proto.ConnectServerFailedReason[m.Reason] : m.Reason;
            }
            return d;
        };
        ConnectServerFailedNotify.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };
        return ConnectServerFailedNotify;
    })();
    gate_proto.ServerCloseNotify = (function () {
        function ServerCloseNotify(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null) this[ks[i]] = p[ks[i]];
        }
        ServerCloseNotify.prototype.ServerType = 0;
        ServerCloseNotify.prototype.ServerId = 0;
        ServerCloseNotify.prototype.CreateTime = $util.Long ? $util.Long.fromBits(0, 0, false) : 0;
        ServerCloseNotify.create = function create(properties) {
            return new ServerCloseNotify(properties);
        };
        ServerCloseNotify.encode = function encode(m, w) {
            if (!w) w = $Writer.create();
            if (m.ServerType != null && Object.hasOwnProperty.call(m, 'ServerType'))
                w.uint32(8).uint32(m.ServerType);
            if (m.ServerId != null && Object.hasOwnProperty.call(m, 'ServerId'))
                w.uint32(16).uint32(m.ServerId);
            if (m.CreateTime != null && Object.hasOwnProperty.call(m, 'CreateTime'))
                w.uint32(24).int64(m.CreateTime);
            return w;
        };
        ServerCloseNotify.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };
        ServerCloseNotify.decode = function decode(r, l) {
            if (!(r instanceof $Reader)) r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l,
                m = new $root.gate_proto.ServerCloseNotify();
            while (r.pos < c) {
                var t = r.uint32();
                switch (t >>> 3) {
                    case 1:
                        m.ServerType = r.uint32();
                        break;
                    case 2:
                        m.ServerId = r.uint32();
                        break;
                    case 3:
                        m.CreateTime = r.int64();
                        break;
                    default:
                        r.skipType(t & 7);
                        break;
                }
            }
            return m;
        };
        ServerCloseNotify.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader)) reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };
        ServerCloseNotify.verify = function verify(m) {
            if (typeof m !== 'object' || m === null) return 'object expected';
            if (m.ServerType != null && m.hasOwnProperty('ServerType')) {
                if (!$util.isInteger(m.ServerType)) return 'ServerType: integer expected';
            }
            if (m.ServerId != null && m.hasOwnProperty('ServerId')) {
                if (!$util.isInteger(m.ServerId)) return 'ServerId: integer expected';
            }
            if (m.CreateTime != null && m.hasOwnProperty('CreateTime')) {
                if (
                    !$util.isInteger(m.CreateTime) &&
                    !(m.CreateTime && $util.isInteger(m.CreateTime.low) && $util.isInteger(m.CreateTime.high))
                )
                    return 'CreateTime: integer|Long expected';
            }
            return null;
        };
        ServerCloseNotify.fromObject = function fromObject(d) {
            if (d instanceof $root.gate_proto.ServerCloseNotify) return d;
            var m = new $root.gate_proto.ServerCloseNotify();
            if (d.ServerType != null) {
                m.ServerType = d.ServerType >>> 0;
            }
            if (d.ServerId != null) {
                m.ServerId = d.ServerId >>> 0;
            }
            if (d.CreateTime != null) {
                if ($util.Long) (m.CreateTime = $util.Long.fromValue(d.CreateTime)).unsigned = false;
                else if (typeof d.CreateTime === 'string') m.CreateTime = parseInt(d.CreateTime, 10);
                else if (typeof d.CreateTime === 'number') m.CreateTime = d.CreateTime;
                else if (typeof d.CreateTime === 'object')
                    m.CreateTime = new $util.LongBits(
                        d.CreateTime.low >>> 0,
                        d.CreateTime.high >>> 0,
                    ).toNumber();
            }
            return m;
        };
        ServerCloseNotify.toObject = function toObject(m, o) {
            if (!o) o = {};
            var d = {};
            if (o.defaults) {
                d.ServerType = 0;
                d.ServerId = 0;
                if ($util.Long) {
                    var n = new $util.Long(0, 0, false);
                    d.CreateTime = o.longs === String ? n.toString() : o.longs === Number ? n.toNumber() : n;
                } else d.CreateTime = o.longs === String ? '0' : 0;
            }
            if (m.ServerType != null && m.hasOwnProperty('ServerType')) {
                d.ServerType = m.ServerType;
            }
            if (m.ServerId != null && m.hasOwnProperty('ServerId')) {
                d.ServerId = m.ServerId;
            }
            if (m.CreateTime != null && m.hasOwnProperty('CreateTime')) {
                if (typeof m.CreateTime === 'number')
                    d.CreateTime = o.longs === String ? String(m.CreateTime) : m.CreateTime;
                else
                    d.CreateTime =
                        o.longs === String
                            ? $util.Long.prototype.toString.call(m.CreateTime)
                            : o.longs === Number
                              ? new $util.LongBits(m.CreateTime.low >>> 0, m.CreateTime.high >>> 0).toNumber()
                              : m.CreateTime;
            }
            return d;
        };
        ServerCloseNotify.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };
        return ServerCloseNotify;
    })();
    gate_proto.ServerExceptNotify = (function () {
        function ServerExceptNotify(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null) this[ks[i]] = p[ks[i]];
        }
        ServerExceptNotify.create = function create(properties) {
            return new ServerExceptNotify(properties);
        };
        ServerExceptNotify.encode = function encode(m, w) {
            if (!w) w = $Writer.create();
            return w;
        };
        ServerExceptNotify.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };
        ServerExceptNotify.decode = function decode(r, l) {
            if (!(r instanceof $Reader)) r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l,
                m = new $root.gate_proto.ServerExceptNotify();
            while (r.pos < c) {
                var t = r.uint32();
                switch (t >>> 3) {
                    default:
                        r.skipType(t & 7);
                        break;
                }
            }
            return m;
        };
        ServerExceptNotify.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader)) reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };
        ServerExceptNotify.verify = function verify(m) {
            if (typeof m !== 'object' || m === null) return 'object expected';
            return null;
        };
        ServerExceptNotify.fromObject = function fromObject(d) {
            if (d instanceof $root.gate_proto.ServerExceptNotify) return d;
            return new $root.gate_proto.ServerExceptNotify();
        };
        ServerExceptNotify.toObject = function toObject() {
            return {};
        };
        ServerExceptNotify.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };
        return ServerExceptNotify;
    })();
    return gate_proto;
})();
module.exports = $root;
