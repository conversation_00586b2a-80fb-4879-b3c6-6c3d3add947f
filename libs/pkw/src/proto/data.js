/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
'use strict';

var $protobuf = require('protobufjs/minimal');

// Common aliases
var $Reader = $protobuf.Reader,
    $Writer = $protobuf.Writer,
    $util = $protobuf.util;

// Exported root namespace
var $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {});

$root.data_proto = (function () {
    /**
     * Namespace data_proto.
     * @exports data_proto
     * @namespace
     */
    var data_proto = {};

    /**
     * CMD enum.
     * @name data_proto.CMD
     * @enum {number}
     * @property {number} CMD_DUMMY=0 CMD_DUMMY value
     * @property {number} GET_DATA_REQ=60001 GET_DATA_REQ value
     * @property {number} GET_DATA_RESP=60002 GET_DATA_RESP value
     * @property {number} GET_PUBLIC_DATA_REQ=60004 GET_PUBLIC_DATA_REQ value
     * @property {number} GET_PUBLIC_DATA_RESP=60005 GET_PUBLIC_DATA_RESP value
     * @property {number} HOME_REQ=60007 HOME_REQ value
     * @property {number} HOME_RESP=60008 HOME_RESP value
     * @property {number} ROOM_RECORDS_LIST_REQ=60011 ROOM_RECORDS_LIST_REQ value
     * @property {number} ROOM_RECORDS_LIST_RESP=60012 ROOM_RECORDS_LIST_RESP value
     * @property {number} ROOM_RECORD_REQ=60014 ROOM_RECORD_REQ value
     * @property {number} ROOM_RECORD_RESP=60015 ROOM_RECORD_RESP value
     * @property {number} GAME_HAND_REQ=60017 GAME_HAND_REQ value
     * @property {number} GAME_HAND_RESP=60018 GAME_HAND_RESP value
     * @property {number} GAME_HAND_TEST_REQ=60021 GAME_HAND_TEST_REQ value
     * @property {number} GAME_HAND_TEST_RESP=60022 GAME_HAND_TEST_RESP value
     * @property {number} DO_FAVORITE_REQ=60024 DO_FAVORITE_REQ value
     * @property {number} DO_FAVORITE_RESP=60025 DO_FAVORITE_RESP value
     * @property {number} FAVORITE_HAND_REQ=60027 FAVORITE_HAND_REQ value
     * @property {number} FAVORITE_HAND_RESP=60028 FAVORITE_HAND_RESP value
     * @property {number} FAVORITE_LIST_NEW_REQ=60031 FAVORITE_LIST_NEW_REQ value
     * @property {number} FAVORITE_LIST_NEW_RESP=60032 FAVORITE_LIST_NEW_RESP value
     * @property {number} GET_BIG_BLIND_REQ=60034 GET_BIG_BLIND_REQ value
     * @property {number} GET_BIG_BLIND_RESP=60035 GET_BIG_BLIND_RESP value
     * @property {number} GET_HAS_BUYIN_REQ=60037 GET_HAS_BUYIN_REQ value
     * @property {number} GET_HAS_BUYIN_RESP=60038 GET_HAS_BUYIN_RESP value
     * @property {number} GET_ROUND_INFO_REQ=60041 GET_ROUND_INFO_REQ value
     * @property {number} GET_ROUND_INFO_RESP=60042 GET_ROUND_INFO_RESP value
     * @property {number} GET_UID_HAND_COUNT_REQ=60044 GET_UID_HAND_COUNT_REQ value
     * @property {number} GET_UID_HAND_COUNT_RESP=60045 GET_UID_HAND_COUNT_RESP value
     * @property {number} GET_HAND_COUNT_REQ=60047 GET_HAND_COUNT_REQ value
     * @property {number} GET_HAND_COUNT_RESP=60048 GET_HAND_COUNT_RESP value
     * @property {number} GET_PLAYER_LATEST_REQ=60051 GET_PLAYER_LATEST_REQ value
     * @property {number} GET_PLAYER_LATEST_RESP=60052 GET_PLAYER_LATEST_RESP value
     * @property {number} JF_GAME_HAND_REQ=60055 JF_GAME_HAND_REQ value
     * @property {number} JF_GAME_HAND_RESP=60056 JF_GAME_HAND_RESP value
     * @property {number} JF_ROOM_LIST_REQ=60057 JF_ROOM_LIST_REQ value
     * @property {number} JF_ROOM_LIST_RESP=60058 JF_ROOM_LIST_RESP value
     * @property {number} JF_GAME_UUIDS_REQ=60060 JF_GAME_UUIDS_REQ value
     * @property {number} JF_GAME_UUIDS_RESP=60061 JF_GAME_UUIDS_RESP value
     * @property {number} JF_DATA_REQ=60062 JF_DATA_REQ value
     * @property {number} JF_DATA_RESP=60063 JF_DATA_RESP value
     * @property {number} GAME_REVIEW_LIST_REQ=60064 GAME_REVIEW_LIST_REQ value
     * @property {number} GAME_REVIEW_LIST_RESP=60065 GAME_REVIEW_LIST_RESP value
     * @property {number} DELETE_FAVORITE_LIST_REQ=60068 DELETE_FAVORITE_LIST_REQ value
     * @property {number} DELETE_FAVORITE_LIST_RESP=60069 DELETE_FAVORITE_LIST_RESP value
     * @property {number} FORCE_SHOW_CARD_REQ=60071 FORCE_SHOW_CARD_REQ value
     * @property {number} FORCE_SHOW_CARD_RSP=60072 FORCE_SHOW_CARD_RSP value
     * @property {number} SEND_CARD_FUN_REQ=60074 SEND_CARD_FUN_REQ value
     * @property {number} SEND_CARD_FUN_RSP=60075 SEND_CARD_FUN_RSP value
     * @property {number} GAME_UUIDS_REQ=60077 GAME_UUIDS_REQ value
     * @property {number} GAME_UUIDS_RESP=60078 GAME_UUIDS_RESP value
     * @property {number} GAME_BIG_POT_LIST_REQ=60080 GAME_BIG_POT_LIST_REQ value
     * @property {number} GAME_BIG_POT_LIST_RSP=60081 GAME_BIG_POT_LIST_RSP value
     * @property {number} SUBMIT_HAND_RECORD_REQ=60082 SUBMIT_HAND_RECORD_REQ value
     * @property {number} SUBMIT_HAND_RECORD_RSP=60083 SUBMIT_HAND_RECORD_RSP value
     * @property {number} SUBMIT_HAND_RECORD_MATCHED_RULE_REQ=60088 SUBMIT_HAND_RECORD_MATCHED_RULE_REQ value
     * @property {number} SUBMIT_HAND_RECORD_MATCHED_RULE_RSP=60089 SUBMIT_HAND_RECORD_MATCHED_RULE_RSP value
     */
    data_proto.CMD = (function () {
        var valuesById = {},
            values = Object.create(valuesById);
        values[(valuesById[0] = 'CMD_DUMMY')] = 0;
        values[(valuesById[60001] = 'GET_DATA_REQ')] = 60001;
        values[(valuesById[60002] = 'GET_DATA_RESP')] = 60002;
        values[(valuesById[60004] = 'GET_PUBLIC_DATA_REQ')] = 60004;
        values[(valuesById[60005] = 'GET_PUBLIC_DATA_RESP')] = 60005;
        values[(valuesById[60007] = 'HOME_REQ')] = 60007;
        values[(valuesById[60008] = 'HOME_RESP')] = 60008;
        values[(valuesById[60011] = 'ROOM_RECORDS_LIST_REQ')] = 60011;
        values[(valuesById[60012] = 'ROOM_RECORDS_LIST_RESP')] = 60012;
        values[(valuesById[60014] = 'ROOM_RECORD_REQ')] = 60014;
        values[(valuesById[60015] = 'ROOM_RECORD_RESP')] = 60015;
        values[(valuesById[60017] = 'GAME_HAND_REQ')] = 60017;
        values[(valuesById[60018] = 'GAME_HAND_RESP')] = 60018;
        values[(valuesById[60021] = 'GAME_HAND_TEST_REQ')] = 60021;
        values[(valuesById[60022] = 'GAME_HAND_TEST_RESP')] = 60022;
        values[(valuesById[60024] = 'DO_FAVORITE_REQ')] = 60024;
        values[(valuesById[60025] = 'DO_FAVORITE_RESP')] = 60025;
        values[(valuesById[60027] = 'FAVORITE_HAND_REQ')] = 60027;
        values[(valuesById[60028] = 'FAVORITE_HAND_RESP')] = 60028;
        values[(valuesById[60031] = 'FAVORITE_LIST_NEW_REQ')] = 60031;
        values[(valuesById[60032] = 'FAVORITE_LIST_NEW_RESP')] = 60032;
        values[(valuesById[60034] = 'GET_BIG_BLIND_REQ')] = 60034;
        values[(valuesById[60035] = 'GET_BIG_BLIND_RESP')] = 60035;
        values[(valuesById[60037] = 'GET_HAS_BUYIN_REQ')] = 60037;
        values[(valuesById[60038] = 'GET_HAS_BUYIN_RESP')] = 60038;
        values[(valuesById[60041] = 'GET_ROUND_INFO_REQ')] = 60041;
        values[(valuesById[60042] = 'GET_ROUND_INFO_RESP')] = 60042;
        values[(valuesById[60044] = 'GET_UID_HAND_COUNT_REQ')] = 60044;
        values[(valuesById[60045] = 'GET_UID_HAND_COUNT_RESP')] = 60045;
        values[(valuesById[60047] = 'GET_HAND_COUNT_REQ')] = 60047;
        values[(valuesById[60048] = 'GET_HAND_COUNT_RESP')] = 60048;
        values[(valuesById[60051] = 'GET_PLAYER_LATEST_REQ')] = 60051;
        values[(valuesById[60052] = 'GET_PLAYER_LATEST_RESP')] = 60052;
        values[(valuesById[60055] = 'JF_GAME_HAND_REQ')] = 60055;
        values[(valuesById[60056] = 'JF_GAME_HAND_RESP')] = 60056;
        values[(valuesById[60057] = 'JF_ROOM_LIST_REQ')] = 60057;
        values[(valuesById[60058] = 'JF_ROOM_LIST_RESP')] = 60058;
        values[(valuesById[60060] = 'JF_GAME_UUIDS_REQ')] = 60060;
        values[(valuesById[60061] = 'JF_GAME_UUIDS_RESP')] = 60061;
        values[(valuesById[60062] = 'JF_DATA_REQ')] = 60062;
        values[(valuesById[60063] = 'JF_DATA_RESP')] = 60063;
        values[(valuesById[60064] = 'GAME_REVIEW_LIST_REQ')] = 60064;
        values[(valuesById[60065] = 'GAME_REVIEW_LIST_RESP')] = 60065;
        values[(valuesById[60068] = 'DELETE_FAVORITE_LIST_REQ')] = 60068;
        values[(valuesById[60069] = 'DELETE_FAVORITE_LIST_RESP')] = 60069;
        values[(valuesById[60071] = 'FORCE_SHOW_CARD_REQ')] = 60071;
        values[(valuesById[60072] = 'FORCE_SHOW_CARD_RSP')] = 60072;
        values[(valuesById[60074] = 'SEND_CARD_FUN_REQ')] = 60074;
        values[(valuesById[60075] = 'SEND_CARD_FUN_RSP')] = 60075;
        values[(valuesById[60077] = 'GAME_UUIDS_REQ')] = 60077;
        values[(valuesById[60078] = 'GAME_UUIDS_RESP')] = 60078;
        values[(valuesById[60080] = 'GAME_BIG_POT_LIST_REQ')] = 60080;
        values[(valuesById[60081] = 'GAME_BIG_POT_LIST_RSP')] = 60081;
        values[(valuesById[60082] = 'SUBMIT_HAND_RECORD_REQ')] = 60082;
        values[(valuesById[60083] = 'SUBMIT_HAND_RECORD_RSP')] = 60083;
        values[(valuesById[60088] = 'SUBMIT_HAND_RECORD_MATCHED_RULE_REQ')] = 60088;
        values[(valuesById[60089] = 'SUBMIT_HAND_RECORD_MATCHED_RULE_RSP')] = 60089;
        return values;
    })();

    data_proto.DataMessage = (function () {
        /**
         * Properties of a DataMessage.
         * @memberof data_proto
         * @interface IDataMessage
         * @property {string|null} [message] DataMessage message
         */

        /**
         * Constructs a new DataMessage.
         * @memberof data_proto
         * @classdesc Represents a DataMessage.
         * @implements IDataMessage
         * @constructor
         * @param {data_proto.IDataMessage=} [p] Properties to set
         */
        function DataMessage(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null) this[ks[i]] = p[ks[i]];
        }

        /**
         * DataMessage message.
         * @member {string} message
         * @memberof data_proto.DataMessage
         * @instance
         */
        DataMessage.prototype.message = '';

        /**
         * Creates a new DataMessage instance using the specified properties.
         * @function create
         * @memberof data_proto.DataMessage
         * @static
         * @param {data_proto.IDataMessage=} [properties] Properties to set
         * @returns {data_proto.DataMessage} DataMessage instance
         */
        DataMessage.create = function create(properties) {
            return new DataMessage(properties);
        };

        /**
         * Encodes the specified DataMessage message. Does not implicitly {@link data_proto.DataMessage.verify|verify} messages.
         * @function encode
         * @memberof data_proto.DataMessage
         * @static
         * @param {data_proto.IDataMessage} m DataMessage message or plain object to encode
         * @param {$protobuf.Writer} [w] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        DataMessage.encode = function encode(m, w) {
            if (!w) w = $Writer.create();
            if (m.message != null && Object.hasOwnProperty.call(m, 'message')) w.uint32(10).string(m.message);
            return w;
        };

        /**
         * Encodes the specified DataMessage message, length delimited. Does not implicitly {@link data_proto.DataMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof data_proto.DataMessage
         * @static
         * @param {data_proto.IDataMessage} message DataMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        DataMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a DataMessage message from the specified reader or buffer.
         * @function decode
         * @memberof data_proto.DataMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} r Reader or buffer to decode from
         * @param {number} [l] Message length if known beforehand
         * @returns {data_proto.DataMessage} DataMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        DataMessage.decode = function decode(r, l) {
            if (!(r instanceof $Reader)) r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l,
                m = new $root.data_proto.DataMessage();
            while (r.pos < c) {
                var t = r.uint32();
                switch (t >>> 3) {
                    case 1:
                        m.message = r.string();
                        break;
                    default:
                        r.skipType(t & 7);
                        break;
                }
            }
            return m;
        };

        /**
         * Decodes a DataMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof data_proto.DataMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {data_proto.DataMessage} DataMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        DataMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader)) reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a DataMessage message.
         * @function verify
         * @memberof data_proto.DataMessage
         * @static
         * @param {Object.<string,*>} m Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        DataMessage.verify = function verify(m) {
            if (typeof m !== 'object' || m === null) return 'object expected';
            if (m.message != null && m.hasOwnProperty('message')) {
                if (!$util.isString(m.message)) return 'message: string expected';
            }
            return null;
        };

        /**
         * Creates a DataMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof data_proto.DataMessage
         * @static
         * @param {Object.<string,*>} d Plain object
         * @returns {data_proto.DataMessage} DataMessage
         */
        DataMessage.fromObject = function fromObject(d) {
            if (d instanceof $root.data_proto.DataMessage) return d;
            var m = new $root.data_proto.DataMessage();
            if (d.message != null) {
                m.message = String(d.message);
            }
            return m;
        };

        /**
         * Creates a plain object from a DataMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof data_proto.DataMessage
         * @static
         * @param {data_proto.DataMessage} m DataMessage
         * @param {$protobuf.IConversionOptions} [o] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        DataMessage.toObject = function toObject(m, o) {
            if (!o) o = {};
            var d = {};
            if (o.defaults) {
                d.message = '';
            }
            if (m.message != null && m.hasOwnProperty('message')) {
                d.message = m.message;
            }
            return d;
        };

        /**
         * Converts this DataMessage to JSON.
         * @function toJSON
         * @memberof data_proto.DataMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        DataMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        return DataMessage;
    })();

    return data_proto;
})();

module.exports = $root;
