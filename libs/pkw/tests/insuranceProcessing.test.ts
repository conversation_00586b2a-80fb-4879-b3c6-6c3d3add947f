import { mock, test } from 'node:test';
import assert from 'node:assert';
import {
    calculateInsuranceAmount,
    LOW_OUTS_COUNT,
    MEDIUM_OUTS_COUNT,
    HIGH_OUTS_COUNT,
    SERVER_COVERAGE_AMOUNT,
} from '../src/pkw_ts/tools/insuranceProcessing';

// Mock the logging module to avoid dependencies
mock.method(console, 'log', mock.fn());

test('shouldBuyInsurance probability increases with higher outsCount', async () => {
    const testCases = [
        { outsCount: 1, expectedProbability: 0.1 },
        { outsCount: LOW_OUTS_COUNT, expectedProbability: 0.1 },
        { outsCount: 5, expectedProbability: 0.25 },
        { outsCount: MEDIUM_OUTS_COUNT, expectedProbability: 0.25 },
        { outsCount: 10, expectedProbability: 0.35 },
        { outsCount: HIGH_OUTS_COUNT, expectedProbability: 0.35 },
        { outsCount: 15, expectedProbability: 0.55 },
        { outsCount: 20, expectedProbability: 0.55 },
    ];

    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;

    for (const testCase of testCases) {
        // Test with random value just below probability (should buy)
        Math.random = () => testCase.expectedProbability - 0.01;
        let result = calculateInsuranceAmount(testCase.outsCount, investmentAmount, coverageLimit);
        assert.ok(
            result !== 0 && typeof result === 'number',
            `Expected to buy insurance for outsCount ${testCase.outsCount} with probability ${testCase.expectedProbability}`,
        );

        // Test with random value just above probability (should not buy)
        Math.random = () => testCase.expectedProbability + 0.01;
        result = calculateInsuranceAmount(testCase.outsCount, investmentAmount, coverageLimit);
        assert.strictEqual(
            result,
            0,
            `Expected NOT to buy insurance for outsCount ${testCase.outsCount} with probability ${testCase.expectedProbability}`,
        );
    }

    Math.random = originalRandom;
});

test('calculateInsuranceAmount - LOW outsCount should prefer smaller coverage amounts', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;
    const results: number[] = [];

    // Force buying insurance and collect insurance amounts
    Math.random = () => 0; // Always buy insurance

    // Run multiple times to test distribution
    for (let i = 0; i < 100; i++) {
        const result = calculateInsuranceAmount(LOW_OUTS_COUNT, investmentAmount, coverageLimit);
        if (result !== 0) {
            results.push(result);
        }
    }

    // Verify that only expected insurance amounts are returned for LOW outsCount
    const expectedAmounts = [
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.LOW / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_LOW / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH / 100)),
    ];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected insurance amount ${amount} for LOW outsCount`,
        );
    }

    Math.random = originalRandom;
});

test('calculateInsuranceAmount - MEDIUM outsCount should include balanced coverage', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;
    const results: number[] = [];

    Math.random = () => 0; // Always buy insurance

    for (let i = 0; i < 100; i++) {
        const result = calculateInsuranceAmount(MEDIUM_OUTS_COUNT, investmentAmount, coverageLimit);
        if (result !== 0) {
            results.push(result);
        }
    }

    const expectedAmounts = [
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.LOW / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_LOW / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.BREAK_EAVEN / 100)),
    ];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected insurance amount ${amount} for MEDIUM outsCount`,
        );
    }

    Math.random = originalRandom;
});

test('calculateInsuranceAmount - HIGH outsCount should prefer medium to high coverage', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;
    const results: number[] = [];

    Math.random = () => 0; // Always buy insurance

    for (let i = 0; i < 100; i++) {
        const result = calculateInsuranceAmount(HIGH_OUTS_COUNT, investmentAmount, coverageLimit);
        if (result !== 0) {
            results.push(result);
        }
    }

    const expectedAmounts = [
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_LOW / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.BREAK_EAVEN / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.FULL_POT / 100)),
    ];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected insurance amount ${amount} for HIGH outsCount`,
        );
    }

    Math.random = originalRandom;
});

test('calculateInsuranceAmount - Very HIGH outsCount should prefer higher coverage amounts', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;
    const results: number[] = [];

    Math.random = () => 0; // Always buy insurance

    for (let i = 0; i < 100; i++) {
        const result = calculateInsuranceAmount(15, investmentAmount, coverageLimit); // outsCount > HIGH
        if (result !== 0) {
            results.push(result);
        }
    }

    const expectedAmounts = [
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.BREAK_EAVEN / 100)),
        Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.FULL_POT / 100)),
    ];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected insurance amount ${amount} for very HIGH outsCount`,
        );
    }

    Math.random = originalRandom;
});

test('calculateInsuranceAmount - coverage limit functionality', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 1; // Non-zero coverage limit

    Math.random = () => 0; // Always buy insurance

    // Test that coverage limit is applied
    const result = calculateInsuranceAmount(HIGH_OUTS_COUNT, investmentAmount, coverageLimit);

    if (result !== 0) {
        const maxAllowedAmount = Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH / 100));
        assert.ok(
            result <= maxAllowedAmount,
            `Insurance amount ${result} should not exceed coverage limit of ${maxAllowedAmount}`,
        );
    }

    Math.random = originalRandom;
});

test('calculateInsuranceAmount - zero coverage limit bypasses limit', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0; // Zero coverage limit should bypass the limit

    Math.random = () => 0; // Always buy insurance

    // Test that zero coverage limit allows any amount
    const result = calculateInsuranceAmount(HIGH_OUTS_COUNT, investmentAmount, coverageLimit);

    if (result !== 0) {
        // Should be able to get amounts higher than MEDIUM_HIGH when coverage limit is 0
        const mediumHighAmount = Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH / 100));
        // We can't guarantee it will be higher, but it should be a valid amount
        assert.ok(result > 0, `Insurance amount should be positive when buying`);
    }

    Math.random = originalRandom;
});

test('calculateInsuranceAmount - investment amount calculation', async () => {
    const originalRandom = Math.random;
    const coverageLimit = 0;

    Math.random = () => 0; // Always buy insurance

    // Test different investment amounts
    const testAmounts = [100, 500, 1000, 2000];

    for (const investmentAmount of testAmounts) {
        const result = calculateInsuranceAmount(MEDIUM_OUTS_COUNT, investmentAmount, coverageLimit);

        if (result !== 0) {
            // Result should be proportional to investment amount
            assert.ok(result > 0, `Insurance amount should be positive for investment ${investmentAmount}`);
            assert.ok(result <= investmentAmount, `Insurance amount should not exceed investment amount`);

            // Should be a reasonable percentage of investment
            const percentage = (result / investmentAmount) * 100;
            assert.ok(
                percentage >= SERVER_COVERAGE_AMOUNT.LOW && percentage <= SERVER_COVERAGE_AMOUNT.FULL_POT,
                `Insurance percentage ${percentage}% should be within expected range`,
            );
        }
    }

    Math.random = originalRandom;
});

test('Edge cases - boundary values for outsCount', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;

    // Test exact boundary values
    const boundaryTests = [
        { outsCount: 0, description: 'zero outsCount' },
        { outsCount: LOW_OUTS_COUNT, description: 'exactly LOW threshold' },
        { outsCount: LOW_OUTS_COUNT + 1, description: 'just above LOW threshold' },
        { outsCount: MEDIUM_OUTS_COUNT, description: 'exactly MEDIUM threshold' },
        { outsCount: MEDIUM_OUTS_COUNT + 1, description: 'just above MEDIUM threshold' },
        { outsCount: HIGH_OUTS_COUNT, description: 'exactly HIGH threshold' },
        { outsCount: HIGH_OUTS_COUNT + 1, description: 'just above HIGH threshold' },
        { outsCount: 100, description: 'very large outsCount' },
    ];

    for (const test of boundaryTests) {
        // Test that function doesn't throw errors
        Math.random = () => 0; // Always buy
        const result = calculateInsuranceAmount(test.outsCount, investmentAmount, coverageLimit);
        assert.ok(
            result === 0 || typeof result === 'number',
            `Should return 0 or number for ${test.description}`,
        );

        if (result !== 0) {
            assert.ok(
                typeof result === 'number',
                `Should have numeric amount when buying for ${test.description}`,
            );
            assert.ok(result > 0, `Amount should be positive for ${test.description}`);
        }
    }

    Math.random = originalRandom;
});

test('Edge cases - negative outsCount should be handled gracefully', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;
    Math.random = () => 0; // Always buy

    const result = calculateInsuranceAmount(-5, investmentAmount, coverageLimit);

    // Should not throw error and should return valid result
    assert.ok(result === 0 || typeof result === 'number');

    Math.random = originalRandom;
});

test('Weighted random selection - should respect weight distribution', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;

    // Test that weighted selection works correctly by controlling random values
    const testCases = [
        { randomValue: 0.1, outsCount: LOW_OUTS_COUNT },
        { randomValue: 0.6, outsCount: LOW_OUTS_COUNT },
        { randomValue: 0.9, outsCount: LOW_OUTS_COUNT },
    ];

    for (const testCase of testCases) {
        Math.random = () => testCase.randomValue;
        const result = calculateInsuranceAmount(testCase.outsCount, investmentAmount, coverageLimit);

        if (result !== 0) {
            // Note: Due to the weighted random algorithm, we can't predict exact values
            // but we can verify the result is within expected insurance amounts
            const validAmounts = [
                Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.LOW / 100)),
                Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_LOW / 100)),
                Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH / 100)),
                Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.BREAK_EAVEN / 100)),
                Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.FULL_POT / 100)),
            ];
            assert.ok(
                validAmounts.includes(result),
                `Insurance amount ${result} should be a valid insurance amount`,
            );
        }
    }

    Math.random = originalRandom;
});

test('Weighted random selection - fallback mechanism', async () => {
    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;

    // Test edge case where random threshold might not match any weight
    Math.random = () => 0.999999; // Very high random value

    const result = calculateInsuranceAmount(LOW_OUTS_COUNT, investmentAmount, coverageLimit);

    if (result !== 0) {
        const validAmounts = [
            Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.LOW / 100)),
            Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_LOW / 100)),
            Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH / 100)),
            Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.BREAK_EAVEN / 100)),
            Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.FULL_POT / 100)),
        ];
        assert.ok(
            validAmounts.includes(result),
            'Should return valid insurance amount even with edge case random value',
        );
    }

    Math.random = originalRandom;
});

test('Insurance probability validation - correct probabilities for each range', async () => {
    // This test validates that the probability logic matches the memory about opponent outs
    // Higher opponent outs should increase insurance buy probability

    const originalRandom = Math.random;
    const investmentAmount = 1000;
    const coverageLimit = 0;

    // Test with a fixed random value that's between probabilities
    const testRandomValue = 0.3; // Between 0.25 and 0.35
    Math.random = () => testRandomValue;

    const testCases = [
        { outsCount: 1, expectedBuy: false }, // 0.1 probability < 0.3
        { outsCount: LOW_OUTS_COUNT, expectedBuy: false }, // 0.1 probability < 0.3
        { outsCount: 5, expectedBuy: false }, // 0.25 probability < 0.3
        { outsCount: MEDIUM_OUTS_COUNT, expectedBuy: false }, // 0.25 probability < 0.3
        { outsCount: 10, expectedBuy: true }, // 0.35 probability > 0.3
        { outsCount: HIGH_OUTS_COUNT, expectedBuy: true }, // 0.35 probability > 0.3
        { outsCount: 15, expectedBuy: true }, // 0.55 probability > 0.3
    ];

    for (const testCase of testCases) {
        const result = calculateInsuranceAmount(testCase.outsCount, investmentAmount, coverageLimit);
        const actualBuy = result !== 0;
        assert.strictEqual(
            actualBuy,
            testCase.expectedBuy,
            `For outsCount ${testCase.outsCount}, expected buy decision to be ${testCase.expectedBuy} (higher outs should increase buy probability)`,
        );
    }

    Math.random = originalRandom;
});
