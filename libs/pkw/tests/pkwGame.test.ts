import { mock, test } from 'node:test';
import assert from 'node:assert';
import pkwGame from '../src/pkw_ts/pkwGame';
import { protocol as game_pb } from '../src/proto/gs_protocol';

test('pkwGame calculates greatestBet', async () => {
	pkwGame['_greatestBet'] = 199;
	await pkwGame['OnActionTurn']({
		players: [
			{playerid: 1, name: 'p1', round_bet: 100},
			// {playerid: 2, name: 'p2', round_bet: 200},
		]
	} as game_pb.NoticePlayerActionTurn);

	assert.equal(pkwGame['_greatestBet'], 100);

	await pkwGame['OnActionTurn']({
		players: [
			{playerid: 1, name: 'p1', round_bet: 100},
			{playerid: 2, name: 'p2', round_bet: 200},
		]
	} as game_pb.NoticePlayerActionTurn);

	assert.equal(pkwGame['_greatestBet'], 200);
})
