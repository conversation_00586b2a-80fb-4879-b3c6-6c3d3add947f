import { startUnleash, Unleash, PayloadType } from 'unleash-client';

let unleash: Unleash | undefined;

async function init(): Promise<void> {
    if (unleash) {
        return;
    }

    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'local') {
        unleash = {
            getVariant: () => undefined,
            isEnabled: () => false,
            destroy: () => Promise.resolve(),
        } as unknown as Unleash; // Mock Unleash instance
    } else {
        unleash = await startUnleash({
            url: process.env.UNLEASH_API_URL,
            appName: 'worker',
            customHeaders: {
                Authorization: process.env.UNLEASH_API_TOKEN,
            },
        });
    }
}

async function close(): Promise<void> {
    if (unleash) {
        unleash.destroy();
    }
}

// Get feature flag value
export async function featureFlagValue(key: string, context: any, defaultValue: any): Promise<any> {
    if (!unleash) {
        await init();
    }
    const variant = unleash.getVariant(key, context);
    if (variant && variant.enabled) {
        switch (variant.payload.type) {
            case PayloadType.STRING:
                return variant.payload.value || defaultValue;
            case PayloadType.JSON:
                try {
                    return JSON.parse(variant.payload.value) || defaultValue;
                } catch (e) {
                    console.error(`Error parsing JSON payload for feature flag ${key}:`, e);
                    return defaultValue;
                }
            case PayloadType.NUMBER:
                const numValue = Number(variant.payload.value);
                return isNaN(numValue) ? defaultValue : numValue;
            default:
                return defaultValue;
        }
    }
    return defaultValue;
}

process.on('exit', async () => {
    await close();
});
