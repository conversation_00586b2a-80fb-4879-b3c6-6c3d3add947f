{"name": "shared", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "tsx --test", "build": "tsc && npm run cp_other_files", "cp_other_files": "cpx 'src/**/*.{js,d.ts,proto,json,csv}' dist"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^22.15.3", "cpx2": "^8.0.0", "prettier": "^3.5.3", "typescript": "^5.8.3", "tsx": "^4.20.3"}, "dependencies": {"bullmq": "^5.51.1", "csv-parser": "^3.2.0", "murmurhash": "^2.0.1", "unleash-client": "^6.6.0"}}