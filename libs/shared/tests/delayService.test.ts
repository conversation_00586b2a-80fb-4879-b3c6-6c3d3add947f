import { SHARED_ACTION, GAME_PHASE, GameMode } from '../src/constants';
import { DelayService, timeBucketLimit } from '../src/delayService';
import { describe, test } from 'node:test';
import assert from 'node:assert';

const actions = [
    SHARED_ACTION.CHECK,
    SHARED_ACTION.CALL,
    SHARED_ACTION.BET,
    SHARED_ACTION.FOLD,
    SHARED_ACTION.RAISE,
    SHARED_ACTION.ALL_IN,
];

const phases = Object.values(GAME_PHASE).filter((v) => typeof v === 'number') as number[];

describe('Delay service', async () => {
    for await (const gameType of [GameMode.NORMAL, GameMode.SPLASH, GameMode.ZOOM]) {

        const delayService = new DelayService(gameType);
        const delayData = await delayService.getDelayData();
        await test(`should initialize delay ${gameType}`, async () => {
            for (const action of actions) {
                for (const phase of phases) {

                    const phaseData = delayData[action][phase];
                    assert.equal(phaseData.length, timeBucketLimit[gameType]);
                    assert.ok(phaseData[0] < 1);
                    for (let i = 1; i < phaseData.length; i++) {
                        assert.ok(phaseData[i] > phaseData[i - 1]);
                    }
                    assert.ok(phaseData.at(-1) <= 1);
                }
            }
        });

        await test(`should calculate delays within valid range ${gameType}`, async () => {
            for (const action of actions) {
                for (const phase of phases) {
                    const delay = delayService.calculateDelayMs(phase, action);
                    assert.ok(delay >= 500);
                    assert.ok(delay <= 15 * 1000);
                }
            }
        });

        await test(`should calculate a delay for certain actions above probability thresholds ${gameType}`, async () => {
            for (const phase of phases) {
                const checkDelay = delayService.calculateDelayMs(phase, SHARED_ACTION.CHECK, 0.6);
                assert.ok(checkDelay >= 0);
                assert.ok(checkDelay <= 15 * 1000);

                const foldDelay = delayService.calculateDelayMs(phase, SHARED_ACTION.FOLD, 0.9);
                assert.ok(foldDelay >= 0);
                assert.ok(foldDelay <= (15 * 1000));
            }
        });

    }

});
