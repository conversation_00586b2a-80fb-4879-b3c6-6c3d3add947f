var W3CWebSocket = require('websocket').w3cwebsocket;

const protobufJs = require('protobufjs/minimal');

const blcommonProto = require('./bl/pb/blcommonProto.js').commonProto;
const commonProto = require('./mtt/pb/commonProto.js').commonProto;
const holdem = require('./mtt/pb/holdem.js').holdem;
const mttPro = require('./mtt/pb/mtt.js').mttPro;

const WEBSOCKET_TYPE = require('./websocket-type.js');
const WEBSOCKET_EVENT_ID = require('./websocket-event-id.js');
const Config = require('./config.js');
const { logging } = require('shared');

class CustomWebsocket {
    ws;

    phone = 0;
    pbIds;
    messageSet;
    type;
    tag;

    pbMessages = {};
    messageHandler = {};
    websocketUrl = 'ws://47.76.215.147:3001/world';

    hasVerifyToken = false;

    waitingPong = false;
    pingInv;
    pongInv;
    tryPongTime = 0;
    pingMessageData = new Uint8Array([0, 0, 0, 6, 0, 3]);
    lastAction = 0;
    constructor(type, phone) {
        // console.log("constructor");
        this.type = type;
        this.phone = phone;
        this.pbIds =
            type == WEBSOCKET_TYPE.BL_WORLD
                ? blcommonProto.SocketMessageId
                : type == WEBSOCKET_TYPE.WORLD
                  ? commonProto.SocketMessageId
                  : { ...holdem.MessageId, ...mttPro.MessageId };
        this.messageSet =
            type == WEBSOCKET_TYPE.BL_WORLD
                ? blcommonProto
                : type == WEBSOCKET_TYPE.WORLD
                  ? commonProto
                  : { ...holdem, ...mttPro, MessageId: { ...holdem.MessageId, ...mttPro.MessageId } };
        this.tag = type == WEBSOCKET_TYPE.BL_WORLD ? 'bl' : type == WEBSOCKET_TYPE.WORLD ? 'world' : 'game';
        this.websocketUrl =
            type == WEBSOCKET_TYPE.BL_WORLD
                ? `${Config.blWorld}/world`
                : type == WEBSOCKET_TYPE.WORLD
                  ? `${Config.mttWorld}/world`
                  : `${Config.mttGame}/mtt/holdem`;

        Object.keys(this.messageSet).forEach((k) => {
            if (this.pbIds[k] !== undefined) {
                this.pbMessages[this.pbIds[k]] = this.messageSet[k];
            }
        });
    }

    intFromBytes(bytes) {
        var val = 0;
        for (var i = 0; i < bytes.length; ++i) {
            val += bytes[i];
            if (i < bytes.length - 1) {
                val = val << 8;
            }
        }
        return val;
    }

    bytesFromInt(int, len) {
        const bytes = new Uint8Array(len);
        var i = len;
        do {
            bytes[--i] = int & 255;
            int = int >> 8;
        } while (i);
        return bytes;
    }

    makeArray(msgBody, msgId) {
        const tmp = new Uint8Array(msgBody.byteLength + 6);
        tmp.set(this.bytesFromInt(msgBody.byteLength + 6, 4), 0);
        tmp.set(this.bytesFromInt(msgId, 2), 4);
        tmp.set(msgBody, 6);
        return tmp;
    }

    send(msgId, data) {
        const message = this.pbMessages[msgId];
        const msg = message.create(data);
        const buff_content = message.encode(msg).finish();
        logging.info(`${this.tag} => ${msgId}`, data);
        try {
            this?.ws?.send(this.makeArray(buff_content, msgId));
        } catch (error) {
            console.log(error);
        }
    }

    addMessageHandler(msgId, msg) {
        // console.log("addMessageHandler before", msgId, this.messageHandler[msgId]);
        if (this.messageHandler[msgId] === undefined) {
            this.messageHandler[msgId] = [];
        }
        this.messageHandler[msgId].push(msg);
        // console.log("addMessageHandler after", msgId, this.messageHandler[msgId].length);
    }

    removeMessageHandler(msgId, handler) {
        if (this.messageHandler[msgId]) {
            if (handler) {
                const seek = this.messageHandler[msgId].indexOf(handler);
                if (seek != -1) {
                    this.messageHandler[msgId].splice(seek, 1);
                }
            } else {
                this.messageHandler[msgId] = [];
            }
        }
    }

    onCallHandler(msgId, msg) {
        if (this.messageHandler[msgId]) {
            // const {messageHandler} = this;
            let remainHandlers = [];
            // console.log("messageHandler", msgId, this.messageHandler[msgId].length);
            this.messageHandler[msgId].forEach((handler) => {
                // console.log("onCallHandler", msgId);
                if (handler(msg) !== true) {
                    remainHandlers.push(handler);
                }
            });
            this.messageHandler[msgId] = remainHandlers;
        }
    }

    onopen = () => {
        console.log(this.tag, 'onopen', this.phone);
        // this.keepPing();
        const msgId = WEBSOCKET_EVENT_ID.ON_CONNECTED;
        this.onCallHandler(msgId);
    };

    onclose = (event) => {
        console.log(this.tag, 'onclose', event);
        this.close();
        const msgId = WEBSOCKET_EVENT_ID.ON_CLOSE;
        this.onCallHandler(msgId);
    };

    onerror = (event) => {
        console.log(this.tag, 'onerror', event);
        this.close();
        const msgId = WEBSOCKET_EVENT_ID.ON_CLOSE;
        this.onCallHandler(msgId);
    };

    onmessage = (e) => {
        const buff = new Uint8Array(e.data);
        const len = this.intFromBytes(buff.slice(0, 4));
        const msgId = this.intFromBytes(buff.slice(4, 6));

        // console.log(this.tag, "onmessage", this.phone, msgId);

        if (msgId == 4) {
            this.waitingPong = false;
            clearInterval(this.pongInv);
            return;
        }

        if (len !== buff.length) {
            return;
        }
        const pbObj = this.pbMessages[msgId];
        if (pbObj) {
            const msg = pbObj.decode(buff.slice(6));
            logging.info(`${this.tag} <= ${msgId}`, msg);
            this.onCallHandler(msgId, msg);
        }
    };

    connect() {
        this.forceClose();
        this.ws = new W3CWebSocket(this.websocketUrl);

        this.ws.onerror = this.onerror;
        this.ws.onopen = this.onopen;
        this.ws.onclose = this.onclose;
        this.ws.onmessage = this.onmessage;
    }

    close() {
        clearInterval(this.pingInv);
        clearInterval(this.pongInv);
        if (this.ws) {
            // this.ws.onerror = null;
            // this.ws.onopen = null;
            // this.ws.onclose = null;
            // this.ws.onmessage = null;
            this.ws.close();
        }
    }

    forceClose() {
        if (this.ws) {
            this.ws.onerror = null;
            this.ws.onopen = null;
            this.ws.onclose = null;
            this.ws.onmessage = null;
        }
        this.close();
    }

    keepPing() {
        clearInterval(this.pingInv);
        clearInterval(this.pongInv);
        this.pingInv = setInterval(() => {
            if (Date.now() > this.lastAction + 5000 - 1) {
                this.lastAction = Date.now();
                try {
                    this?.ws?.send(this.pingMessageData);
                } catch (error) {
                    console.log(error);
                }
                // console.log('ping ',this.ws.url);
                this.waitingPong = true;
                this.tryPongTime = 0;
                this.pongInv = setInterval(() => {
                    if (this.waitingPong) {
                        this.tryPongTime++;
                        console.log(this.tag, 'wait for pong', this.phone, this.tryPongTime);
                        if (this.tryPongTime >= 2) {
                            this.tryPongTime = 0;
                            this.waitingPong = false;
                            console.log(this.tag, 'pong lost', this.phone, this?.ws?.url);
                            clearInterval(this.pongInv);
                            this.connect();
                        }
                    }
                }, 1000);
            }
        }, 1000);
    }
}

module.exports = CustomWebsocket;
