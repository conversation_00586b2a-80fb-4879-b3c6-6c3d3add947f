const blcommonProto = require('./bl/pb/blcommonProto.js').commonProto;
const blmodelProto = require('./bl/pb/blcommonProto.js').modelProto;
const commonProto = require('./mtt/pb/commonProto.js').commonProto;
const holdem = require('./mtt/pb/holdem.js').holdem;
const mttPro = require('./mtt/pb/mtt.js').mttPro;
const CustomWebsocket = require('./websocket.js');
const WEBSOCKET_TYPE = require('./websocket-type.js');
const WEBSOCKET_EVENT_ID = require('./websocket-event-id.js');
const CONNECTION_STATUS = require('./connection-status.js');
const XMLHttpRequest = require('xmlhttprequest').XMLHttpRequest;
const { UnrecoverableError } = require('bullmq');
const axios = require('axios');
const Config = require('./config.js');
const { HttpApis } = require('./httpApis.js');
const { logging } = require('shared');

const reconnectInterval = 3000;

class BasicPlayer {
    websocket;
    connectionStatus = 0;
    phone;
    error = '';
    dataManager = {};
    tag = '';
    autoPlay;
    constructor(phone, dataManager, type, autoPlay) {
        this.phone = phone;
        this.dataManager = dataManager;
        this.autoPlay = autoPlay;
        this.websocket = new CustomWebsocket(type, phone);
    }

    connect = () => {
        logging.info(`${this.tag} player try to connect`);
        if (this.autoPlay) {
            this.unbindMessageHandler();
            this.bindMessageHandler();
        }
        this.websocket.connect();
    };

    close() {
        if (this.websocket) {
            this.websocket.forceClose();
        }
        this.setConnectionStatus(CONNECTION_STATUS.STOP);
    }

    setConnectionStatus(status) {
        this.connectionStatus = status;
    }

    setError(error) {
        this.error = error;
    }

    onConnected = () => {
        logging.info(`${this.tag} Player onConnected ${this.phone}`);
        this.setConnectionStatus(CONNECTION_STATUS.CONNECTED);
        this.requestUserLogin();
    };

    onDisconnected = () => {
        logging.info(`${this.tag} Player onDisconnected ${this.phone}`);
        this.setConnectionStatus(CONNECTION_STATUS.NONE);
        setTimeout(() => {
            this.websocket.connect();
        }, reconnectInterval);
    };

    onUserInfoUpdated = (msg) => {
        if (this.dataManager) {
            this.dataManager.userData = msg.UserData;
        }
    };

    onUserLoginResponse = (msg) => {};

    requestUserLogin() {}

    bindMessageHandler = () => {};

    unbindMessageHandler = () => {};
}

class BlWorldPlayer extends BasicPlayer {
    areaCode;
    password;
    userRegisterTimeout;
    constructor(phone, password, areaCode, dataManager, autoPlay = false) {
        super(phone, dataManager, WEBSOCKET_TYPE.BL_WORLD, autoPlay);
        this.tag = 'bl';
        this.password = password;
        this.areaCode = areaCode;
    }

    close() {
        super.close();
        clearTimeout(this.signupTimeout);
    }

    requestUserLogin() {
        let inputData = {
            AreaCode: this.areaCode + '',
            Mobile: this.phone + '',
            Password: this.password + '',
            ClientType: 0,
            ClientVersion: '',
            JPushRegistrationID: '',
            device: 'web',
            environment: '{}',
        };
        // console.log(this.tag, "Player onConnected inputData", this.phone, inputData);
        this.websocket.send(commonProto.SocketMessageId.User_Login_Request, inputData);
    }

    requestA92MTT() {
        if (this.websocket) {
            let inputData = {
                MiniGameId: 0,
                Platform: blcommonProto.PLATFORM.Mtt_Pool,
                TicketId: 0,
            };
            this.websocket.send(commonProto.SocketMessageId.BLMiniGameEnterReq, inputData);
        }
    }

    requestUserRegister(inputData, codeInput, fromUserId, callback, onerror) {
        // console.log('requestUserRegister', inputData);
        const { UserRegisterRequest, UserRegisterResponse } = commonProto;
        let userModel = blmodelProto.User.create(inputData);

        axios
            .get(`${Config.blApi}/api/world/register`, {
                method: 'post',
                responseType: 'arraybuffer',
                data: UserRegisterRequest.encode(
                    new UserRegisterRequest({
                        User: userModel,
                        validationCode: codeInput,
                        fromUserId: fromUserId,
                    }),
                ).finish(),
            })
            .then((response) => {
                let arrayBuffer = response.data;
                logging.info(`requestUserRegister res ${arrayBuffer}`);
                if (arrayBuffer) {
                    let byteArray = new Uint8Array(arrayBuffer);
                    callback(UserRegisterResponse.decode(byteArray));
                }
            })
            .catch((e) => {
                logging.error('requestUserRegister error', e);
                onerror();
            });
    }

    onUserInfoUpdated = (msg) => {
        if (this.dataManager) {
            this.dataManager.userData = msg.UserData;
        }
    };

    onSecureTokenErrorResponse = (msg) => {
        // console.log(this.tag, "Player responseUserLogin", this.phone, msg);
        this.setError(this.tag, 'onSecureTokenErrorResponse: ' + msg?.ErrorCode);
        this.close();
    };

    userRegister = () => {
        let inputData = {
            AreaCode: this.areaCode + '',
            Mobile: this.phone + '',
            Password: this.password + '',
            Nickname: 'AT' + this.phone,
            Channel: 0,
        };
        console.log(this.tag, 'Player requestUserRegister inputData', this.phone, inputData);
        this.requestUserRegister(
            inputData,
            '009527',
            0,
            (data) => {
                console.log(this.tag, 'Player requestUserRegister inputData res', this.phone, data);
                if (!data?.ErrorCode) {
                    this.requestUserLogin();
                } else {
                    this.setError('requestUserRegister' + data.ErrorCode.toString());
                }
            },
            (error) => {
                this.setError('requestUserRegister error');
                clearTimeout(this.userRegisterTimeout);
                this.userRegisterTimeout = setTimeout(this.userRegister, reconnectInterval);
            },
        );
    };

    onUserLoginResponse = (msg) => {
        // console.log(this.tag, "Player responseUserLogin", this.phone, msg);
        if (!msg.ErrorCode) {
            this.setError('');
            if (this.websocket) {
                this.dataManager.userData = msg.UserData;
                this.dataManager.token = msg.Token;
                this.websocket.hasVerifyToken = true;

                this.requestA92MTT();
                this.setConnectionStatus(CONNECTION_STATUS.LOGINED);
            }
        } else {
            if (msg.ErrorCode == 10008) {
                this.userRegister();
            }
            this.setError('onUserLoginResponse: ' + msg.ErrorCode.toString());
        }
    };

    bindMessageHandler = () => {
        if (this.websocket) {
            this.websocket.addMessageHandler(
                commonProto.SocketMessageId.User_Login_Response,
                this.onUserLoginResponse,
            );
            this.websocket.addMessageHandler(
                commonProto.SocketMessageId.User_Info_Updated,
                this.onUserInfoUpdated,
            );
            this.websocket.addMessageHandler(
                commonProto.SocketMessageId.Secure_Token_Error_Response,
                this.onSecureTokenErrorResponse,
            );
            this.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED, this.onConnected);
            this.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CLOSE, this.onDisconnected);
        }
    };

    unbindMessageHandler = () => {
        if (this.websocket) {
            this.websocket.removeMessageHandler(commonProto.SocketMessageId.User_Login_Response);
            this.websocket.removeMessageHandler(commonProto.SocketMessageId.User_Info_Updated);
            this.websocket.removeMessageHandler(commonProto.SocketMessageId.Secure_Token_Error_Response);
            this.websocket.removeMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED);
            this.websocket.removeMessageHandler(WEBSOCKET_EVENT_ID.ON_CLOSE);
        }
    };
}

class WorldPlayer extends BasicPlayer {
    blWorldPlayer;
    gamePlayer;
    tournamentStatus = 0;
    tournamentJoinStatus = 0;
    mttId;
    ticketId;
    tournamentStatusInterval;
    signupTimeout;
    httpApis;
    constructor(phone, dataManager, blWorldPlayer, gamePlayer, mttId, ticketId = 0, autoPlay = false) {
        super(phone, dataManager, WEBSOCKET_TYPE.WORLD, autoPlay);
        this.tag = 'mtt';
        this.mttId = mttId;
        this.ticketId = ticketId;
        this.blWorldPlayer = blWorldPlayer;
        this.gamePlayer = gamePlayer;
        this.httpApis = new HttpApis();
    }

    close() {
        super.close();
        clearTimeout(this.signupTimeout);
        clearInterval(this.tournamentStatusInterval);
    }

    setTournamentStatus(status) {
        this.tournamentStatus = status;
        if (this.autoPlay) {
            if (
                this.connectionStatus == CONNECTION_STATUS.LOGINED &&
                this.tournamentStatus > 0 &&
                this.tournamentJoinStatus > 0 &&
                this.gamePlayer
            ) {
                this.connectGame();
            }
        }
    }

    setTournamentJoinStatus(status) {
        this.tournamentJoinStatus = status;
    }

    getMyJoinStatus = (JoinedTournaments, tournamentId) => {
        let index = JoinedTournaments.findIndex((tournament) => tournament.TournamentId == tournamentId);
        return index >= 0 ? JoinedTournaments[index].JoinStatus : 0;
    };

    onConnected = () => {
        logging.info(`${this.tag} 'Player onConnected' ${this.phone}`);
        this.setConnectionStatus(CONNECTION_STATUS.CONNECTED);
        if (this.autoPlay) {
            this.requestUserLogin();
        }
    };

    signup = (onError = (_) => {}) => {
        logging.info(`${this.tag} 'Player signup' ${this.ticketId}`);
        let inputData = {
            UserId: this.dataManager?.userData?.Id,
            UserToken: this.dataManager.token,
            TournamentId: this.mttId,
            TicketId: this.ticketId,
            PlatForm: commonProto.PLATFORM.TRIBAL_PIONEER,
            RegGoldType: 0,
        };
        logging.info('requestMttPlayerSignUp req', inputData);
        this.httpApis.requestMttPlayerSignUp(
            inputData,
            (data) => {
                logging.info('requestMttPlayerSignUp res', data);
                if (!data?.ErrorCode) {
                    this.setError('');
                    this.setTournamentJoinStatus(1);
                    this.getTournamentStatusInterval();
                } else {
                    if (data?.ErrorCode == 30009) {
                        clearTimeout(this.signupTimeout);
                        this.signupTimeout = setTimeout(() => this.signup(onError), reconnectInterval);
                    } else if (data?.ErrorCode == 31100) {
                        clearTimeout(this.signupTimeout);
                        this.signupTimeout = setTimeout(() => this.signup(onError), 60000);
                    } else if (data?.ErrorCode == 10910) {
                        clearTimeout(this.signupTimeout);
                        this.signupTimeout = setTimeout(() => this.signup(onError), 5000);
                    } else if (data?.ErrorCode == 31000) {
                        throw UnrecoverableError('Sign up stopped');
                    } else if (data?.ErrorCode == 5000) {
                        throw UnrecoverableError('Gold insufficient');                     
                    } else {
                        onError(data?.ErrorCode);
                    }
                    this.setError(data?.ErrorCode);
                }
            },
            (err) => {
                if (err instanceof UnrecoverableError) {
                    throw err;
                }
                this.setError('requestMttPlayerSignUp' + err);
                clearTimeout(this.signupTimeout);
                this.signupTimeout = setTimeout(() => this.signup(onError), reconnectInterval);
            },
        );
    };

    onUserLoginResponse = (msg) => {
        // console.log(this.tag, "Player responseUserLogin", this.phone, msg);
        if (!msg.ErrorCode) {
            this.setError('');
            if (this.websocket) {
                this.dataManager.userData = msg.UserData;
                this.dataManager.token = msg.Token;
                this.websocket.hasVerifyToken = true;
                this.setConnectionStatus(CONNECTION_STATUS.LOGINED);
                if (this.blWorldPlayer) {
                    this.blWorldPlayer.close();
                }
            }

            if (this.autoPlay) {
                let myJoinStatus = this.getMyJoinStatus(msg.JoinedTournaments, this.mttId);
                this.setTournamentJoinStatus(myJoinStatus);
                console.log(this.tag, 'Player responseUserLogin myJoinStatus', myJoinStatus);
                if (!myJoinStatus) {
                    this.signup();
                } else {
                    if (myJoinStatus > 0) {
                        this.getTournamentStatusInterval();
                    }
                }
            }
        }
    };

    onSecureTokenErrorResponse = (msg) => {
        // console.log(this.tag, "Player responseUserLogin", this.phone, msg);
        this.setError(this.tag, 'onSecureTokenErrorResponse: ' + msg?.ErrorCode);
        this.close();
    };

    getTournamentStatusInterval = () => {
        clearInterval(this.tournamentStatusInterval);
        this.tournamentStatusInterval = setInterval(this.getTournamentStatus, 5000);
    };

    getTournamentStatus = () => {
        if (this.dataManager) {
            this.httpApis.requestMttTournamentStatus(
                this.dataManager.token,
                this.mttId,
                (data) => {
                    console.log(this.tag, 'requestMttTournamentStatus', data.Status);
                    this.setTournamentStatus(data.Status);
                },
                (err) => {
                    logging.error('requestMttTournamentStatus error', err);
                    this.setTournamentStatus(0);
                    this.setError(err);
                },
            );
        }
    };

    requestUserLogin() {
        if (this.dataManager.token) {
            this.websocket.send(commonProto.SocketMessageId.Secure_Token_Check, {
                Token: this.dataManager.token,
                device: 'web',
                environment: '',
            });
        }
    }

    connectGame = () => {
        clearInterval(this.tournamentStatusInterval);
        if (this.gamePlayer) {
            this.gamePlayer.connect();
        }
    };

    clearTournamentStatusInterval = () => {
        clearInterval(this.tournamentStatusInterval);
    };

    bindMessageHandler = () => {
        if (this.websocket) {
            this.websocket.addMessageHandler(
                commonProto.SocketMessageId.User_Login_Response,
                this.onUserLoginResponse,
            );
            this.websocket.addMessageHandler(
                commonProto.SocketMessageId.Secure_Token_Error_Response,
                this.onSecureTokenErrorResponse,
            );
            this.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED, this.onConnected);
            this.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CLOSE, this.onDisconnected);
        }
    };

    unbindMessageHandler = () => {
        if (this.websocket) {
            this.websocket.removeMessageHandler(commonProto.SocketMessageId.User_Login_Response);
            this.websocket.removeMessageHandler(commonProto.SocketMessageId.onSecureTokenErrorResponse);
            this.websocket.removeMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED);
            this.websocket.removeMessageHandler(WEBSOCKET_EVENT_ID.ON_CLOSE);
        }
    };
}

class GamePlayer extends BasicPlayer {
    ids = { ...holdem.MessageId, ...mttPro.MessageId };
    roomId = 0;
    seatNum = -1;

    mttId;
    worldPlayer;
    constructor(phone, dataManager, mttId, autoPlay = false) {
        super(phone, dataManager, WEBSOCKET_TYPE.GAME, autoPlay);
        this.tag = 'game';
        this.mttId = mttId;
    }

    requestUserLogin() {
        if (this.dataManager.token) {
            this.websocket.send(this.ids.UserTokenReq, {
                token: this.dataManager.token,
                userId: this.dataManager?.userData?.Id,
                hideHole: false,
            });
        }
    }

    onUserTokenRes = (msg) => {
        if (!msg.code) {
            this.setConnectionStatus(CONNECTION_STATUS.LOGINED);
            this.MTTEnterRoom({ mttId: this.mttId });
            if (this.worldPlayer) {
                this.worldPlayer.close();
            }
        }
    };

    onEnterRoomRes = (msg) => {
        if (msg.mttId == this.mttId) {
            this.roomId = msg.roomId;
        }
    };

    MTTEnterRoom = (data) => {
        if (this.websocket) {
            this.websocket.send(this.ids.MttEnterGameReq, data);
        }
    };

    onRoomSnapshotMsg = (msg) => {
        console.log('onRoomSnapshotMsg', msg, this.mttId, this?.dataManager?.userData?.Id);
        if (msg.mttId == this.mttId) {
            this.roomId = msg.roomId;
            if (msg.players) {
                let selfPlayer = msg.players.find((obj) => obj.userId == this?.dataManager?.userData?.Id);
                console.log('onRoomSnapshotMsg selfPlayer', selfPlayer);
                if (selfPlayer) {
                    this.seatNum = selfPlayer.seatNum;
                    if (selfPlayer.flags && selfPlayer.flags & 1) {
                        if (this.websocket) {
                            this.websocket.send(this.ids.CancelAutoPlayReq, { roomId: msg.roomId });
                        }
                    }
                }
            }
        }
    };

    onSeatOccupiedMsg = (msg) => {
        if (msg.roomId == this.roomId && msg.userId == this?.dataManager?.userData?.Id) {
            this.seatNum = msg.seatNum;
        }
    };

    Action = (action, coin) => {
        console.log('ActionReq', ' action:', action, ' coin: ', coin, this.phone, this.roomId);
        if (this.websocket) {
            this.websocket.send(this.ids.ActionReq, { roomId: this.roomId, action, coin });
        }
    };

    onNeedActionMsg = (msg) => {
        console.log('onNeedActionMsg', this.phone, msg, this.roomId, this.seatNum);
        if (msg.roomId == this.roomId && msg.seatNum == this.seatNum) {
            let timeRandom = Math.random();
            setTimeout(() => {
                let actionRandom = Math.random();
                if (actionRandom < 0.2) {
                    this.Action(holdem.Action.ALL_IN, 0);
                } else {
                    this.Action(holdem.Action.FOLD, 0);
                }
            }, timeRandom * 5000);
        }
    };

    onAutoPlayMsg = (msg) => {
        console.log('onAutoPlayMsg', msg, this.roomId, this.seatNum);
        if (msg.roomId == this.roomId) {
            if (this.websocket) {
                this.websocket.send(this.ids.CancelAutoPlayReq, { roomId: this.roomId });
            }
            this.MTTEnterRoom({ mttId: this.mttId });
        }
    };

    onPlayerLeaveMsg = (msg) => {
        console.log('onPlayerLeaveMsg', msg, this.roomId, this.seatNum);
        if (msg.roomId == this.roomId && msg.userId == this?.dataManager?.userData?.Id) {
            this.websocket.forceClose();
            this.setConnectionStatus(CONNECTION_STATUS.STOP);
        }
    };

    bindMessageHandler = () => {
        if (this.websocket) {
            this.websocket.addMessageHandler(this.ids.UserTokenRes, this.onUserTokenRes);
            this.websocket.addMessageHandler(this.ids.EnterRoomRes, this.onEnterRoomRes);
            this.websocket.addMessageHandler(this.ids.RoomSnapshotMsg, this.onRoomSnapshotMsg);
            this.websocket.addMessageHandler(this.ids.SeatOccupiedMsg, this.onSeatOccupiedMsg);
            this.websocket.addMessageHandler(this.ids.AutoPlayMsg, this.onAutoPlayMsg);
            this.websocket.addMessageHandler(this.ids.NeedActionMsg, this.onNeedActionMsg);
            this.websocket.addMessageHandler(this.ids.PlayerLeaveMsg, this.onPlayerLeaveMsg);
            this.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED, this.onConnected);
            this.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CLOSE, this.onDisconnected);
        }
    };

    unbindMessageHandler = () => {
        if (this.websocket) {
            this.websocket.removeMessageHandler(this.ids.UserTokenRes);
            this.websocket.removeMessageHandler(this.ids.EnterRoomRes);
            this.websocket.removeMessageHandler(this.ids.RoomSnapshotMsg);
            this.websocket.removeMessageHandler(this.ids.SeatOccupiedMsg);
            this.websocket.removeMessageHandler(this.ids.AutoPlayMsg);
            this.websocket.removeMessageHandler(this.ids.NeedActionMsg);
            this.websocket.removeMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED);
            this.websocket.removeMessageHandler(WEBSOCKET_EVENT_ID.ON_CLOSE);
        }
    };
}

class Player {
    blWorldPlayer;
    worldPlayer;
    gamePlayer;

    phone;
    mttId;

    blDataManager = {};
    dataManager = {};
    tag = 'player';
    constructor(areaCode, phone, password, mttId) {
        this.phone = phone;
        this.mttId = mttId;
        this.blWorldPlayer = new BlWorldPlayer(phone, password, areaCode, this.blDataManager);
        this.gamePlayer = new GamePlayer(this.phone, this.dataManager, this.mttId);
        this.worldPlayer = new WorldPlayer(
            this.phone,
            this.dataManager,
            this.blWorldPlayer,
            this.gamePlayer,
            this.mttId,
        );
        this.gamePlayer.worldPlayer = this.worldPlayer;
        this.blWorldPlayer.websocket.addMessageHandler(
            commonProto.SocketMessageId.BLMiniGameEnterRes,
            this.onMiniGameEnterRes,
        );
        this.blWorldPlayer.connect();
    }

    close = () => {
        if (this.blWorldPlayer) {
            this.blWorldPlayer.close();
        }
        if (this.worldPlayer) {
            this.worldPlayer.close();
        }
        if (this.gamePlayer) {
            this.gamePlayer.close();
        }
    };

    onMiniGameEnterRes = (msg) => {
        if (msg && msg.Platform) {
            console.log(this.tag, 'onMiniGameEnterRes', this.phone, msg, this.dataManager);
            switch (msg.Platform) {
                case blcommonProto.PLATFORM.Mtt_Pool:
                    console.log(this.tag, 'onMiniGameEnterRes 2', this.phone, msg, this.dataManager);
                    if (!msg.ErrorCode && msg.Token) {
                        if (this.dataManager) {
                            this.dataManager.token = msg.Token;
                            this.blWorldPlayer.setConnectionStatus(CONNECTION_STATUS.MTT_TOKEN_END);
                            this.createWorld();
                        }
                    }
                    break;
            }
        }
    };

    createWorld = () => {
        if (this.worldPlayer) {
            this.worldPlayer.connect();
        }
    };
}

module.exports = { Player, WorldPlayer, GamePlayer };
