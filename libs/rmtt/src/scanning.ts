import { AppType, JobDataHandler, logging } from "shared";

const { HttpApis } = require('./httpApis.js');

const scanInterval = 10000;
const fetchDetailInterval = 5000;

export async function scanAndFetch(token: string, updateData: JobDataHandler, storeTournamentDetails: Function): Promise<void> {
    return new Promise((_, reject) => {
        const httpApis = new HttpApis();
        let requestMttTournamentListInterval: any;
        let fetchTournamentDetailsInterval: any;
        let tournaments: any[] = [];

        const listTournaments = () => {
            httpApis.requestMttTournamentList(
                token,
                0,
                (data: any) => {
                    tournaments = data.TournamentInfos.map(item => toTournamentData(item, AppType.R4));
                    updateData({tournaments: tournaments});
                },
                (error: any) => {
                    clearInterval(requestMttTournamentListInterval);
                    clearInterval(fetchTournamentDetailsInterval);
                    reject(error);
                },
            );
        };

        let tournamentDetails: number[] = [];
        const fetchTournamentDetails = () => {
            if (tournamentDetails.length === 0) {
                if (tournaments.length === 0) {
                    return;
                }
                tournamentDetails = tournaments
                    .filter((t) => {
                        return !t.isSatelliteMode
                            && t.gameMode === 2
                            && (
                                (t.status === 0 && t.startingTime <= Date.now() + 3600 * 1000)
                                || t.status === 1
                                || t.status === 2
                            )
                    }).map((t) => t.id);
            }

            const mttId = tournamentDetails.shift();
            httpApis.requestMttTournamentDetail(
                token,
                mttId,
                (data: any) => storeTournamentDetails(mttId, data),
                (error: any) => logging.error(`Failed to fetch tournament detail: ${mttId}`, error),
            );
        }

        listTournaments();
        requestMttTournamentListInterval = setInterval(listTournaments, scanInterval);
        fetchTournamentDetailsInterval = setInterval(fetchTournamentDetails, fetchDetailInterval);
    });
}

function toTournamentData(tournament: any, appId: AppType) {
    const joinedCount = tournament.JoinedCount || 0;
    const gamePool = tournament.GamePool || 0;
    const regFee = tournament.Detail.RegFee || 0;
    const overlay = gamePool - regFee * joinedCount;
    let lateRegistrationTime;
    if (tournament.TimeLeftSec) {
        lateRegistrationTime = new Date(Date.now() + tournament.TimeLeftSec * 1000);
    }
    let startingTime = new Date(tournament.Detail.StartingTime.seconds * 1000 + tournament.Detail.StartingTime.nanos / 1000000);

    return {
        id: tournament.Detail.Id,
        appId: appId,
        tournamentName: tournament.Detail.TournamentName,
        tournamentNameEng: tournament.Detail.TournamentNameI18N,
        startingTime: startingTime,
        lateRegistrationTime: lateRegistrationTime,
        seats: tournament.Detail.Seats || 0,
        regFee: regFee,
        srvFee: tournament.Detail.SrvFee || 0,
        gamePool: gamePool,
        overlay: overlay,
        displayCurrency: tournament.Detail.DisplayCurrency,
        registeredCount: tournament.RegisteredCount || 0,
        joinedCount: joinedCount,
        status: tournament.Detail.Status || 0,
        mttMode: tournament.Detail.MttMode || 0,
        tournamentMode: tournament.Detail.TournamentMode || 0,
        gameMode: tournament.Detail.GameMode || 0,
        isSatelliteMode: tournament.Detail.IsSatelliteMode || false,
        signUpOptions: tournament.Detail.SignUpOptions || '',
        multiFlightId: tournament.Detail.MultiFlightId || 0,
        multiFlightLevel: tournament.Detail.MultiFlightLevel || 0,
    };
}
