import { BuildVariant } from './utils/Enum';

export class AppConfig {
    private static _instance: AppConfig;
    public static isProd: boolean = false;
    public static buildVariant: BuildVariant = BuildVariant.Staging;
    public static worldHeartBeatInterval: number = 12000;

    public static getInstance(): AppConfig {
        if (!AppConfig._instance) {
            AppConfig._instance = new AppConfig();
        }
        return AppConfig._instance;
    }
}
