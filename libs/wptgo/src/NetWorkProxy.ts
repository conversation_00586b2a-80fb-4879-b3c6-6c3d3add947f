import { IConversionOptions } from 'protobufjs';
import { NetWork } from './NetWork';
import { GameId } from './utils/Enum';

export class NetWorkProxy {
    protected requestTimeout: { [key: number]: { [key: number]: ReturnType<typeof setTimeout> } } = {};
    public readonly defValOptions: IConversionOptions = {
        enums: Number, // enums as string names
        longs: Number, // longs as strings (requires long.js)
        bytes: String, // bytes as base64 encoded strings
        defaults: true, // includes default values
        arrays: true, // populates empty arrays (repeated fields) even if defaults=false
        objects: true, // populates empty objects (map fields) even if defaults=false
        oneofs: true
    };

    /**
     * Parse pb deserialization
     * @param recvClassType responseDataModule
     * @param puf       Metadata
     */
    public decodePB(recvClassType: any, puf: any): any {
        try {
            const buf = new Uint8Array(puf);
            const recvData = recvClassType.decode(buf);
            return recvClassType.toObject(recvData, this.defValOptions);
        } catch (error) {
            console.log('decodePB:Error: PB did not find this message ==>> ' + error);
            return null;
        }
    }

    public registerMsg(msgid: number, fn: any): void {
        NetWork.getInstance().registerMsg(GameId.World, msgid, fn);
    }
}
