// @ts-ignore
import CryptoJS from 'crypto-js';

// we will come back to this later
const SECRET_KEY = '';

export class aesHandler {
    static AES_KEY: string = '';
    static MTT_PRIVATE_KEY: string = '2sdGzJ6rKkyZjPU04SWEqEK4Uwho8NDp';

    static EncryptStringCBC(content: string) {
        const keyBytes = CryptoJS.enc.Utf8.parse(aesHandler.MTT_PRIVATE_KEY);
        const iv = aesHandler.MTT_PRIVATE_KEY.substring(0, 16);
        const srcsBytes = CryptoJS.enc.Utf8.parse(content);
        const encrypted = CryptoJS.AES.encrypt(srcsBytes, keyBytes, {
            iv: CryptoJS.enc.Utf8.parse(iv),
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });

        return CryptoJS.enc.Base64.parse(encrypted.toString());
    }

    static EncryptString(content: string, key: string) {
        // 这里是加密所需要的key
        const keyBytes = CryptoJS.enc.Utf8.parse(key); // 数据解析
        // 需要加密的bytes数组
        const srcsBytes = CryptoJS.enc.Utf8.parse(content); // 数据解析
        const encrypted = CryptoJS.AES.encrypt(srcsBytes, keyBytes, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });

        console.log('-----------> 4:' + encrypted.toString());
        return CryptoJS.enc.Base64.parse(encrypted.toString());
    }

    static DecryptString(content: string, key: string) {
        const keyBytes = CryptoJS.enc.Utf8.parse(key);

        const decrypt = CryptoJS.AES.decrypt(content, keyBytes, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });

        return decrypt.toString(CryptoJS.enc.Utf8);
    }

    static EncryptBytes(content: Uint8Array) {
        const keyBytes = CryptoJS.enc.Utf8.parse(SECRET_KEY);
        const srcsBytes = this.Int8parse(content);

        const encrypted = CryptoJS.AES.encrypt(srcsBytes, keyBytes, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });

        console.log('-----------> 4:' + encrypted.toString());
        return this.base64ToBytes(encrypted.toString());
    }

    static DecryptBytes(content: Uint8Array): Uint8Array {
        const contentArr = new Uint8Array(content);

        const keyBytes = CryptoJS.enc.Utf8.parse(SECRET_KEY);

        const srcsBytes = this.Int8parse(contentArr);
        const word = CryptoJS.enc.Base64.stringify(srcsBytes);

        const decrypt = CryptoJS.AES.decrypt(word, keyBytes, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });

        const result: number[] = [];
        const decryptLength = decrypt.words.length;
        for (let i = 0; i < decryptLength; i++) {
            const a = this.intTobytes(decrypt.words[i]);
            if (decrypt.sigBytes / 4 >= i + 1) {
                for (let j = 0; j < 4; j++) {
                    result.push(a[j]);
                }

                if (decrypt.sigBytes / 4 === i + 1) break;
            } else {
                const len = decrypt.sigBytes % 4;
                for (let j = 0; j < len; j++) {
                    result.push(a[j]);
                }
                break;
            }
        }
        return new Uint8Array(result);
    }

    static Int8parse(u8arr: Uint8Array) {
        // Shortcut
        const len = u8arr.length | u8arr.byteLength;

        // Convert
        const words: number[] = [];
        for (let i = 0; i < len; i++) {
            words[i >>> 2] = words[i >>> 2] || 0;
            words[i >>> 2] |= (u8arr[i] & 0xff) << (24 - (i % 4) * 8);
        }

        return CryptoJS.lib.WordArray.create(words, len);
    }

    static getCharCode(codeString: string) {
        let result = '';
        const codeStringArray = codeString.split(',');
        for (let i = 0; i < codeStringArray.length; i++) {
            result += String.fromCharCode(Number(codeStringArray[i]));
        }
        return result;
    }

    static bytesToHex(bytes: Uint8Array) {
        const hex: string[] = [];
        for (let i = 0; i < bytes.length; i++) {
            hex.push((bytes[i] >>> 4).toString(16));
            hex.push((bytes[i] & 0xf).toString(16));
        }
        return hex.join('');
    }

    static base64ToBytes(base64: string): Uint8Array {
        // Use browser-native function if it exists
        const base64map = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        // Remove non-base-64 characters
        const cleanBase64 = base64.replace(/[^A-Z0-9+/]/gi, '');
        const bytes: number[] = [];
        for (let i = 0, imod4 = 0; i < cleanBase64.length; imod4 = ++i % 4) {
            if (imod4 !== 0) {
                bytes.push(
                    ((base64map.indexOf(cleanBase64.charAt(i - 1)) & (2 ** (-2 * imod4 + 8) - 1)) << (imod4 * 2)) |
                        (base64map.indexOf(cleanBase64.charAt(i)) >>> (6 - imod4 * 2))
                );
            }
        }
        return new Uint8Array(bytes);
    }

    static hexToBytes(hex: string) {
        const bytes: number[] = [];
        for (let c = 0; c < hex.length; c += 2) bytes.push(parseInt(hex.substr(c, 2), 16));
        return bytes;
    }

    static bytesToBase64(bytes: Uint8Array) {
        // Use browser-native function if it exists
        const base64map = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        const base64: string[] = [];
        const len = bytes.length | bytes.byteLength;
        for (let i = 0; i < len; i += 3) {
            const triplet = (bytes[i] << 16) | (bytes[i + 1] << 8) | bytes[i + 2];
            for (let j = 0; j < 4; j++) {
                if (i * 8 + j * 6 <= len * 8) base64.push(base64map.charAt((triplet >>> (6 * (3 - j))) & 0x3f));
                else base64.push('=');
            }
        }
        return base64.join('');
    }

    static stringToUint8Array(str: string): Uint8Array {
        const arr: number[] = [];
        const len = str.length;
        for (let i = 0; i < len; ++i) {
            arr.push(str.charCodeAt(i));
        }

        const tmpUint8Array = new Uint8Array(arr);
        return tmpUint8Array;
    }

    static intTobytes(value: number) {
        const a = new Uint8Array(4);
        a[0] = (value >> 24) & 0xff;
        a[1] = (value >> 16) & 0xff;
        a[2] = (value >> 8) & 0xff;
        a[3] = value & 0xff;
        return a;
    }

    static DecryptBase64(content: string, key: string): string {
        const keyBytes = CryptoJS.enc.Utf8.parse(key);

        const decrypt = CryptoJS.AES.decrypt(content, keyBytes, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });

        let result = '';
        const decryptLength = decrypt.words.length;
        for (let i = 0; i < decryptLength; i++) {
            const a = this.intTobytes(decrypt.words[i]);
            if (decrypt.sigBytes / 4 >= i + 1) {
                for (let j = 0; j < 4; j++) {
                    if (a[j] !== 0) {
                        // 过滤无效的值
                        result += String.fromCharCode(a[j]);
                    }
                }

                if (decrypt.sigBytes / 4 === i + 1) break;
            } else {
                const len = decrypt.sigBytes % 4;
                for (let j = 0; j < len; j++) {
                    if (a[j] !== 0) {
                        // 过滤无效的值
                        result += String.fromCharCode(a[j]);
                    }
                }
                break;
            }
        }
        if (result.length <= 0) {
            result = content;
        }
        this.AES_KEY = result;
        return result;
    }
}
