/**
 * Login request payload interface
 */
export interface LoginRequest {
    version: string;
    device_uuid: string;
    location: {
        latitude: number;
        longitude: number;
    };
    deviceType: string;
    login: string;
    password: string;
    clientType: number;
    language: string;
}

/**
 * Login response interface
 */
export interface LoginResponse {
    token: string;
    user_id: number;
    sharedPlayerId: number;
    sharedPlayerToken: string;
    pkwGateAddresses: string[];
    areaCode: string;
    mobile: string;
    countryIsoCode: string;
    verificationType: string;
    ip: string;
    safe: string;
    pay_type: number;
    shop: string;
    cdn: string;
    activity: {
        act: any[];
    };
    domain: any[];
    is_upgrade: string;
    encry_switch: any[];
    is_vpn: boolean;
    is_ban: boolean;
    club_head: string;
    is_allow_update_name: boolean;
    bk_img: string;
    file_upload_url: string;
    isallowsimulator: boolean;
    mtt_status: number;
    vipTool_url: string;
    responsibleGamingMetadata: any;
    legal_documents_to_accept: Record<string, any>;
    avatarAddress: string;
    pkwFileAddress: string;
    pkwApiAddress: string;
    playerFeatures: Array<{
        feature: string;
        success: boolean;
        failedRules: any[];
    }>;
    download_url: string;
}

/**
 * Simplified login response with only essential fields
 */
export interface LoginResponseEssentials {
    token: string;
    user_id: number;
    sharedPlayerId: number;
    sharedPlayerToken: string;
    pkwGateAddresses: string[];
}
