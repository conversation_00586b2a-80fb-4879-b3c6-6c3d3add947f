import ByteBuffer from 'bytebuffer';
import Long from 'long';

export class ByteArray {
    buffer!: ArrayBuffer;
    rpos: number = 0;
    wpos: number = 0;
    public createBuffer(size_or_buffer: ArrayBuffer): void {
        if (size_or_buffer instanceof ArrayBuffer) {
            this.buffer = size_or_buffer;
        } else {
            this.buffer = new ArrayBuffer(size_or_buffer);
        }

        this.rpos = 0;
        this.wpos = 0;
    }

    public readInt8(): number {
        const buf = new DataView(this.buffer);

        this.rpos += 1;
        return buf.getInt8(this.rpos - 1);
    }

    public readInt16(): number {
        let v = this.readUint16();
        if (v >= 32768) v -= 65536;
        return v;
    }

    public readInt32(): number {
        const buffers = this.buffer.slice(this.rpos, this.rpos + 4);
        const byteBuffer = ByteBuffer.wrap(buffers, 'utf8', ByteBuffer.LITTLE_ENDIAN);
        const ret = byteBuffer.readInt32();
        this.rpos += 4;
        return ret;
    }

    public readInt64(): Long {
        const buffers = this.buffer.slice(this.rpos, this.rpos + 8);
        const byteBuffer = ByteBuffer.wrap(buffers, 'utf8', ByteBuffer.LITTLE_ENDIAN);
        const bbLong = byteBuffer.readInt64();
        this.rpos += 8;
        return new Long(bbLong.low, bbLong.high, bbLong.unsigned);
    }

    public readUint8(): number {
        const buf = new DataView(this.buffer);
        this.rpos += 1;
        return buf.getUint8(this.rpos - 1);
    }

    public readUint16(): number {
        const buf = new DataView(this.buffer);
        this.rpos += 2;

        return buf.getUint16(this.rpos - 2);
    }

    public readUint32(): number {
        const buf = new DataView(this.buffer);

        this.rpos += 4;
        return buf.getUint32(this.rpos - 4);
    }

    public readUint64(): Long {
        const buffers = this.buffer.slice(this.rpos, this.rpos + 8);
        const byteBuffer = ByteBuffer.wrap(buffers, 'utf8', ByteBuffer.LITTLE_ENDIAN);
        const bbLong = byteBuffer.readUint64();
        this.rpos += 8;
        return new Long(bbLong.low, bbLong.high, bbLong.unsigned);
    }

    public readFloat(): number {
        let buf;
        try {
            buf = new Float32Array(this.buffer, this.rpos, 1);
        } catch {
            buf = new Float32Array(this.buffer.slice(this.rpos, this.rpos + 4));
        }

        this.rpos += 4;
        return buf[0];
    }

    public readDouble(): number {
        let buf;
        try {
            buf = new Float64Array(this.buffer, this.rpos, 1);
        } catch {
            buf = new Float64Array(this.buffer.slice(this.rpos, this.rpos + 8), 0, 1);
        }

        this.rpos += 8;
        return buf[0];
    }

    public readString(): string {
        const buf = new Uint8Array(this.buffer, this.rpos);
        let i = 0;
        let s = '';

        while (true) {
            if (buf[i] !== 0) {
                s += String.fromCharCode(buf[i]);
            } else {
                i++;
                break;
            }

            i++;

            if (this.rpos + i >= this.buffer.byteLength) {
                throw new Error(
                    'KBEngine.MemoryStream::readString: rpos(' +
                        (this.rpos + i) +
                        ')>=' +
                        this.buffer.byteLength +
                        ' overflow!'
                );
            }
        }

        this.rpos += i;
        return s;
    }

    public readBlob() {
        const size = this.readUint32();
        const buf = new Uint8Array(this.buffer, this.rpos, size);
        this.rpos += size;
        return buf;
    }

    public readPackY(): number {
        const v = this.readUint16();
        return v;
    }

    public writeInt8(v: number): void {
        const buf = new DataView(this.buffer);
        buf.setInt8(this.wpos, v);
        this.wpos += 1;
    }

    public writeInt16(v: number): void {
        this.writeInt8(v & 0xff);
        this.writeInt8((v >> 8) & 0xff);
    }

    public writeInt32(v: number): void {
        for (let i = 0; i < 4; i++) this.writeInt8((v >> (i * 8)) & 0xff);
    }

    public writeInt64(v: { lo: number; hi: number }): void {
        this.writeInt32(v.lo);
        this.writeInt32(v.hi);
    }

    public writeUint8(v: number): void {
        const buf = new DataView(this.buffer);
        buf.setUint8(this.wpos, v);
        this.wpos += 1;
    }

    public writeUint16(v: number): void {
        const buf = new DataView(this.buffer);
        buf.setUint16(this.wpos, v, false);
        this.wpos += 2;
    }

    public writeUint32(v: number): void {
        const buf = new DataView(this.buffer);
        buf.setUint32(this.wpos, v, false);
        this.wpos += 4;
    }

    public writeUint64(v: { lo: number; hi: number }): void {
        this.writeUint32(v.lo);
        this.writeUint32(v.hi);
    }

    public writeFloat(v: number): void {
        let buf;
        try {
            buf = new Float32Array(this.buffer, this.wpos, 1);
            buf[0] = v;
        } catch {
            buf = new Float32Array(1);
            buf[0] = v;
            const buf1 = new Uint8Array(this.buffer);
            const buf2 = new Uint8Array(buf.buffer);
            buf1.set(buf2, this.wpos);
        }

        this.wpos += 4;
    }

    public writeDouble(v: number): void {
        let buf;
        try {
            buf = new Float64Array(this.buffer, this.wpos, 1);
            buf[0] = v;
        } catch {
            buf = new Float64Array(1);
            buf[0] = v;
            const buf1 = new Uint8Array(this.buffer);
            const buf2 = new Uint8Array(buf.buffer);
            buf1.set(buf2, this.wpos);
        }

        this.wpos += 8;
    }

    public writeString(v: string) {
        if (v.length > this.space()) {
            return;
        }

        const buf = new Uint8Array(this.buffer, this.wpos);
        let i = 0;
        for (let idx = 0; idx < v.length; idx++) {
            buf[i++] = v.charCodeAt(idx);
        }

        buf[i++] = 0;
        this.wpos += i;
    }

    public writeBuffer(buf: ArrayBuffer | Uint8Array): void {
        if (buf instanceof Uint8Array) {
            const buf1 = new Uint8Array(this.buffer);
            buf1.set(buf, this.wpos);
            this.wpos += buf.length;
        } else {
            const buf1 = new Uint8Array(this.buffer);
            const buf2 = new Uint8Array(buf);
            buf1.set(buf2, this.wpos);
            this.wpos += buf.byteLength;
        }
    }

    public readSkip(v: number): void {
        this.rpos += v;
    }

    public space() {
        return this.buffer.byteLength - this.wpos;
    }

    public readEOF() {
        return this.buffer.byteLength - this.rpos <= 0;
    }

    public done() {
        this.rpos = this.wpos;
    }

    public getrpos() {
        return this.rpos;
    }

    public getwpos() {
        return this.wpos;
    }

    public getbuffer() {
        return this.buffer.slice(this.rpos, this.wpos);
    }

    public islittleEndian(): boolean {
        const buffer = new ArrayBuffer(2);
        new DataView(buffer).setInt16(0, 256, true);
        const num = new Int16Array(buffer)[0];
        return num === 256;
    }
}
