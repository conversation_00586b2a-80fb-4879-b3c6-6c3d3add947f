import * as $protobuf from "protobufjs";
import Long = require("long");
/** Properties of a MsgDeliverReq. */
export interface IMsgDeliverReq {

    /** MsgDeliverReq targetId */
    targetId?: (number|Long|null);

    /** MsgDeliverReq msgType */
    msgType?: (string|null);

    /** MsgDeliverReq msgBody */
    msgBody?: (Uint8Array|null);

    /** MsgDeliverReq sign */
    sign?: (string|null);

    /** MsgDeliverReq callbackId */
    callbackId?: (string|null);

    /** MsgDeliverReq stubInfo */
    stubInfo?: (IMsgUserInfo|null);

    /** MsgDeliverReq optHandle */
    optHandle?: (number|null);
}

/** Represents a MsgDeliverReq. */
export class MsgDeliverReq implements IMsgDeliverReq {

    /**
     * Constructs a new MsgDeliverReq.
     * @param [properties] Properties to set
     */
    constructor(properties?: IMsgDeliverReq);

    /** MsgDeliverReq targetId. */
    public targetId: (number|Long);

    /** MsgDeliverReq msgType. */
    public msgType: string;

    /** MsgDeliverReq msgBody. */
    public msgBody: Uint8Array;

    /** MsgDeliverReq sign. */
    public sign: string;

    /** MsgDeliverReq callbackId. */
    public callbackId: string;

    /** MsgDeliverReq stubInfo. */
    public stubInfo?: (IMsgUserInfo|null);

    /** MsgDeliverReq optHandle. */
    public optHandle: number;

    /**
     * Creates a new MsgDeliverReq instance using the specified properties.
     * @param [properties] Properties to set
     * @returns MsgDeliverReq instance
     */
    public static create(properties?: IMsgDeliverReq): MsgDeliverReq;

    /**
     * Encodes the specified MsgDeliverReq message. Does not implicitly {@link MsgDeliverReq.verify|verify} messages.
     * @param message MsgDeliverReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IMsgDeliverReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified MsgDeliverReq message, length delimited. Does not implicitly {@link MsgDeliverReq.verify|verify} messages.
     * @param message MsgDeliverReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IMsgDeliverReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a MsgDeliverReq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MsgDeliverReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): MsgDeliverReq;

    /**
     * Decodes a MsgDeliverReq message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns MsgDeliverReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): MsgDeliverReq;

    /**
     * Verifies a MsgDeliverReq message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a MsgDeliverReq message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns MsgDeliverReq
     */
    public static fromObject(object: { [k: string]: any }): MsgDeliverReq;

    /**
     * Creates a plain object from a MsgDeliverReq message. Also converts values to other types if specified.
     * @param message MsgDeliverReq
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: MsgDeliverReq, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this MsgDeliverReq to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for MsgDeliverReq
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a MsgUserInfo. */
export interface IMsgUserInfo {

    /** MsgUserInfo userId */
    userId?: (number|Long|null);

    /** MsgUserInfo nickname */
    nickname?: (string|null);

    /** MsgUserInfo avatar */
    avatar?: (string|null);

    /** MsgUserInfo sex */
    sex?: (number|null);

    /** MsgUserInfo sessionToken */
    sessionToken?: (string|null);

    /** MsgUserInfo version */
    version?: (string|null);
}

/** Represents a MsgUserInfo. */
export class MsgUserInfo implements IMsgUserInfo {

    /**
     * Constructs a new MsgUserInfo.
     * @param [properties] Properties to set
     */
    constructor(properties?: IMsgUserInfo);

    /** MsgUserInfo userId. */
    public userId: (number|Long);

    /** MsgUserInfo nickname. */
    public nickname: string;

    /** MsgUserInfo avatar. */
    public avatar: string;

    /** MsgUserInfo sex. */
    public sex: number;

    /** MsgUserInfo sessionToken. */
    public sessionToken: string;

    /** MsgUserInfo version. */
    public version: string;

    /**
     * Creates a new MsgUserInfo instance using the specified properties.
     * @param [properties] Properties to set
     * @returns MsgUserInfo instance
     */
    public static create(properties?: IMsgUserInfo): MsgUserInfo;

    /**
     * Encodes the specified MsgUserInfo message. Does not implicitly {@link MsgUserInfo.verify|verify} messages.
     * @param message MsgUserInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IMsgUserInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified MsgUserInfo message, length delimited. Does not implicitly {@link MsgUserInfo.verify|verify} messages.
     * @param message MsgUserInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IMsgUserInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a MsgUserInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MsgUserInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): MsgUserInfo;

    /**
     * Decodes a MsgUserInfo message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns MsgUserInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): MsgUserInfo;

    /**
     * Verifies a MsgUserInfo message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a MsgUserInfo message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns MsgUserInfo
     */
    public static fromObject(object: { [k: string]: any }): MsgUserInfo;

    /**
     * Creates a plain object from a MsgUserInfo message. Also converts values to other types if specified.
     * @param message MsgUserInfo
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: MsgUserInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this MsgUserInfo to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for MsgUserInfo
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleRaisePO. */
export interface IBattleRaisePO {

    /** BattleRaisePO raiseScore */
    raiseScore?: (number|null);

    /** BattleRaisePO reSend */
    reSend?: (number|null);
}

/** Represents a BattleRaisePO. */
export class BattleRaisePO implements IBattleRaisePO {

    /**
     * Constructs a new BattleRaisePO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleRaisePO);

    /** BattleRaisePO raiseScore. */
    public raiseScore: number;

    /** BattleRaisePO reSend. */
    public reSend: number;

    /**
     * Creates a new BattleRaisePO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleRaisePO instance
     */
    public static create(properties?: IBattleRaisePO): BattleRaisePO;

    /**
     * Encodes the specified BattleRaisePO message. Does not implicitly {@link BattleRaisePO.verify|verify} messages.
     * @param message BattleRaisePO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleRaisePO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleRaisePO message, length delimited. Does not implicitly {@link BattleRaisePO.verify|verify} messages.
     * @param message BattleRaisePO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleRaisePO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleRaisePO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleRaisePO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleRaisePO;

    /**
     * Decodes a BattleRaisePO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleRaisePO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleRaisePO;

    /**
     * Verifies a BattleRaisePO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleRaisePO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleRaisePO
     */
    public static fromObject(object: { [k: string]: any }): BattleRaisePO;

    /**
     * Creates a plain object from a BattleRaisePO message. Also converts values to other types if specified.
     * @param message BattleRaisePO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleRaisePO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleRaisePO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleRaisePO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleSitMsg. */
export interface IBattleSitMsg {

    /** BattleSitMsg seatNum */
    seatNum?: (number|null);

    /** BattleSitMsg lon */
    lon?: (number|null);

    /** BattleSitMsg lat */
    lat?: (number|null);
}

/** Represents a BattleSitMsg. */
export class BattleSitMsg implements IBattleSitMsg {

    /**
     * Constructs a new BattleSitMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleSitMsg);

    /** BattleSitMsg seatNum. */
    public seatNum: number;

    /** BattleSitMsg lon. */
    public lon: number;

    /** BattleSitMsg lat. */
    public lat: number;

    /**
     * Creates a new BattleSitMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleSitMsg instance
     */
    public static create(properties?: IBattleSitMsg): BattleSitMsg;

    /**
     * Encodes the specified BattleSitMsg message. Does not implicitly {@link BattleSitMsg.verify|verify} messages.
     * @param message BattleSitMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleSitMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleSitMsg message, length delimited. Does not implicitly {@link BattleSitMsg.verify|verify} messages.
     * @param message BattleSitMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleSitMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleSitMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleSitMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleSitMsg;

    /**
     * Decodes a BattleSitMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleSitMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleSitMsg;

    /**
     * Verifies a BattleSitMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleSitMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleSitMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleSitMsg;

    /**
     * Creates a plain object from a BattleSitMsg message. Also converts values to other types if specified.
     * @param message BattleSitMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleSitMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleSitMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleSitMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleOpenCardReq. */
export interface IBattleOpenCardReq {

    /** BattleOpenCardReq card */
    card?: (number|null);

    /** BattleOpenCardReq optType */
    optType?: (number|null);
}

/** Represents a BattleOpenCardReq. */
export class BattleOpenCardReq implements IBattleOpenCardReq {

    /**
     * Constructs a new BattleOpenCardReq.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleOpenCardReq);

    /** BattleOpenCardReq card. */
    public card: number;

    /** BattleOpenCardReq optType. */
    public optType: number;

    /**
     * Creates a new BattleOpenCardReq instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleOpenCardReq instance
     */
    public static create(properties?: IBattleOpenCardReq): BattleOpenCardReq;

    /**
     * Encodes the specified BattleOpenCardReq message. Does not implicitly {@link BattleOpenCardReq.verify|verify} messages.
     * @param message BattleOpenCardReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleOpenCardReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleOpenCardReq message, length delimited. Does not implicitly {@link BattleOpenCardReq.verify|verify} messages.
     * @param message BattleOpenCardReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleOpenCardReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleOpenCardReq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleOpenCardReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleOpenCardReq;

    /**
     * Decodes a BattleOpenCardReq message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleOpenCardReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleOpenCardReq;

    /**
     * Verifies a BattleOpenCardReq message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleOpenCardReq message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleOpenCardReq
     */
    public static fromObject(object: { [k: string]: any }): BattleOpenCardReq;

    /**
     * Creates a plain object from a BattleOpenCardReq message. Also converts values to other types if specified.
     * @param message BattleOpenCardReq
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleOpenCardReq, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleOpenCardReq to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleOpenCardReq
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleAutoOptPO. */
export interface IBattleAutoOptPO {

    /** BattleAutoOptPO preOptType */
    preOptType?: (number|null);
}

/** Represents a BattleAutoOptPO. */
export class BattleAutoOptPO implements IBattleAutoOptPO {

    /**
     * Constructs a new BattleAutoOptPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleAutoOptPO);

    /** BattleAutoOptPO preOptType. */
    public preOptType: number;

    /**
     * Creates a new BattleAutoOptPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleAutoOptPO instance
     */
    public static create(properties?: IBattleAutoOptPO): BattleAutoOptPO;

    /**
     * Encodes the specified BattleAutoOptPO message. Does not implicitly {@link BattleAutoOptPO.verify|verify} messages.
     * @param message BattleAutoOptPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleAutoOptPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleAutoOptPO message, length delimited. Does not implicitly {@link BattleAutoOptPO.verify|verify} messages.
     * @param message BattleAutoOptPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleAutoOptPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleAutoOptPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleAutoOptPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleAutoOptPO;

    /**
     * Decodes a BattleAutoOptPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleAutoOptPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleAutoOptPO;

    /**
     * Verifies a BattleAutoOptPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleAutoOptPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleAutoOptPO
     */
    public static fromObject(object: { [k: string]: any }): BattleAutoOptPO;

    /**
     * Creates a plain object from a BattleAutoOptPO message. Also converts values to other types if specified.
     * @param message BattleAutoOptPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleAutoOptPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleAutoOptPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleAutoOptPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleScorePO. */
export interface IBattleScorePO {

    /** BattleScorePO score */
    score?: (number|null);

    /** BattleScorePO passwd */
    passwd?: (string|null);
}

/** Represents a BattleScorePO. */
export class BattleScorePO implements IBattleScorePO {

    /**
     * Constructs a new BattleScorePO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleScorePO);

    /** BattleScorePO score. */
    public score: number;

    /** BattleScorePO passwd. */
    public passwd: string;

    /**
     * Creates a new BattleScorePO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleScorePO instance
     */
    public static create(properties?: IBattleScorePO): BattleScorePO;

    /**
     * Encodes the specified BattleScorePO message. Does not implicitly {@link BattleScorePO.verify|verify} messages.
     * @param message BattleScorePO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleScorePO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleScorePO message, length delimited. Does not implicitly {@link BattleScorePO.verify|verify} messages.
     * @param message BattleScorePO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleScorePO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleScorePO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleScorePO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleScorePO;

    /**
     * Decodes a BattleScorePO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleScorePO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleScorePO;

    /**
     * Verifies a BattleScorePO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleScorePO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleScorePO
     */
    public static fromObject(object: { [k: string]: any }): BattleScorePO;

    /**
     * Creates a plain object from a BattleScorePO message. Also converts values to other types if specified.
     * @param message BattleScorePO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleScorePO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleScorePO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleScorePO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleDoubleChooseReq. */
export interface IBattleDoubleChooseReq {

    /** BattleDoubleChooseReq chooseStatus */
    chooseStatus?: (number|null);
}

/** Represents a BattleDoubleChooseReq. */
export class BattleDoubleChooseReq implements IBattleDoubleChooseReq {

    /**
     * Constructs a new BattleDoubleChooseReq.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleDoubleChooseReq);

    /** BattleDoubleChooseReq chooseStatus. */
    public chooseStatus: number;

    /**
     * Creates a new BattleDoubleChooseReq instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleDoubleChooseReq instance
     */
    public static create(properties?: IBattleDoubleChooseReq): BattleDoubleChooseReq;

    /**
     * Encodes the specified BattleDoubleChooseReq message. Does not implicitly {@link BattleDoubleChooseReq.verify|verify} messages.
     * @param message BattleDoubleChooseReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleDoubleChooseReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleDoubleChooseReq message, length delimited. Does not implicitly {@link BattleDoubleChooseReq.verify|verify} messages.
     * @param message BattleDoubleChooseReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleDoubleChooseReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleDoubleChooseReq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleDoubleChooseReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleDoubleChooseReq;

    /**
     * Decodes a BattleDoubleChooseReq message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleDoubleChooseReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleDoubleChooseReq;

    /**
     * Verifies a BattleDoubleChooseReq message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleDoubleChooseReq message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleDoubleChooseReq
     */
    public static fromObject(object: { [k: string]: any }): BattleDoubleChooseReq;

    /**
     * Creates a plain object from a BattleDoubleChooseReq message. Also converts values to other types if specified.
     * @param message BattleDoubleChooseReq
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleDoubleChooseReq, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleDoubleChooseReq to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleDoubleChooseReq
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a MsgTypeMsgPO. */
export interface IMsgTypeMsgPO {

    /** MsgTypeMsgPO type */
    type?: (number|null);

    /** MsgTypeMsgPO content */
    content?: (string|null);

    /** MsgTypeMsgPO second */
    second?: (number|null);

    /** MsgTypeMsgPO createTime */
    createTime?: (number|Long|null);

    /** MsgTypeMsgPO id */
    id?: (string|null);
}

/** Represents a MsgTypeMsgPO. */
export class MsgTypeMsgPO implements IMsgTypeMsgPO {

    /**
     * Constructs a new MsgTypeMsgPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IMsgTypeMsgPO);

    /** MsgTypeMsgPO type. */
    public type: number;

    /** MsgTypeMsgPO content. */
    public content: string;

    /** MsgTypeMsgPO second. */
    public second: number;

    /** MsgTypeMsgPO createTime. */
    public createTime: (number|Long);

    /** MsgTypeMsgPO id. */
    public id: string;

    /**
     * Creates a new MsgTypeMsgPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns MsgTypeMsgPO instance
     */
    public static create(properties?: IMsgTypeMsgPO): MsgTypeMsgPO;

    /**
     * Encodes the specified MsgTypeMsgPO message. Does not implicitly {@link MsgTypeMsgPO.verify|verify} messages.
     * @param message MsgTypeMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IMsgTypeMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified MsgTypeMsgPO message, length delimited. Does not implicitly {@link MsgTypeMsgPO.verify|verify} messages.
     * @param message MsgTypeMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IMsgTypeMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a MsgTypeMsgPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MsgTypeMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): MsgTypeMsgPO;

    /**
     * Decodes a MsgTypeMsgPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns MsgTypeMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): MsgTypeMsgPO;

    /**
     * Verifies a MsgTypeMsgPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a MsgTypeMsgPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns MsgTypeMsgPO
     */
    public static fromObject(object: { [k: string]: any }): MsgTypeMsgPO;

    /**
     * Creates a plain object from a MsgTypeMsgPO message. Also converts values to other types if specified.
     * @param message MsgTypeMsgPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: MsgTypeMsgPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this MsgTypeMsgPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for MsgTypeMsgPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an EmojiMsgPO. */
export interface IEmojiMsgPO {

    /** EmojiMsgPO type */
    type?: (number|null);

    /** EmojiMsgPO content */
    content?: (string|null);

    /** EmojiMsgPO receiverId */
    receiverId?: (number|Long|null);

    /** EmojiMsgPO num */
    num?: (number|null);

    /** EmojiMsgPO emojiId */
    emojiId?: (number|null);

    /** EmojiMsgPO aniType */
    aniType?: (number|null);
}

/** Represents an EmojiMsgPO. */
export class EmojiMsgPO implements IEmojiMsgPO {

    /**
     * Constructs a new EmojiMsgPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IEmojiMsgPO);

    /** EmojiMsgPO type. */
    public type: number;

    /** EmojiMsgPO content. */
    public content: string;

    /** EmojiMsgPO receiverId. */
    public receiverId: (number|Long);

    /** EmojiMsgPO num. */
    public num: number;

    /** EmojiMsgPO emojiId. */
    public emojiId: number;

    /** EmojiMsgPO aniType. */
    public aniType: number;

    /**
     * Creates a new EmojiMsgPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns EmojiMsgPO instance
     */
    public static create(properties?: IEmojiMsgPO): EmojiMsgPO;

    /**
     * Encodes the specified EmojiMsgPO message. Does not implicitly {@link EmojiMsgPO.verify|verify} messages.
     * @param message EmojiMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IEmojiMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified EmojiMsgPO message, length delimited. Does not implicitly {@link EmojiMsgPO.verify|verify} messages.
     * @param message EmojiMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IEmojiMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an EmojiMsgPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns EmojiMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): EmojiMsgPO;

    /**
     * Decodes an EmojiMsgPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns EmojiMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): EmojiMsgPO;

    /**
     * Verifies an EmojiMsgPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an EmojiMsgPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns EmojiMsgPO
     */
    public static fromObject(object: { [k: string]: any }): EmojiMsgPO;

    /**
     * Creates a plain object from an EmojiMsgPO message. Also converts values to other types if specified.
     * @param message EmojiMsgPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: EmojiMsgPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this EmojiMsgPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for EmojiMsgPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a ForceStandReq. */
export interface IForceStandReq {

    /** ForceStandReq standUserId */
    standUserId?: (number|Long|null);
}

/** Represents a ForceStandReq. */
export class ForceStandReq implements IForceStandReq {

    /**
     * Constructs a new ForceStandReq.
     * @param [properties] Properties to set
     */
    constructor(properties?: IForceStandReq);

    /** ForceStandReq standUserId. */
    public standUserId: (number|Long);

    /**
     * Creates a new ForceStandReq instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ForceStandReq instance
     */
    public static create(properties?: IForceStandReq): ForceStandReq;

    /**
     * Encodes the specified ForceStandReq message. Does not implicitly {@link ForceStandReq.verify|verify} messages.
     * @param message ForceStandReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IForceStandReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ForceStandReq message, length delimited. Does not implicitly {@link ForceStandReq.verify|verify} messages.
     * @param message ForceStandReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IForceStandReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ForceStandReq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ForceStandReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ForceStandReq;

    /**
     * Decodes a ForceStandReq message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ForceStandReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ForceStandReq;

    /**
     * Verifies a ForceStandReq message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ForceStandReq message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ForceStandReq
     */
    public static fromObject(object: { [k: string]: any }): ForceStandReq;

    /**
     * Creates a plain object from a ForceStandReq message. Also converts values to other types if specified.
     * @param message ForceStandReq
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ForceStandReq, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ForceStandReq to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ForceStandReq
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a LookComCardsReq. */
export interface ILookComCardsReq {

    /** LookComCardsReq handNum */
    handNum?: (number|null);

    /** LookComCardsReq reSend */
    reSend?: (number|null);
}

/** Represents a LookComCardsReq. */
export class LookComCardsReq implements ILookComCardsReq {

    /**
     * Constructs a new LookComCardsReq.
     * @param [properties] Properties to set
     */
    constructor(properties?: ILookComCardsReq);

    /** LookComCardsReq handNum. */
    public handNum: number;

    /** LookComCardsReq reSend. */
    public reSend: number;

    /**
     * Creates a new LookComCardsReq instance using the specified properties.
     * @param [properties] Properties to set
     * @returns LookComCardsReq instance
     */
    public static create(properties?: ILookComCardsReq): LookComCardsReq;

    /**
     * Encodes the specified LookComCardsReq message. Does not implicitly {@link LookComCardsReq.verify|verify} messages.
     * @param message LookComCardsReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ILookComCardsReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified LookComCardsReq message, length delimited. Does not implicitly {@link LookComCardsReq.verify|verify} messages.
     * @param message LookComCardsReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ILookComCardsReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a LookComCardsReq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns LookComCardsReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LookComCardsReq;

    /**
     * Decodes a LookComCardsReq message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns LookComCardsReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LookComCardsReq;

    /**
     * Verifies a LookComCardsReq message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a LookComCardsReq message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns LookComCardsReq
     */
    public static fromObject(object: { [k: string]: any }): LookComCardsReq;

    /**
     * Creates a plain object from a LookComCardsReq message. Also converts values to other types if specified.
     * @param message LookComCardsReq
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: LookComCardsReq, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this LookComCardsReq to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for LookComCardsReq
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a ShieldUserMsgReq. */
export interface IShieldUserMsgReq {

    /** ShieldUserMsgReq type */
    type?: (number|null);

    /** ShieldUserMsgReq targetUserId */
    targetUserId?: (number|Long|null);
}

/** Represents a ShieldUserMsgReq. */
export class ShieldUserMsgReq implements IShieldUserMsgReq {

    /**
     * Constructs a new ShieldUserMsgReq.
     * @param [properties] Properties to set
     */
    constructor(properties?: IShieldUserMsgReq);

    /** ShieldUserMsgReq type. */
    public type: number;

    /** ShieldUserMsgReq targetUserId. */
    public targetUserId: (number|Long);

    /**
     * Creates a new ShieldUserMsgReq instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ShieldUserMsgReq instance
     */
    public static create(properties?: IShieldUserMsgReq): ShieldUserMsgReq;

    /**
     * Encodes the specified ShieldUserMsgReq message. Does not implicitly {@link ShieldUserMsgReq.verify|verify} messages.
     * @param message ShieldUserMsgReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IShieldUserMsgReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ShieldUserMsgReq message, length delimited. Does not implicitly {@link ShieldUserMsgReq.verify|verify} messages.
     * @param message ShieldUserMsgReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IShieldUserMsgReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ShieldUserMsgReq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ShieldUserMsgReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ShieldUserMsgReq;

    /**
     * Decodes a ShieldUserMsgReq message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ShieldUserMsgReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ShieldUserMsgReq;

    /**
     * Verifies a ShieldUserMsgReq message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ShieldUserMsgReq message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ShieldUserMsgReq
     */
    public static fromObject(object: { [k: string]: any }): ShieldUserMsgReq;

    /**
     * Creates a plain object from a ShieldUserMsgReq message. Also converts values to other types if specified.
     * @param message ShieldUserMsgReq
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ShieldUserMsgReq, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ShieldUserMsgReq to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ShieldUserMsgReq
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an OptTargetPB. */
export interface IOptTargetPB {

    /** OptTargetPB targetId */
    targetId?: (number|Long|null);

    /** OptTargetPB ext */
    ext?: (string|null);
}

/** Represents an OptTargetPB. */
export class OptTargetPB implements IOptTargetPB {

    /**
     * Constructs a new OptTargetPB.
     * @param [properties] Properties to set
     */
    constructor(properties?: IOptTargetPB);

    /** OptTargetPB targetId. */
    public targetId: (number|Long);

    /** OptTargetPB ext. */
    public ext: string;

    /**
     * Creates a new OptTargetPB instance using the specified properties.
     * @param [properties] Properties to set
     * @returns OptTargetPB instance
     */
    public static create(properties?: IOptTargetPB): OptTargetPB;

    /**
     * Encodes the specified OptTargetPB message. Does not implicitly {@link OptTargetPB.verify|verify} messages.
     * @param message OptTargetPB message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IOptTargetPB, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified OptTargetPB message, length delimited. Does not implicitly {@link OptTargetPB.verify|verify} messages.
     * @param message OptTargetPB message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IOptTargetPB, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an OptTargetPB message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns OptTargetPB
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): OptTargetPB;

    /**
     * Decodes an OptTargetPB message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns OptTargetPB
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): OptTargetPB;

    /**
     * Verifies an OptTargetPB message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an OptTargetPB message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns OptTargetPB
     */
    public static fromObject(object: { [k: string]: any }): OptTargetPB;

    /**
     * Creates a plain object from an OptTargetPB message. Also converts values to other types if specified.
     * @param message OptTargetPB
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: OptTargetPB, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this OptTargetPB to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for OptTargetPB
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a DiamondSendMsgPO. */
export interface IDiamondSendMsgPO {

    /** DiamondSendMsgPO receiverId */
    receiverId?: (number|Long|null);

    /** DiamondSendMsgPO diamondId */
    diamondId?: (number|null);

    /** DiamondSendMsgPO passwd */
    passwd?: (string|null);
}

/** Represents a DiamondSendMsgPO. */
export class DiamondSendMsgPO implements IDiamondSendMsgPO {

    /**
     * Constructs a new DiamondSendMsgPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IDiamondSendMsgPO);

    /** DiamondSendMsgPO receiverId. */
    public receiverId: (number|Long);

    /** DiamondSendMsgPO diamondId. */
    public diamondId: number;

    /** DiamondSendMsgPO passwd. */
    public passwd: string;

    /**
     * Creates a new DiamondSendMsgPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns DiamondSendMsgPO instance
     */
    public static create(properties?: IDiamondSendMsgPO): DiamondSendMsgPO;

    /**
     * Encodes the specified DiamondSendMsgPO message. Does not implicitly {@link DiamondSendMsgPO.verify|verify} messages.
     * @param message DiamondSendMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IDiamondSendMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified DiamondSendMsgPO message, length delimited. Does not implicitly {@link DiamondSendMsgPO.verify|verify} messages.
     * @param message DiamondSendMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IDiamondSendMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a DiamondSendMsgPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DiamondSendMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): DiamondSendMsgPO;

    /**
     * Decodes a DiamondSendMsgPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns DiamondSendMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): DiamondSendMsgPO;

    /**
     * Verifies a DiamondSendMsgPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a DiamondSendMsgPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns DiamondSendMsgPO
     */
    public static fromObject(object: { [k: string]: any }): DiamondSendMsgPO;

    /**
     * Creates a plain object from a DiamondSendMsgPO message. Also converts values to other types if specified.
     * @param message DiamondSendMsgPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: DiamondSendMsgPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this DiamondSendMsgPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for DiamondSendMsgPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a GuessHandBetPO. */
export interface IGuessHandBetPO {

    /** GuessHandBetPO type */
    type?: (number|null);

    /** GuessHandBetPO bet */
    bet?: (number|null);

    /** GuessHandBetPO oddsId */
    oddsId?: (number|null);

    /** GuessHandBetPO continuous */
    continuous?: (number|null);
}

/** Represents a GuessHandBetPO. */
export class GuessHandBetPO implements IGuessHandBetPO {

    /**
     * Constructs a new GuessHandBetPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IGuessHandBetPO);

    /** GuessHandBetPO type. */
    public type: number;

    /** GuessHandBetPO bet. */
    public bet: number;

    /** GuessHandBetPO oddsId. */
    public oddsId: number;

    /** GuessHandBetPO continuous. */
    public continuous: number;

    /**
     * Creates a new GuessHandBetPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns GuessHandBetPO instance
     */
    public static create(properties?: IGuessHandBetPO): GuessHandBetPO;

    /**
     * Encodes the specified GuessHandBetPO message. Does not implicitly {@link GuessHandBetPO.verify|verify} messages.
     * @param message GuessHandBetPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IGuessHandBetPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified GuessHandBetPO message, length delimited. Does not implicitly {@link GuessHandBetPO.verify|verify} messages.
     * @param message GuessHandBetPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IGuessHandBetPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a GuessHandBetPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns GuessHandBetPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): GuessHandBetPO;

    /**
     * Decodes a GuessHandBetPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns GuessHandBetPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): GuessHandBetPO;

    /**
     * Verifies a GuessHandBetPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a GuessHandBetPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns GuessHandBetPO
     */
    public static fromObject(object: { [k: string]: any }): GuessHandBetPO;

    /**
     * Creates a plain object from a GuessHandBetPO message. Also converts values to other types if specified.
     * @param message GuessHandBetPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: GuessHandBetPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this GuessHandBetPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for GuessHandBetPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a RetraceScorePO. */
export interface IRetraceScorePO {

    /** RetraceScorePO type */
    type?: (number|null);

    /** RetraceScorePO score */
    score?: (number|null);
}

/** Represents a RetraceScorePO. */
export class RetraceScorePO implements IRetraceScorePO {

    /**
     * Constructs a new RetraceScorePO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IRetraceScorePO);

    /** RetraceScorePO type. */
    public type: number;

    /** RetraceScorePO score. */
    public score: number;

    /**
     * Creates a new RetraceScorePO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns RetraceScorePO instance
     */
    public static create(properties?: IRetraceScorePO): RetraceScorePO;

    /**
     * Encodes the specified RetraceScorePO message. Does not implicitly {@link RetraceScorePO.verify|verify} messages.
     * @param message RetraceScorePO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IRetraceScorePO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified RetraceScorePO message, length delimited. Does not implicitly {@link RetraceScorePO.verify|verify} messages.
     * @param message RetraceScorePO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IRetraceScorePO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a RetraceScorePO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RetraceScorePO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): RetraceScorePO;

    /**
     * Decodes a RetraceScorePO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns RetraceScorePO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): RetraceScorePO;

    /**
     * Verifies a RetraceScorePO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a RetraceScorePO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns RetraceScorePO
     */
    public static fromObject(object: { [k: string]: any }): RetraceScorePO;

    /**
     * Creates a plain object from a RetraceScorePO message. Also converts values to other types if specified.
     * @param message RetraceScorePO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: RetraceScorePO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this RetraceScorePO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for RetraceScorePO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a JoinRoomReq. */
export interface IJoinRoomReq {

    /** JoinRoomReq type */
    type?: (number|null);
}

/** Represents a JoinRoomReq. */
export class JoinRoomReq implements IJoinRoomReq {

    /**
     * Constructs a new JoinRoomReq.
     * @param [properties] Properties to set
     */
    constructor(properties?: IJoinRoomReq);

    /** JoinRoomReq type. */
    public type: number;

    /**
     * Creates a new JoinRoomReq instance using the specified properties.
     * @param [properties] Properties to set
     * @returns JoinRoomReq instance
     */
    public static create(properties?: IJoinRoomReq): JoinRoomReq;

    /**
     * Encodes the specified JoinRoomReq message. Does not implicitly {@link JoinRoomReq.verify|verify} messages.
     * @param message JoinRoomReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IJoinRoomReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified JoinRoomReq message, length delimited. Does not implicitly {@link JoinRoomReq.verify|verify} messages.
     * @param message JoinRoomReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IJoinRoomReq, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a JoinRoomReq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns JoinRoomReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): JoinRoomReq;

    /**
     * Decodes a JoinRoomReq message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns JoinRoomReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): JoinRoomReq;

    /**
     * Verifies a JoinRoomReq message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a JoinRoomReq message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns JoinRoomReq
     */
    public static fromObject(object: { [k: string]: any }): JoinRoomReq;

    /**
     * Creates a plain object from a JoinRoomReq message. Also converts values to other types if specified.
     * @param message JoinRoomReq
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: JoinRoomReq, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this JoinRoomReq to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for JoinRoomReq
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a PlaceCardPO. */
export interface IPlaceCardPO {

    /** PlaceCardPO location */
    location?: (string|null);

    /** PlaceCardPO index */
    index?: (number|null);

    /** PlaceCardPO card */
    card?: (number|null);
}

/** Represents a PlaceCardPO. */
export class PlaceCardPO implements IPlaceCardPO {

    /**
     * Constructs a new PlaceCardPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IPlaceCardPO);

    /** PlaceCardPO location. */
    public location: string;

    /** PlaceCardPO index. */
    public index: number;

    /** PlaceCardPO card. */
    public card: number;

    /**
     * Creates a new PlaceCardPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns PlaceCardPO instance
     */
    public static create(properties?: IPlaceCardPO): PlaceCardPO;

    /**
     * Encodes the specified PlaceCardPO message. Does not implicitly {@link PlaceCardPO.verify|verify} messages.
     * @param message PlaceCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IPlaceCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified PlaceCardPO message, length delimited. Does not implicitly {@link PlaceCardPO.verify|verify} messages.
     * @param message PlaceCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IPlaceCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a PlaceCardPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PlaceCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): PlaceCardPO;

    /**
     * Decodes a PlaceCardPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns PlaceCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): PlaceCardPO;

    /**
     * Verifies a PlaceCardPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a PlaceCardPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns PlaceCardPO
     */
    public static fromObject(object: { [k: string]: any }): PlaceCardPO;

    /**
     * Creates a plain object from a PlaceCardPO message. Also converts values to other types if specified.
     * @param message PlaceCardPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: PlaceCardPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this PlaceCardPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for PlaceCardPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a MultiPlaceCardPO. */
export interface IMultiPlaceCardPO {

    /** MultiPlaceCardPO locations */
    locations?: (IPlaceCardPO[]|null);

    /** MultiPlaceCardPO specialHandType */
    specialHandType?: (string|null);

    /** MultiPlaceCardPO moveCard */
    moveCard?: (number|null);
}

/** Represents a MultiPlaceCardPO. */
export class MultiPlaceCardPO implements IMultiPlaceCardPO {

    /**
     * Constructs a new MultiPlaceCardPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IMultiPlaceCardPO);

    /** MultiPlaceCardPO locations. */
    public locations: IPlaceCardPO[];

    /** MultiPlaceCardPO specialHandType. */
    public specialHandType: string;

    /** MultiPlaceCardPO moveCard. */
    public moveCard: number;

    /**
     * Creates a new MultiPlaceCardPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns MultiPlaceCardPO instance
     */
    public static create(properties?: IMultiPlaceCardPO): MultiPlaceCardPO;

    /**
     * Encodes the specified MultiPlaceCardPO message. Does not implicitly {@link MultiPlaceCardPO.verify|verify} messages.
     * @param message MultiPlaceCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IMultiPlaceCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified MultiPlaceCardPO message, length delimited. Does not implicitly {@link MultiPlaceCardPO.verify|verify} messages.
     * @param message MultiPlaceCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IMultiPlaceCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a MultiPlaceCardPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MultiPlaceCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): MultiPlaceCardPO;

    /**
     * Decodes a MultiPlaceCardPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns MultiPlaceCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): MultiPlaceCardPO;

    /**
     * Verifies a MultiPlaceCardPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a MultiPlaceCardPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns MultiPlaceCardPO
     */
    public static fromObject(object: { [k: string]: any }): MultiPlaceCardPO;

    /**
     * Creates a plain object from a MultiPlaceCardPO message. Also converts values to other types if specified.
     * @param message MultiPlaceCardPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: MultiPlaceCardPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this MultiPlaceCardPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for MultiPlaceCardPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a ReplaceCardPO. */
export interface IReplaceCardPO {

    /** ReplaceCardPO locations */
    locations?: (IPlaceCardPO[]|null);
}

/** Represents a ReplaceCardPO. */
export class ReplaceCardPO implements IReplaceCardPO {

    /**
     * Constructs a new ReplaceCardPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IReplaceCardPO);

    /** ReplaceCardPO locations. */
    public locations: IPlaceCardPO[];

    /**
     * Creates a new ReplaceCardPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ReplaceCardPO instance
     */
    public static create(properties?: IReplaceCardPO): ReplaceCardPO;

    /**
     * Encodes the specified ReplaceCardPO message. Does not implicitly {@link ReplaceCardPO.verify|verify} messages.
     * @param message ReplaceCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IReplaceCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ReplaceCardPO message, length delimited. Does not implicitly {@link ReplaceCardPO.verify|verify} messages.
     * @param message ReplaceCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IReplaceCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ReplaceCardPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ReplaceCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ReplaceCardPO;

    /**
     * Decodes a ReplaceCardPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ReplaceCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ReplaceCardPO;

    /**
     * Verifies a ReplaceCardPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ReplaceCardPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ReplaceCardPO
     */
    public static fromObject(object: { [k: string]: any }): ReplaceCardPO;

    /**
     * Creates a plain object from a ReplaceCardPO message. Also converts values to other types if specified.
     * @param message ReplaceCardPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ReplaceCardPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ReplaceCardPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ReplaceCardPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a SwapCardPO. */
export interface ISwapCardPO {

    /** SwapCardPO locations */
    locations?: (IPlaceCardPO[]|null);
}

/** Represents a SwapCardPO. */
export class SwapCardPO implements ISwapCardPO {

    /**
     * Constructs a new SwapCardPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: ISwapCardPO);

    /** SwapCardPO locations. */
    public locations: IPlaceCardPO[];

    /**
     * Creates a new SwapCardPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns SwapCardPO instance
     */
    public static create(properties?: ISwapCardPO): SwapCardPO;

    /**
     * Encodes the specified SwapCardPO message. Does not implicitly {@link SwapCardPO.verify|verify} messages.
     * @param message SwapCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ISwapCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified SwapCardPO message, length delimited. Does not implicitly {@link SwapCardPO.verify|verify} messages.
     * @param message SwapCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ISwapCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a SwapCardPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns SwapCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): SwapCardPO;

    /**
     * Decodes a SwapCardPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns SwapCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): SwapCardPO;

    /**
     * Verifies a SwapCardPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a SwapCardPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns SwapCardPO
     */
    public static fromObject(object: { [k: string]: any }): SwapCardPO;

    /**
     * Creates a plain object from a SwapCardPO message. Also converts values to other types if specified.
     * @param message SwapCardPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: SwapCardPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this SwapCardPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for SwapCardPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BetPO. */
export interface IBetPO {

    /** BetPO value */
    value?: (number|null);
}

/** Represents a BetPO. */
export class BetPO implements IBetPO {

    /**
     * Constructs a new BetPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBetPO);

    /** BetPO value. */
    public value: number;

    /**
     * Creates a new BetPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BetPO instance
     */
    public static create(properties?: IBetPO): BetPO;

    /**
     * Encodes the specified BetPO message. Does not implicitly {@link BetPO.verify|verify} messages.
     * @param message BetPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBetPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BetPO message, length delimited. Does not implicitly {@link BetPO.verify|verify} messages.
     * @param message BetPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBetPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BetPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BetPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BetPO;

    /**
     * Decodes a BetPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BetPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BetPO;

    /**
     * Verifies a BetPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BetPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BetPO
     */
    public static fromObject(object: { [k: string]: any }): BetPO;

    /**
     * Creates a plain object from a BetPO message. Also converts values to other types if specified.
     * @param message BetPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BetPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BetPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BetPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a SortPO. */
export interface ISortPO {

    /** SortPO orderBy */
    orderBy?: (string|null);
}

/** Represents a SortPO. */
export class SortPO implements ISortPO {

    /**
     * Constructs a new SortPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: ISortPO);

    /** SortPO orderBy. */
    public orderBy: string;

    /**
     * Creates a new SortPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns SortPO instance
     */
    public static create(properties?: ISortPO): SortPO;

    /**
     * Encodes the specified SortPO message. Does not implicitly {@link SortPO.verify|verify} messages.
     * @param message SortPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ISortPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified SortPO message, length delimited. Does not implicitly {@link SortPO.verify|verify} messages.
     * @param message SortPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ISortPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a SortPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns SortPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): SortPO;

    /**
     * Decodes a SortPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns SortPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): SortPO;

    /**
     * Verifies a SortPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a SortPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns SortPO
     */
    public static fromObject(object: { [k: string]: any }): SortPO;

    /**
     * Creates a plain object from a SortPO message. Also converts values to other types if specified.
     * @param message SortPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: SortPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this SortPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for SortPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a SpecialHandComparisonDecisionPO. */
export interface ISpecialHandComparisonDecisionPO {

    /** SpecialHandComparisonDecisionPO useSpecialHand */
    useSpecialHand?: (string|null);
}

/** Represents a SpecialHandComparisonDecisionPO. */
export class SpecialHandComparisonDecisionPO implements ISpecialHandComparisonDecisionPO {

    /**
     * Constructs a new SpecialHandComparisonDecisionPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: ISpecialHandComparisonDecisionPO);

    /** SpecialHandComparisonDecisionPO useSpecialHand. */
    public useSpecialHand: string;

    /**
     * Creates a new SpecialHandComparisonDecisionPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns SpecialHandComparisonDecisionPO instance
     */
    public static create(properties?: ISpecialHandComparisonDecisionPO): SpecialHandComparisonDecisionPO;

    /**
     * Encodes the specified SpecialHandComparisonDecisionPO message. Does not implicitly {@link SpecialHandComparisonDecisionPO.verify|verify} messages.
     * @param message SpecialHandComparisonDecisionPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ISpecialHandComparisonDecisionPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified SpecialHandComparisonDecisionPO message, length delimited. Does not implicitly {@link SpecialHandComparisonDecisionPO.verify|verify} messages.
     * @param message SpecialHandComparisonDecisionPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ISpecialHandComparisonDecisionPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a SpecialHandComparisonDecisionPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns SpecialHandComparisonDecisionPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): SpecialHandComparisonDecisionPO;

    /**
     * Decodes a SpecialHandComparisonDecisionPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns SpecialHandComparisonDecisionPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): SpecialHandComparisonDecisionPO;

    /**
     * Verifies a SpecialHandComparisonDecisionPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a SpecialHandComparisonDecisionPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns SpecialHandComparisonDecisionPO
     */
    public static fromObject(object: { [k: string]: any }): SpecialHandComparisonDecisionPO;

    /**
     * Creates a plain object from a SpecialHandComparisonDecisionPO message. Also converts values to other types if specified.
     * @param message SpecialHandComparisonDecisionPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: SpecialHandComparisonDecisionPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this SpecialHandComparisonDecisionPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for SpecialHandComparisonDecisionPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a RealHeadPO. */
export interface IRealHeadPO {

    /** RealHeadPO head */
    head?: (google.protobuf.IStringValue|null);

    /** RealHeadPO userId */
    userId?: (google.protobuf.IInt64Value|null);
}

/** Represents a RealHeadPO. */
export class RealHeadPO implements IRealHeadPO {

    /**
     * Constructs a new RealHeadPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IRealHeadPO);

    /** RealHeadPO head. */
    public head?: (google.protobuf.IStringValue|null);

    /** RealHeadPO userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /**
     * Creates a new RealHeadPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns RealHeadPO instance
     */
    public static create(properties?: IRealHeadPO): RealHeadPO;

    /**
     * Encodes the specified RealHeadPO message. Does not implicitly {@link RealHeadPO.verify|verify} messages.
     * @param message RealHeadPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IRealHeadPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified RealHeadPO message, length delimited. Does not implicitly {@link RealHeadPO.verify|verify} messages.
     * @param message RealHeadPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IRealHeadPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a RealHeadPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RealHeadPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): RealHeadPO;

    /**
     * Decodes a RealHeadPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns RealHeadPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): RealHeadPO;

    /**
     * Verifies a RealHeadPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a RealHeadPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns RealHeadPO
     */
    public static fromObject(object: { [k: string]: any }): RealHeadPO;

    /**
     * Creates a plain object from a RealHeadPO message. Also converts values to other types if specified.
     * @param message RealHeadPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: RealHeadPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this RealHeadPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for RealHeadPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an InsuranceBuyPO. */
export interface IInsuranceBuyPO {

    /** InsuranceBuyPO type */
    type?: (number|null);

    /** InsuranceBuyPO buyRound */
    buyRound?: (number|null);
}

/** Buy game insurance operation */
export class InsuranceBuyPO implements IInsuranceBuyPO {

    /**
     * Constructs a new InsuranceBuyPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IInsuranceBuyPO);

    /** InsuranceBuyPO type. */
    public type: number;

    /** InsuranceBuyPO buyRound. */
    public buyRound: number;

    /**
     * Creates a new InsuranceBuyPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns InsuranceBuyPO instance
     */
    public static create(properties?: IInsuranceBuyPO): InsuranceBuyPO;

    /**
     * Encodes the specified InsuranceBuyPO message. Does not implicitly {@link InsuranceBuyPO.verify|verify} messages.
     * @param message InsuranceBuyPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IInsuranceBuyPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified InsuranceBuyPO message, length delimited. Does not implicitly {@link InsuranceBuyPO.verify|verify} messages.
     * @param message InsuranceBuyPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IInsuranceBuyPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an InsuranceBuyPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns InsuranceBuyPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): InsuranceBuyPO;

    /**
     * Decodes an InsuranceBuyPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns InsuranceBuyPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): InsuranceBuyPO;

    /**
     * Verifies an InsuranceBuyPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an InsuranceBuyPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns InsuranceBuyPO
     */
    public static fromObject(object: { [k: string]: any }): InsuranceBuyPO;

    /**
     * Creates a plain object from an InsuranceBuyPO message. Also converts values to other types if specified.
     * @param message InsuranceBuyPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: InsuranceBuyPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this InsuranceBuyPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for InsuranceBuyPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Namespace google. */
export namespace google {

    /** Namespace protobuf. */
    namespace protobuf {

        /** Properties of a DoubleValue. */
        interface IDoubleValue {

            /** DoubleValue value */
            value?: (number|null);
        }

        /** Represents a DoubleValue. */
        class DoubleValue implements IDoubleValue {

            /**
             * Constructs a new DoubleValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IDoubleValue);

            /** DoubleValue value. */
            public value: number;

            /**
             * Creates a new DoubleValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns DoubleValue instance
             */
            public static create(properties?: google.protobuf.IDoubleValue): google.protobuf.DoubleValue;

            /**
             * Encodes the specified DoubleValue message. Does not implicitly {@link google.protobuf.DoubleValue.verify|verify} messages.
             * @param message DoubleValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IDoubleValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified DoubleValue message, length delimited. Does not implicitly {@link google.protobuf.DoubleValue.verify|verify} messages.
             * @param message DoubleValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IDoubleValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a DoubleValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns DoubleValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DoubleValue;

            /**
             * Decodes a DoubleValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns DoubleValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.DoubleValue;

            /**
             * Verifies a DoubleValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a DoubleValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns DoubleValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.DoubleValue;

            /**
             * Creates a plain object from a DoubleValue message. Also converts values to other types if specified.
             * @param message DoubleValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.DoubleValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this DoubleValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for DoubleValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a FloatValue. */
        interface IFloatValue {

            /** FloatValue value */
            value?: (number|null);
        }

        /** Represents a FloatValue. */
        class FloatValue implements IFloatValue {

            /**
             * Constructs a new FloatValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFloatValue);

            /** FloatValue value. */
            public value: number;

            /**
             * Creates a new FloatValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns FloatValue instance
             */
            public static create(properties?: google.protobuf.IFloatValue): google.protobuf.FloatValue;

            /**
             * Encodes the specified FloatValue message. Does not implicitly {@link google.protobuf.FloatValue.verify|verify} messages.
             * @param message FloatValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFloatValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified FloatValue message, length delimited. Does not implicitly {@link google.protobuf.FloatValue.verify|verify} messages.
             * @param message FloatValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IFloatValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FloatValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FloatValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FloatValue;

            /**
             * Decodes a FloatValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns FloatValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.FloatValue;

            /**
             * Verifies a FloatValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FloatValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FloatValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FloatValue;

            /**
             * Creates a plain object from a FloatValue message. Also converts values to other types if specified.
             * @param message FloatValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FloatValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FloatValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FloatValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an Int64Value. */
        interface IInt64Value {

            /** Int64Value value */
            value?: (number|Long|null);
        }

        /** Represents an Int64Value. */
        class Int64Value implements IInt64Value {

            /**
             * Constructs a new Int64Value.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IInt64Value);

            /** Int64Value value. */
            public value: (number|Long);

            /**
             * Creates a new Int64Value instance using the specified properties.
             * @param [properties] Properties to set
             * @returns Int64Value instance
             */
            public static create(properties?: google.protobuf.IInt64Value): google.protobuf.Int64Value;

            /**
             * Encodes the specified Int64Value message. Does not implicitly {@link google.protobuf.Int64Value.verify|verify} messages.
             * @param message Int64Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IInt64Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified Int64Value message, length delimited. Does not implicitly {@link google.protobuf.Int64Value.verify|verify} messages.
             * @param message Int64Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IInt64Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an Int64Value message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Int64Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.Int64Value;

            /**
             * Decodes an Int64Value message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns Int64Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.Int64Value;

            /**
             * Verifies an Int64Value message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an Int64Value message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Int64Value
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.Int64Value;

            /**
             * Creates a plain object from an Int64Value message. Also converts values to other types if specified.
             * @param message Int64Value
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.Int64Value, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Int64Value to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for Int64Value
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a UInt64Value. */
        interface IUInt64Value {

            /** UInt64Value value */
            value?: (number|Long|null);
        }

        /** Represents a UInt64Value. */
        class UInt64Value implements IUInt64Value {

            /**
             * Constructs a new UInt64Value.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IUInt64Value);

            /** UInt64Value value. */
            public value: (number|Long);

            /**
             * Creates a new UInt64Value instance using the specified properties.
             * @param [properties] Properties to set
             * @returns UInt64Value instance
             */
            public static create(properties?: google.protobuf.IUInt64Value): google.protobuf.UInt64Value;

            /**
             * Encodes the specified UInt64Value message. Does not implicitly {@link google.protobuf.UInt64Value.verify|verify} messages.
             * @param message UInt64Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IUInt64Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified UInt64Value message, length delimited. Does not implicitly {@link google.protobuf.UInt64Value.verify|verify} messages.
             * @param message UInt64Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IUInt64Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a UInt64Value message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UInt64Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UInt64Value;

            /**
             * Decodes a UInt64Value message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns UInt64Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.UInt64Value;

            /**
             * Verifies a UInt64Value message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a UInt64Value message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UInt64Value
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.UInt64Value;

            /**
             * Creates a plain object from a UInt64Value message. Also converts values to other types if specified.
             * @param message UInt64Value
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.UInt64Value, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UInt64Value to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for UInt64Value
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an Int32Value. */
        interface IInt32Value {

            /** Int32Value value */
            value?: (number|null);
        }

        /** Represents an Int32Value. */
        class Int32Value implements IInt32Value {

            /**
             * Constructs a new Int32Value.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IInt32Value);

            /** Int32Value value. */
            public value: number;

            /**
             * Creates a new Int32Value instance using the specified properties.
             * @param [properties] Properties to set
             * @returns Int32Value instance
             */
            public static create(properties?: google.protobuf.IInt32Value): google.protobuf.Int32Value;

            /**
             * Encodes the specified Int32Value message. Does not implicitly {@link google.protobuf.Int32Value.verify|verify} messages.
             * @param message Int32Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IInt32Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified Int32Value message, length delimited. Does not implicitly {@link google.protobuf.Int32Value.verify|verify} messages.
             * @param message Int32Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IInt32Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an Int32Value message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Int32Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.Int32Value;

            /**
             * Decodes an Int32Value message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns Int32Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.Int32Value;

            /**
             * Verifies an Int32Value message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an Int32Value message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Int32Value
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.Int32Value;

            /**
             * Creates a plain object from an Int32Value message. Also converts values to other types if specified.
             * @param message Int32Value
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.Int32Value, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Int32Value to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for Int32Value
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a UInt32Value. */
        interface IUInt32Value {

            /** UInt32Value value */
            value?: (number|null);
        }

        /** Represents a UInt32Value. */
        class UInt32Value implements IUInt32Value {

            /**
             * Constructs a new UInt32Value.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IUInt32Value);

            /** UInt32Value value. */
            public value: number;

            /**
             * Creates a new UInt32Value instance using the specified properties.
             * @param [properties] Properties to set
             * @returns UInt32Value instance
             */
            public static create(properties?: google.protobuf.IUInt32Value): google.protobuf.UInt32Value;

            /**
             * Encodes the specified UInt32Value message. Does not implicitly {@link google.protobuf.UInt32Value.verify|verify} messages.
             * @param message UInt32Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IUInt32Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified UInt32Value message, length delimited. Does not implicitly {@link google.protobuf.UInt32Value.verify|verify} messages.
             * @param message UInt32Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IUInt32Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a UInt32Value message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UInt32Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UInt32Value;

            /**
             * Decodes a UInt32Value message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns UInt32Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.UInt32Value;

            /**
             * Verifies a UInt32Value message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a UInt32Value message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UInt32Value
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.UInt32Value;

            /**
             * Creates a plain object from a UInt32Value message. Also converts values to other types if specified.
             * @param message UInt32Value
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.UInt32Value, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UInt32Value to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for UInt32Value
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a BoolValue. */
        interface IBoolValue {

            /** BoolValue value */
            value?: (boolean|null);
        }

        /** Represents a BoolValue. */
        class BoolValue implements IBoolValue {

            /**
             * Constructs a new BoolValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IBoolValue);

            /** BoolValue value. */
            public value: boolean;

            /**
             * Creates a new BoolValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns BoolValue instance
             */
            public static create(properties?: google.protobuf.IBoolValue): google.protobuf.BoolValue;

            /**
             * Encodes the specified BoolValue message. Does not implicitly {@link google.protobuf.BoolValue.verify|verify} messages.
             * @param message BoolValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IBoolValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified BoolValue message, length delimited. Does not implicitly {@link google.protobuf.BoolValue.verify|verify} messages.
             * @param message BoolValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IBoolValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a BoolValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns BoolValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.BoolValue;

            /**
             * Decodes a BoolValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns BoolValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.BoolValue;

            /**
             * Verifies a BoolValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a BoolValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns BoolValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.BoolValue;

            /**
             * Creates a plain object from a BoolValue message. Also converts values to other types if specified.
             * @param message BoolValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.BoolValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this BoolValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for BoolValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a StringValue. */
        interface IStringValue {

            /** StringValue value */
            value?: (string|null);
        }

        /** Represents a StringValue. */
        class StringValue implements IStringValue {

            /**
             * Constructs a new StringValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IStringValue);

            /** StringValue value. */
            public value: string;

            /**
             * Creates a new StringValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns StringValue instance
             */
            public static create(properties?: google.protobuf.IStringValue): google.protobuf.StringValue;

            /**
             * Encodes the specified StringValue message. Does not implicitly {@link google.protobuf.StringValue.verify|verify} messages.
             * @param message StringValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IStringValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified StringValue message, length delimited. Does not implicitly {@link google.protobuf.StringValue.verify|verify} messages.
             * @param message StringValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IStringValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a StringValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns StringValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.StringValue;

            /**
             * Decodes a StringValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns StringValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.StringValue;

            /**
             * Verifies a StringValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a StringValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns StringValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.StringValue;

            /**
             * Creates a plain object from a StringValue message. Also converts values to other types if specified.
             * @param message StringValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.StringValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this StringValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for StringValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a BytesValue. */
        interface IBytesValue {

            /** BytesValue value */
            value?: (Uint8Array|null);
        }

        /** Represents a BytesValue. */
        class BytesValue implements IBytesValue {

            /**
             * Constructs a new BytesValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IBytesValue);

            /** BytesValue value. */
            public value: Uint8Array;

            /**
             * Creates a new BytesValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns BytesValue instance
             */
            public static create(properties?: google.protobuf.IBytesValue): google.protobuf.BytesValue;

            /**
             * Encodes the specified BytesValue message. Does not implicitly {@link google.protobuf.BytesValue.verify|verify} messages.
             * @param message BytesValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IBytesValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified BytesValue message, length delimited. Does not implicitly {@link google.protobuf.BytesValue.verify|verify} messages.
             * @param message BytesValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IBytesValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a BytesValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns BytesValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.BytesValue;

            /**
             * Decodes a BytesValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns BytesValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.BytesValue;

            /**
             * Verifies a BytesValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a BytesValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns BytesValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.BytesValue;

            /**
             * Creates a plain object from a BytesValue message. Also converts values to other types if specified.
             * @param message BytesValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.BytesValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this BytesValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for BytesValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }
    }
}
