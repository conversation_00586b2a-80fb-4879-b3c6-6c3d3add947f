syntax = "proto3";
import "google/protobuf/wrappers.proto";

message MsgDeliverReq {
    int64 targetId = 1; // Target room ID
    string msgType = 2; // Message type
    bytes msgBody = 3;  // Message content
    string sign = 4;    // Signature
    string callbackId = 5; // Callback ID
    MsgUserInfo stubInfo = 6; // User basic information
    int32 optHandle = 7; // Operation ID (message verification)
}

message MsgUserInfo {
    int64 userId = 1;  // User ID
    string nickname = 2; // Nickname
    string avatar = 3; // Avatar
    int32 sex = 4;     // Gender: 0 not set, 1 male, 2 female
    string sessionToken = 5; // Session token
    string version = 6; // App version number
}

message BattleRaisePO {
    int32 raiseScore = 1;
    int32 reSend = 2;
}

message BattleSitMsg {
    int32 seatNum = 1;
    double lon = 2; // Longitude
    double lat = 3; // Latitude
}

message BattleOpenCardReq {
    int32 card = 1;
    int32 optType = 2; // 0: hide, 1: show
}

message BattleAutoOptPO {
    int32 preOptType = 1; // 0: cancel, 1: check or fold, 2: check, 3: call
}

message BattleScorePO {
    int32 score = 1;
    string passwd = 2; // Password
}

message BattleDoubleChooseReq {
    int32 chooseStatus = 1;
}

message MsgTypeMsgPO {
    int32 type = 1;
    string content = 2;
    int32 second = 3;
    int64 createTime = 4;
    string id = 5; // Message ID
}

message EmojiMsgPO {
    int32 type = 1;
    string content = 2;
    int64 receiverId = 3;
    int32 num = 4;
    int32 emojiId = 5;
    int32 aniType = 6;
}

message ForceStandReq {
    int64 standUserId = 1;
}

message LookComCardsReq {
    int32 handNum = 1;
    int32 reSend = 2;
}

message ShieldUserMsgReq {
    int32 type = 1;
    int64 targetUserId = 2;
}

message OptTargetPB {
    int64 targetId = 1; // Operation target (target user ID)
    string ext = 2;     // Extension parameters (for extending other messages)
}

message DiamondSendMsgPO {
	int64 receiverId = 1;
	int32 diamondId = 2;
    string passwd = 3; // Payment password
}


message GuessHandBetPO {
    int32 type = 1; // Bet type: 1: bet, 2: continuous bet setting
    int32 bet = 2; // Bet option: 1: bet option 1, 2: bet option 2
    int32 oddsId = 3; // Bet odds type ID
    int32 continuous = 4; // Continuous bet configuration: 1: enable continuous bet, -1: disable continuous bet
}


message RetraceScorePO {
    int32 type = 1;  // Retraction type: 1: manual retraction, 2: automatic retraction
    int32 score = 2; // Score to retract: 0: no retraction
}


message JoinRoomReq {
    int32 type = 1;  // Join type: 1: preload data (only notify C_updateRoomNotify), 0/other values: normal entry
}

//Chinese poker msgs start from here
//Place card
message PlaceCardPO{
    string location = 1;
    int32 index = 2;
    int32 card = 3;
}

//Used for placing multiple cards at once
message MultiPlaceCardPO{
    repeated PlaceCardPO locations = 1;
    string specialHandType = 2;
    int32 moveCard = 3;
}

//Replace card
message ReplaceCardPO{
    repeated PlaceCardPO locations = 1;
}


//Swap
message SwapCardPO{
    repeated PlaceCardPO locations = 1;
}

//To set the bet
message BetPO{
    int32 value = 1;
}

//Sort
message SortPO{
    string orderBy = 1;
}

message SpecialHandComparisonDecisionPO{
    string useSpecialHand = 1;
}

message RealHeadPO {
    google.protobuf.StringValue head = 1;
    google.protobuf.Int64Value userId = 2;
}

/**
Buy game insurance operation
 */
message InsuranceBuyPO{
    int32 type = 1; // -1: don't buy, 0: not purchased, 1: full pot, 2: 1/2 pot, 3: 1/3 pot, 4: 1/8 pot, 5: 1/5 pot, 6: break even, 7: only buy back insurance
    int32 buyRound = 2; // Buy round: 1 for flop, 2 for turn
}
