import * as $protobuf from "protobufjs";
import Long = require("long");
/** Properties of a MsgDeliverResp. */
export interface IMsgDeliverResp {

    /** MsgDeliverResp errorCode */
    errorCode?: (google.protobuf.IInt32Value|null);

    /** MsgDeliverResp errMsg */
    errMsg?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp sysTime */
    sysTime?: (google.protobuf.IInt64Value|null);

    /** MsgDeliverResp msgType */
    msgType?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp msgBody */
    msgBody?: (Uint8Array|null);

    /** MsgDeliverResp originOpt */
    originOpt?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp callbackId */
    callbackId?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp optHandle */
    optHandle?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp protoType */
    protoType?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp newToken */
    newToken?: (google.protobuf.IStringValue|null);
}

/** Represents a MsgDeliverResp. */
export class MsgDeliverResp implements IMsgDeliverResp {

    /**
     * Constructs a new MsgDeliverResp.
     * @param [properties] Properties to set
     */
    constructor(properties?: IMsgDeliverResp);

    /** MsgDeliverResp errorCode. */
    public errorCode?: (google.protobuf.IInt32Value|null);

    /** MsgDeliverResp errMsg. */
    public errMsg?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp sysTime. */
    public sysTime?: (google.protobuf.IInt64Value|null);

    /** MsgDeliverResp msgType. */
    public msgType?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp msgBody. */
    public msgBody: Uint8Array;

    /** MsgDeliverResp originOpt. */
    public originOpt?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp callbackId. */
    public callbackId?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp optHandle. */
    public optHandle?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp protoType. */
    public protoType?: (google.protobuf.IStringValue|null);

    /** MsgDeliverResp newToken. */
    public newToken?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new MsgDeliverResp instance using the specified properties.
     * @param [properties] Properties to set
     * @returns MsgDeliverResp instance
     */
    public static create(properties?: IMsgDeliverResp): MsgDeliverResp;

    /**
     * Encodes the specified MsgDeliverResp message. Does not implicitly {@link MsgDeliverResp.verify|verify} messages.
     * @param message MsgDeliverResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IMsgDeliverResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified MsgDeliverResp message, length delimited. Does not implicitly {@link MsgDeliverResp.verify|verify} messages.
     * @param message MsgDeliverResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IMsgDeliverResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a MsgDeliverResp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MsgDeliverResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): MsgDeliverResp;

    /**
     * Decodes a MsgDeliverResp message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns MsgDeliverResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): MsgDeliverResp;

    /**
     * Verifies a MsgDeliverResp message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a MsgDeliverResp message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns MsgDeliverResp
     */
    public static fromObject(object: { [k: string]: any }): MsgDeliverResp;

    /**
     * Creates a plain object from a MsgDeliverResp message. Also converts values to other types if specified.
     * @param message MsgDeliverResp
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: MsgDeliverResp, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this MsgDeliverResp to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for MsgDeliverResp
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleDealCardPOList. */
export interface IBattleDealCardPOList {

    /** BattleDealCardPOList list */
    list?: (IBattleDealCardPO[]|null);
}

/** Represents a BattleDealCardPOList. */
export class BattleDealCardPOList implements IBattleDealCardPOList {

    /**
     * Constructs a new BattleDealCardPOList.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleDealCardPOList);

    /** BattleDealCardPOList list. */
    public list: IBattleDealCardPO[];

    /**
     * Creates a new BattleDealCardPOList instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleDealCardPOList instance
     */
    public static create(properties?: IBattleDealCardPOList): BattleDealCardPOList;

    /**
     * Encodes the specified BattleDealCardPOList message. Does not implicitly {@link BattleDealCardPOList.verify|verify} messages.
     * @param message BattleDealCardPOList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleDealCardPOList, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleDealCardPOList message, length delimited. Does not implicitly {@link BattleDealCardPOList.verify|verify} messages.
     * @param message BattleDealCardPOList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleDealCardPOList, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleDealCardPOList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleDealCardPOList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleDealCardPOList;

    /**
     * Decodes a BattleDealCardPOList message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleDealCardPOList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleDealCardPOList;

    /**
     * Verifies a BattleDealCardPOList message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleDealCardPOList message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleDealCardPOList
     */
    public static fromObject(object: { [k: string]: any }): BattleDealCardPOList;

    /**
     * Creates a plain object from a BattleDealCardPOList message. Also converts values to other types if specified.
     * @param message BattleDealCardPOList
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleDealCardPOList, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleDealCardPOList to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleDealCardPOList
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleDealCardPO. */
export interface IBattleDealCardPO {

    /** 手牌list */
    handCards?: (google.protobuf.IInt32Value[]|null);

    /** 用户ID */
    userId?: (google.protobuf.IInt64Value|null);

    /** 卡牌类型 */
    cardType?: (google.protobuf.IStringValue|null);

    /** 游戏状态 */
    gameStatus?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleDealCardPO. */
export class BattleDealCardPO implements IBattleDealCardPO {

    /**
     * Constructs a new BattleDealCardPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleDealCardPO);

    /** 手牌list */
    public handCards: google.protobuf.IInt32Value[];

    /** 用户ID */
    public userId?: (google.protobuf.IInt64Value|null);

    /** 卡牌类型 */
    public cardType?: (google.protobuf.IStringValue|null);

    /** 游戏状态 */
    public gameStatus?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleDealCardPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleDealCardPO instance
     */
    public static create(properties?: IBattleDealCardPO): BattleDealCardPO;

    /**
     * Encodes the specified BattleDealCardPO message. Does not implicitly {@link BattleDealCardPO.verify|verify} messages.
     * @param message BattleDealCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleDealCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleDealCardPO message, length delimited. Does not implicitly {@link BattleDealCardPO.verify|verify} messages.
     * @param message BattleDealCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleDealCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleDealCardPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleDealCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleDealCardPO;

    /**
     * Decodes a BattleDealCardPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleDealCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleDealCardPO;

    /**
     * Verifies a BattleDealCardPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleDealCardPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleDealCardPO
     */
    public static fromObject(object: { [k: string]: any }): BattleDealCardPO;

    /**
     * Creates a plain object from a BattleDealCardPO message. Also converts values to other types if specified.
     * @param message BattleDealCardPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleDealCardPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleDealCardPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleDealCardPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleRoomUserMsg. */
export interface IBattleRoomUserMsg {

    /** BattleRoomUserMsg nickname */
    nickname?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg avatar */
    avatar?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg sex */
    sex?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg seatNum */
    seatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg handCards */
    handCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoomUserMsg publicCards */
    publicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoomUserMsg actionPO */
    actionPO?: (IBattleActionPO|null);

    /** BattleRoomUserMsg gameStatus */
    gameStatus?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg brandType */
    brandType?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg maxCardCombination */
    maxCardCombination?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoomUserMsg userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomUserMsg currentScore */
    currentScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg buyScore */
    buyScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg inRoomStatus */
    inRoomStatus?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg isOnline */
    isOnline?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg countDown */
    countDown?: (google.protobuf.IInt64Value|null);

    /** BattleRoomUserMsg cardType */
    cardType?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg preOptType */
    preOptType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg playBoutNum */
    playBoutNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg publicHandCards */
    publicHandCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoomUserMsg optHandle */
    optHandle?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg roomDetail */
    roomDetail?: (IBattleRoomDetailResp|null);

    /** BattleRoomUserMsg currRankNum */
    currRankNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg remark */
    remark?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg highHandCards */
    highHandCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoomUserMsg remarkDetail */
    remarkDetail?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg squidWin */
    squidWin?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg realHead */
    realHead?: (google.protobuf.IStringValue|null);
}

/** Represents a BattleRoomUserMsg. */
export class BattleRoomUserMsg implements IBattleRoomUserMsg {

    /**
     * Constructs a new BattleRoomUserMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleRoomUserMsg);

    /** BattleRoomUserMsg nickname. */
    public nickname?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg avatar. */
    public avatar?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg sex. */
    public sex?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg seatNum. */
    public seatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg handCards. */
    public handCards: google.protobuf.IInt32Value[];

    /** BattleRoomUserMsg publicCards. */
    public publicCards: google.protobuf.IInt32Value[];

    /** BattleRoomUserMsg actionPO. */
    public actionPO?: (IBattleActionPO|null);

    /** BattleRoomUserMsg gameStatus. */
    public gameStatus?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg brandType. */
    public brandType?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg maxCardCombination. */
    public maxCardCombination: google.protobuf.IInt32Value[];

    /** BattleRoomUserMsg userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomUserMsg currentScore. */
    public currentScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg buyScore. */
    public buyScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg inRoomStatus. */
    public inRoomStatus?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg isOnline. */
    public isOnline?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg countDown. */
    public countDown?: (google.protobuf.IInt64Value|null);

    /** BattleRoomUserMsg cardType. */
    public cardType?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg preOptType. */
    public preOptType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg playBoutNum. */
    public playBoutNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg publicHandCards. */
    public publicHandCards: google.protobuf.IInt32Value[];

    /** BattleRoomUserMsg optHandle. */
    public optHandle?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg roomDetail. */
    public roomDetail?: (IBattleRoomDetailResp|null);

    /** BattleRoomUserMsg currRankNum. */
    public currRankNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg remark. */
    public remark?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg highHandCards. */
    public highHandCards: google.protobuf.IInt32Value[];

    /** BattleRoomUserMsg remarkDetail. */
    public remarkDetail?: (google.protobuf.IStringValue|null);

    /** BattleRoomUserMsg squidWin. */
    public squidWin?: (google.protobuf.IInt32Value|null);

    /** BattleRoomUserMsg realHead. */
    public realHead?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new BattleRoomUserMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleRoomUserMsg instance
     */
    public static create(properties?: IBattleRoomUserMsg): BattleRoomUserMsg;

    /**
     * Encodes the specified BattleRoomUserMsg message. Does not implicitly {@link BattleRoomUserMsg.verify|verify} messages.
     * @param message BattleRoomUserMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleRoomUserMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleRoomUserMsg message, length delimited. Does not implicitly {@link BattleRoomUserMsg.verify|verify} messages.
     * @param message BattleRoomUserMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleRoomUserMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleRoomUserMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleRoomUserMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleRoomUserMsg;

    /**
     * Decodes a BattleRoomUserMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleRoomUserMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleRoomUserMsg;

    /**
     * Verifies a BattleRoomUserMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleRoomUserMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleRoomUserMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleRoomUserMsg;

    /**
     * Creates a plain object from a BattleRoomUserMsg message. Also converts values to other types if specified.
     * @param message BattleRoomUserMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleRoomUserMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleRoomUserMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleRoomUserMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleSimpleUserPO. */
export interface IBattleSimpleUserPO {

    /** BattleSimpleUserPO nickname */
    nickname?: (google.protobuf.IStringValue|null);

    /** BattleSimpleUserPO avatar */
    avatar?: (google.protobuf.IStringValue|null);

    /** BattleSimpleUserPO sex */
    sex?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleSimpleUserPO inRoomStatus */
    inRoomStatus?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO buyScore */
    buyScore?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO currentScore */
    currentScore?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO createTime */
    createTime?: (google.protobuf.IInt64Value|null);

    /** BattleSimpleUserPO playBoutNum */
    playBoutNum?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO isAdmin */
    isAdmin?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO currRankNum */
    currRankNum?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO sngRank */
    sngRank?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO changeScore */
    changeScore?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO squidWin */
    squidWin?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleSimpleUserPO. */
export class BattleSimpleUserPO implements IBattleSimpleUserPO {

    /**
     * Constructs a new BattleSimpleUserPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleSimpleUserPO);

    /** BattleSimpleUserPO nickname. */
    public nickname?: (google.protobuf.IStringValue|null);

    /** BattleSimpleUserPO avatar. */
    public avatar?: (google.protobuf.IStringValue|null);

    /** BattleSimpleUserPO sex. */
    public sex?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleSimpleUserPO inRoomStatus. */
    public inRoomStatus?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO buyScore. */
    public buyScore?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO currentScore. */
    public currentScore?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO createTime. */
    public createTime?: (google.protobuf.IInt64Value|null);

    /** BattleSimpleUserPO playBoutNum. */
    public playBoutNum?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO isAdmin. */
    public isAdmin?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO currRankNum. */
    public currRankNum?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO sngRank. */
    public sngRank?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO changeScore. */
    public changeScore?: (google.protobuf.IInt32Value|null);

    /** BattleSimpleUserPO squidWin. */
    public squidWin?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleSimpleUserPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleSimpleUserPO instance
     */
    public static create(properties?: IBattleSimpleUserPO): BattleSimpleUserPO;

    /**
     * Encodes the specified BattleSimpleUserPO message. Does not implicitly {@link BattleSimpleUserPO.verify|verify} messages.
     * @param message BattleSimpleUserPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleSimpleUserPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleSimpleUserPO message, length delimited. Does not implicitly {@link BattleSimpleUserPO.verify|verify} messages.
     * @param message BattleSimpleUserPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleSimpleUserPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleSimpleUserPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleSimpleUserPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleSimpleUserPO;

    /**
     * Decodes a BattleSimpleUserPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleSimpleUserPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleSimpleUserPO;

    /**
     * Verifies a BattleSimpleUserPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleSimpleUserPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleSimpleUserPO
     */
    public static fromObject(object: { [k: string]: any }): BattleSimpleUserPO;

    /**
     * Creates a plain object from a BattleSimpleUserPO message. Also converts values to other types if specified.
     * @param message BattleSimpleUserPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleSimpleUserPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleSimpleUserPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleSimpleUserPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleRoomDetailResp. */
export interface IBattleRoomDetailResp {

    /** BattleRoomDetailResp lookList */
    lookList?: (IBattleSimpleUserPO[]|null);

    /** BattleRoomDetailResp buyScoreList */
    buyScoreList?: (IBattleSimpleUserPO[]|null);

    /** BattleRoomDetailResp gameFund */
    gameFund?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp curGameFund */
    curGameFund?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp currentBoutNum */
    currentBoutNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp totalBuyScore */
    totalBuyScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp totalDealScore */
    totalDealScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp avgPotScore */
    avgPotScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp duration */
    duration?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp residueSec */
    residueSec?: (google.protobuf.IInt64Value|null);

    /** BattleRoomDetailResp gameFundRate */
    gameFundRate?: (google.protobuf.IDoubleValue|null);

    /** BattleRoomDetailResp isAdmin */
    isAdmin?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp errorCode */
    errorCode?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp errMsg */
    errMsg?: (google.protobuf.IStringValue|null);

    /** BattleRoomDetailResp sysTime */
    sysTime?: (google.protobuf.IInt64Value|null);

    /** BattleRoomDetailResp curSB */
    curSB?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp nextSB */
    nextSB?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp usedSec */
    usedSec?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp useWallet */
    useWallet?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp forceLive */
    forceLive?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp insuranceTotal */
    insuranceTotal?: (google.protobuf.IInt64Value|null);
}

/** Represents a BattleRoomDetailResp. */
export class BattleRoomDetailResp implements IBattleRoomDetailResp {

    /**
     * Constructs a new BattleRoomDetailResp.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleRoomDetailResp);

    /** BattleRoomDetailResp lookList. */
    public lookList: IBattleSimpleUserPO[];

    /** BattleRoomDetailResp buyScoreList. */
    public buyScoreList: IBattleSimpleUserPO[];

    /** BattleRoomDetailResp gameFund. */
    public gameFund?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp curGameFund. */
    public curGameFund?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp currentBoutNum. */
    public currentBoutNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp totalBuyScore. */
    public totalBuyScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp totalDealScore. */
    public totalDealScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp avgPotScore. */
    public avgPotScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp duration. */
    public duration?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp residueSec. */
    public residueSec?: (google.protobuf.IInt64Value|null);

    /** BattleRoomDetailResp gameFundRate. */
    public gameFundRate?: (google.protobuf.IDoubleValue|null);

    /** BattleRoomDetailResp isAdmin. */
    public isAdmin?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp errorCode. */
    public errorCode?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp errMsg. */
    public errMsg?: (google.protobuf.IStringValue|null);

    /** BattleRoomDetailResp sysTime. */
    public sysTime?: (google.protobuf.IInt64Value|null);

    /** BattleRoomDetailResp curSB. */
    public curSB?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp nextSB. */
    public nextSB?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp usedSec. */
    public usedSec?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp useWallet. */
    public useWallet?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp forceLive. */
    public forceLive?: (google.protobuf.IInt32Value|null);

    /** BattleRoomDetailResp insuranceTotal. */
    public insuranceTotal?: (google.protobuf.IInt64Value|null);

    /**
     * Creates a new BattleRoomDetailResp instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleRoomDetailResp instance
     */
    public static create(properties?: IBattleRoomDetailResp): BattleRoomDetailResp;

    /**
     * Encodes the specified BattleRoomDetailResp message. Does not implicitly {@link BattleRoomDetailResp.verify|verify} messages.
     * @param message BattleRoomDetailResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleRoomDetailResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleRoomDetailResp message, length delimited. Does not implicitly {@link BattleRoomDetailResp.verify|verify} messages.
     * @param message BattleRoomDetailResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleRoomDetailResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleRoomDetailResp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleRoomDetailResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleRoomDetailResp;

    /**
     * Decodes a BattleRoomDetailResp message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleRoomDetailResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleRoomDetailResp;

    /**
     * Verifies a BattleRoomDetailResp message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleRoomDetailResp message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleRoomDetailResp
     */
    public static fromObject(object: { [k: string]: any }): BattleRoomDetailResp;

    /**
     * Creates a plain object from a BattleRoomDetailResp message. Also converts values to other types if specified.
     * @param message BattleRoomDetailResp
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleRoomDetailResp, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleRoomDetailResp to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleRoomDetailResp
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a GradeCfgPO. */
export interface IGradeCfgPO {

    /** GradeCfgPO bigBlind */
    bigBlind?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO smallBlind */
    smallBlind?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO ante */
    ante?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO buyScore */
    buyScore?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO collectNum */
    collectNum?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO lookPublicCardNum */
    lookPublicCardNum?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO cfgList */
    cfgList?: (IDealProlongCfg[]|null);

    /** GradeCfgPO upBlindTime */
    upBlindTime?: (google.protobuf.IInt64Value|null);

    /** GradeCfgPO fundCapBb */
    fundCapBb?: (google.protobuf.IDoubleValue|null);
}

/** Represents a GradeCfgPO. */
export class GradeCfgPO implements IGradeCfgPO {

    /**
     * Constructs a new GradeCfgPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IGradeCfgPO);

    /** GradeCfgPO bigBlind. */
    public bigBlind?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO smallBlind. */
    public smallBlind?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO ante. */
    public ante?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO buyScore. */
    public buyScore?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO collectNum. */
    public collectNum?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO lookPublicCardNum. */
    public lookPublicCardNum?: (google.protobuf.IInt32Value|null);

    /** GradeCfgPO cfgList. */
    public cfgList: IDealProlongCfg[];

    /** GradeCfgPO upBlindTime. */
    public upBlindTime?: (google.protobuf.IInt64Value|null);

    /** GradeCfgPO fundCapBb. */
    public fundCapBb?: (google.protobuf.IDoubleValue|null);

    /**
     * Creates a new GradeCfgPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns GradeCfgPO instance
     */
    public static create(properties?: IGradeCfgPO): GradeCfgPO;

    /**
     * Encodes the specified GradeCfgPO message. Does not implicitly {@link GradeCfgPO.verify|verify} messages.
     * @param message GradeCfgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IGradeCfgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified GradeCfgPO message, length delimited. Does not implicitly {@link GradeCfgPO.verify|verify} messages.
     * @param message GradeCfgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IGradeCfgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a GradeCfgPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns GradeCfgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): GradeCfgPO;

    /**
     * Decodes a GradeCfgPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns GradeCfgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): GradeCfgPO;

    /**
     * Verifies a GradeCfgPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a GradeCfgPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns GradeCfgPO
     */
    public static fromObject(object: { [k: string]: any }): GradeCfgPO;

    /**
     * Creates a plain object from a GradeCfgPO message. Also converts values to other types if specified.
     * @param message GradeCfgPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: GradeCfgPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this GradeCfgPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for GradeCfgPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a DealProlongCfg. */
export interface IDealProlongCfg {

    /** DealProlongCfg dealType */
    dealType?: (google.protobuf.IInt32Value|null);

    /** DealProlongCfg amount */
    amount?: (google.protobuf.IInt32Value|null);

    /** DealProlongCfg timeSec */
    timeSec?: (google.protobuf.IInt32Value|null);
}

/** Represents a DealProlongCfg. */
export class DealProlongCfg implements IDealProlongCfg {

    /**
     * Constructs a new DealProlongCfg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IDealProlongCfg);

    /** DealProlongCfg dealType. */
    public dealType?: (google.protobuf.IInt32Value|null);

    /** DealProlongCfg amount. */
    public amount?: (google.protobuf.IInt32Value|null);

    /** DealProlongCfg timeSec. */
    public timeSec?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new DealProlongCfg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns DealProlongCfg instance
     */
    public static create(properties?: IDealProlongCfg): DealProlongCfg;

    /**
     * Encodes the specified DealProlongCfg message. Does not implicitly {@link DealProlongCfg.verify|verify} messages.
     * @param message DealProlongCfg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IDealProlongCfg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified DealProlongCfg message, length delimited. Does not implicitly {@link DealProlongCfg.verify|verify} messages.
     * @param message DealProlongCfg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IDealProlongCfg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a DealProlongCfg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DealProlongCfg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): DealProlongCfg;

    /**
     * Decodes a DealProlongCfg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns DealProlongCfg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): DealProlongCfg;

    /**
     * Verifies a DealProlongCfg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a DealProlongCfg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns DealProlongCfg
     */
    public static fromObject(object: { [k: string]: any }): DealProlongCfg;

    /**
     * Creates a plain object from a DealProlongCfg message. Also converts values to other types if specified.
     * @param message DealProlongCfg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: DealProlongCfg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this DealProlongCfg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for DealProlongCfg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleRoomMsg. */
export interface IBattleRoomMsg {

    /** BattleRoomMsg optSeatNum */
    optSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg sitUserList */
    sitUserList?: (IBattleRoomUserMsg[]|null);

    /** BattleRoomMsg roomId */
    roomId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg roomNumber */
    roomNumber?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg title */
    title?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg clubId */
    clubId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg masterUserId */
    masterUserId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg roomStatus */
    roomStatus?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg createTime */
    createTime?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg gamePersonNum */
    gamePersonNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg currentBoutNum */
    currentBoutNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg gradeCfg */
    gradeCfg?: (IGradeCfgPO|null);

    /** BattleRoomMsg isNeedAgreeSit */
    isNeedAgreeSit?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg minScoreMultiple */
    minScoreMultiple?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg maxScoreMultiple */
    maxScoreMultiple?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg duration */
    duration?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg startTime */
    startTime?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg isAnte */
    isAnte?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg gameFund */
    gameFund?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isSendDouble */
    isSendDouble?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg roomType */
    roomType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isLimitIp */
    isLimitIp?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg bankerUserId */
    bankerUserId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg publicCards */
    publicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoomMsg round */
    round?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg isPause */
    isPause?: (google.protobuf.IBoolValue|null);

    /** BattleRoomMsg countDown */
    countDown?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg totalCountDown */
    totalCountDown?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg currentUserSeatNum */
    currentUserSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg bBSeatNum */
    bBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isDoubleAnte */
    isDoubleAnte?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg playType */
    playType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg sBSeatNum */
    sBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg totalPot */
    totalPot?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg potList */
    potList?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoomMsg clubName */
    clubName?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg masterNickname */
    masterNickname?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg masterAvatar */
    masterAvatar?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg curAction */
    curAction?: (IBattleActionPO|null);

    /** BattleRoomMsg residueSec */
    residueSec?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg prolongCfg */
    prolongCfg?: (IBattleProlongPO|null);

    /** BattleRoomMsg bankerSeatNum */
    bankerSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg curRoundMaxBetScore */
    curRoundMaxBetScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg oldPublicCards */
    oldPublicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoomMsg highPublicCards */
    highPublicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoomMsg oldThanResult */
    oldThanResult?: (IBattleDoubleThanResultMsg|null);

    /** BattleRoomMsg thanResult */
    thanResult?: (IBattleDoubleThanResultMsg|null);

    /** BattleRoomMsg seeComCardCfg */
    seeComCardCfg?: (IBattleProlongPO|null);

    /** BattleRoomMsg shortCompareType */
    shortCompareType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg gameFundRate */
    gameFundRate?: (google.protobuf.IDoubleValue|null);

    /** BattleRoomMsg isLimitScore */
    isLimitScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg originOptUserId */
    originOptUserId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg roomDetail */
    roomDetail?: (IBattleRoomDetailResp|null);

    /** BattleRoomMsg currRankNum */
    currRankNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg grade */
    grade?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg usedSec */
    usedSec?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg isRandSeat */
    isRandSeat?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg liveInfo */
    liveInfo?: (IBattleLiveInfoPO|null);

    /** BattleRoomMsg forceSeeCardCfg */
    forceSeeCardCfg?: (IBattleForceSeeCardCfgPO|null);

    /** BattleRoomMsg isThirdBlind */
    isThirdBlind?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg tBSeatNum */
    tBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg minBuyInScore */
    minBuyInScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg maxBuyInScore */
    maxBuyInScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isAdmin */
    isAdmin?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg bigPotNum */
    bigPotNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg mulTableUserNum */
    mulTableUserNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg lastCritTime */
    lastCritTime?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg critBBTimes */
    critBBTimes?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg critMomentType */
    critMomentType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg critMinute */
    critMinute?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isInsurance */
    isInsurance?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleRoomMsg. */
export class BattleRoomMsg implements IBattleRoomMsg {

    /**
     * Constructs a new BattleRoomMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleRoomMsg);

    /** BattleRoomMsg optSeatNum. */
    public optSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg sitUserList. */
    public sitUserList: IBattleRoomUserMsg[];

    /** BattleRoomMsg roomId. */
    public roomId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg roomNumber. */
    public roomNumber?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg title. */
    public title?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg clubId. */
    public clubId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg masterUserId. */
    public masterUserId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg roomStatus. */
    public roomStatus?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg createTime. */
    public createTime?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg gamePersonNum. */
    public gamePersonNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg currentBoutNum. */
    public currentBoutNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg gradeCfg. */
    public gradeCfg?: (IGradeCfgPO|null);

    /** BattleRoomMsg isNeedAgreeSit. */
    public isNeedAgreeSit?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg minScoreMultiple. */
    public minScoreMultiple?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg maxScoreMultiple. */
    public maxScoreMultiple?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg duration. */
    public duration?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg startTime. */
    public startTime?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg isAnte. */
    public isAnte?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg gameFund. */
    public gameFund?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isSendDouble. */
    public isSendDouble?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg roomType. */
    public roomType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isLimitIp. */
    public isLimitIp?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg bankerUserId. */
    public bankerUserId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg publicCards. */
    public publicCards: google.protobuf.IInt32Value[];

    /** BattleRoomMsg round. */
    public round?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg isPause. */
    public isPause?: (google.protobuf.IBoolValue|null);

    /** BattleRoomMsg countDown. */
    public countDown?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg totalCountDown. */
    public totalCountDown?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg currentUserSeatNum. */
    public currentUserSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg bBSeatNum. */
    public bBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isDoubleAnte. */
    public isDoubleAnte?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg playType. */
    public playType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg sBSeatNum. */
    public sBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg totalPot. */
    public totalPot?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg potList. */
    public potList: google.protobuf.IInt32Value[];

    /** BattleRoomMsg clubName. */
    public clubName?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg masterNickname. */
    public masterNickname?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg masterAvatar. */
    public masterAvatar?: (google.protobuf.IStringValue|null);

    /** BattleRoomMsg curAction. */
    public curAction?: (IBattleActionPO|null);

    /** BattleRoomMsg residueSec. */
    public residueSec?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg prolongCfg. */
    public prolongCfg?: (IBattleProlongPO|null);

    /** BattleRoomMsg bankerSeatNum. */
    public bankerSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg curRoundMaxBetScore. */
    public curRoundMaxBetScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg oldPublicCards. */
    public oldPublicCards: google.protobuf.IInt32Value[];

    /** BattleRoomMsg highPublicCards. */
    public highPublicCards: google.protobuf.IInt32Value[];

    /** BattleRoomMsg oldThanResult. */
    public oldThanResult?: (IBattleDoubleThanResultMsg|null);

    /** BattleRoomMsg thanResult. */
    public thanResult?: (IBattleDoubleThanResultMsg|null);

    /** BattleRoomMsg seeComCardCfg. */
    public seeComCardCfg?: (IBattleProlongPO|null);

    /** BattleRoomMsg shortCompareType. */
    public shortCompareType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg gameFundRate. */
    public gameFundRate?: (google.protobuf.IDoubleValue|null);

    /** BattleRoomMsg isLimitScore. */
    public isLimitScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg originOptUserId. */
    public originOptUserId?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg roomDetail. */
    public roomDetail?: (IBattleRoomDetailResp|null);

    /** BattleRoomMsg currRankNum. */
    public currRankNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg grade. */
    public grade?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg usedSec. */
    public usedSec?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg isRandSeat. */
    public isRandSeat?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg liveInfo. */
    public liveInfo?: (IBattleLiveInfoPO|null);

    /** BattleRoomMsg forceSeeCardCfg. */
    public forceSeeCardCfg?: (IBattleForceSeeCardCfgPO|null);

    /** BattleRoomMsg isThirdBlind. */
    public isThirdBlind?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg tBSeatNum. */
    public tBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg minBuyInScore. */
    public minBuyInScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg maxBuyInScore. */
    public maxBuyInScore?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isAdmin. */
    public isAdmin?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg bigPotNum. */
    public bigPotNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg mulTableUserNum. */
    public mulTableUserNum?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg lastCritTime. */
    public lastCritTime?: (google.protobuf.IInt64Value|null);

    /** BattleRoomMsg critBBTimes. */
    public critBBTimes?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg critMomentType. */
    public critMomentType?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg critMinute. */
    public critMinute?: (google.protobuf.IInt32Value|null);

    /** BattleRoomMsg isInsurance. */
    public isInsurance?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleRoomMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleRoomMsg instance
     */
    public static create(properties?: IBattleRoomMsg): BattleRoomMsg;

    /**
     * Encodes the specified BattleRoomMsg message. Does not implicitly {@link BattleRoomMsg.verify|verify} messages.
     * @param message BattleRoomMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleRoomMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleRoomMsg message, length delimited. Does not implicitly {@link BattleRoomMsg.verify|verify} messages.
     * @param message BattleRoomMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleRoomMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleRoomMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleRoomMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleRoomMsg;

    /**
     * Decodes a BattleRoomMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleRoomMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleRoomMsg;

    /**
     * Verifies a BattleRoomMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleRoomMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleRoomMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleRoomMsg;

    /**
     * Creates a plain object from a BattleRoomMsg message. Also converts values to other types if specified.
     * @param message BattleRoomMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleRoomMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleRoomMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleRoomMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleLiveInfoPO. */
export interface IBattleLiveInfoPO {

    /** BattleLiveInfoPO forceLive */
    forceLive?: (google.protobuf.IInt32Value|null);

    /** BattleLiveInfoPO liveToken */
    liveToken?: (google.protobuf.IStringValue|null);

    /** BattleLiveInfoPO liveChannel */
    liveChannel?: (google.protobuf.IStringValue|null);

    /** BattleLiveInfoPO allowLive */
    allowLive?: (google.protobuf.IInt32Value|null);

    /** BattleLiveInfoPO allowVoice */
    allowVoice?: (google.protobuf.IInt32Value|null);

    /** BattleLiveInfoPO allowJoinLive */
    allowJoinLive?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleLiveInfoPO. */
export class BattleLiveInfoPO implements IBattleLiveInfoPO {

    /**
     * Constructs a new BattleLiveInfoPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleLiveInfoPO);

    /** BattleLiveInfoPO forceLive. */
    public forceLive?: (google.protobuf.IInt32Value|null);

    /** BattleLiveInfoPO liveToken. */
    public liveToken?: (google.protobuf.IStringValue|null);

    /** BattleLiveInfoPO liveChannel. */
    public liveChannel?: (google.protobuf.IStringValue|null);

    /** BattleLiveInfoPO allowLive. */
    public allowLive?: (google.protobuf.IInt32Value|null);

    /** BattleLiveInfoPO allowVoice. */
    public allowVoice?: (google.protobuf.IInt32Value|null);

    /** BattleLiveInfoPO allowJoinLive. */
    public allowJoinLive?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleLiveInfoPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleLiveInfoPO instance
     */
    public static create(properties?: IBattleLiveInfoPO): BattleLiveInfoPO;

    /**
     * Encodes the specified BattleLiveInfoPO message. Does not implicitly {@link BattleLiveInfoPO.verify|verify} messages.
     * @param message BattleLiveInfoPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleLiveInfoPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleLiveInfoPO message, length delimited. Does not implicitly {@link BattleLiveInfoPO.verify|verify} messages.
     * @param message BattleLiveInfoPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleLiveInfoPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleLiveInfoPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleLiveInfoPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleLiveInfoPO;

    /**
     * Decodes a BattleLiveInfoPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleLiveInfoPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleLiveInfoPO;

    /**
     * Verifies a BattleLiveInfoPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleLiveInfoPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleLiveInfoPO
     */
    public static fromObject(object: { [k: string]: any }): BattleLiveInfoPO;

    /**
     * Creates a plain object from a BattleLiveInfoPO message. Also converts values to other types if specified.
     * @param message BattleLiveInfoPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleLiveInfoPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleLiveInfoPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleLiveInfoPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleProlongPO. */
export interface IBattleProlongPO {

    /** BattleProlongPO btnType */
    btnType?: (google.protobuf.IInt32Value|null);

    /** BattleProlongPO needDiamond */
    needDiamond?: (google.protobuf.IInt32Value|null);

    /** BattleProlongPO canNextProlong */
    canNextProlong?: (google.protobuf.IInt32Value|null);

    /** BattleProlongPO freeTimes */
    freeTimes?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleProlongPO. */
export class BattleProlongPO implements IBattleProlongPO {

    /**
     * Constructs a new BattleProlongPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleProlongPO);

    /** BattleProlongPO btnType. */
    public btnType?: (google.protobuf.IInt32Value|null);

    /** BattleProlongPO needDiamond. */
    public needDiamond?: (google.protobuf.IInt32Value|null);

    /** BattleProlongPO canNextProlong. */
    public canNextProlong?: (google.protobuf.IInt32Value|null);

    /** BattleProlongPO freeTimes. */
    public freeTimes?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleProlongPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleProlongPO instance
     */
    public static create(properties?: IBattleProlongPO): BattleProlongPO;

    /**
     * Encodes the specified BattleProlongPO message. Does not implicitly {@link BattleProlongPO.verify|verify} messages.
     * @param message BattleProlongPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleProlongPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleProlongPO message, length delimited. Does not implicitly {@link BattleProlongPO.verify|verify} messages.
     * @param message BattleProlongPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleProlongPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleProlongPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleProlongPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleProlongPO;

    /**
     * Decodes a BattleProlongPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleProlongPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleProlongPO;

    /**
     * Verifies a BattleProlongPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleProlongPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleProlongPO
     */
    public static fromObject(object: { [k: string]: any }): BattleProlongPO;

    /**
     * Creates a plain object from a BattleProlongPO message. Also converts values to other types if specified.
     * @param message BattleProlongPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleProlongPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleProlongPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleProlongPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleForceSeeCardCfgPO. */
export interface IBattleForceSeeCardCfgPO {

    /** BattleForceSeeCardCfgPO isCanSee */
    isCanSee?: (google.protobuf.IInt32Value|null);

    /** BattleForceSeeCardCfgPO needDiamond */
    needDiamond?: (google.protobuf.IInt32Value|null);

    /** BattleForceSeeCardCfgPO seeNum */
    seeNum?: (google.protobuf.IInt32Value|null);

    /** BattleForceSeeCardCfgPO freeTimes */
    freeTimes?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleForceSeeCardCfgPO. */
export class BattleForceSeeCardCfgPO implements IBattleForceSeeCardCfgPO {

    /**
     * Constructs a new BattleForceSeeCardCfgPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleForceSeeCardCfgPO);

    /** BattleForceSeeCardCfgPO isCanSee. */
    public isCanSee?: (google.protobuf.IInt32Value|null);

    /** BattleForceSeeCardCfgPO needDiamond. */
    public needDiamond?: (google.protobuf.IInt32Value|null);

    /** BattleForceSeeCardCfgPO seeNum. */
    public seeNum?: (google.protobuf.IInt32Value|null);

    /** BattleForceSeeCardCfgPO freeTimes. */
    public freeTimes?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleForceSeeCardCfgPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleForceSeeCardCfgPO instance
     */
    public static create(properties?: IBattleForceSeeCardCfgPO): BattleForceSeeCardCfgPO;

    /**
     * Encodes the specified BattleForceSeeCardCfgPO message. Does not implicitly {@link BattleForceSeeCardCfgPO.verify|verify} messages.
     * @param message BattleForceSeeCardCfgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleForceSeeCardCfgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleForceSeeCardCfgPO message, length delimited. Does not implicitly {@link BattleForceSeeCardCfgPO.verify|verify} messages.
     * @param message BattleForceSeeCardCfgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleForceSeeCardCfgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleForceSeeCardCfgPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleForceSeeCardCfgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleForceSeeCardCfgPO;

    /**
     * Decodes a BattleForceSeeCardCfgPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleForceSeeCardCfgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleForceSeeCardCfgPO;

    /**
     * Verifies a BattleForceSeeCardCfgPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleForceSeeCardCfgPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleForceSeeCardCfgPO
     */
    public static fromObject(object: { [k: string]: any }): BattleForceSeeCardCfgPO;

    /**
     * Creates a plain object from a BattleForceSeeCardCfgPO message. Also converts values to other types if specified.
     * @param message BattleForceSeeCardCfgPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleForceSeeCardCfgPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleForceSeeCardCfgPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleForceSeeCardCfgPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleActionPO. */
export interface IBattleActionPO {

    /** BattleActionPO actionType */
    actionType?: (google.protobuf.IStringValue|null);

    /** BattleActionPO seatScore */
    seatScore?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO actionScore */
    actionScore?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO card */
    card?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleActionPO seatNum */
    seatNum?: (google.protobuf.IInt64Value|null);

    /** BattleActionPO currentScore */
    currentScore?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO isShowAllin */
    isShowAllin?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO sex */
    sex?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO thinkTime */
    thinkTime?: (google.protobuf.IInt64Value|null);
}

/** Represents a BattleActionPO. */
export class BattleActionPO implements IBattleActionPO {

    /**
     * Constructs a new BattleActionPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleActionPO);

    /** BattleActionPO actionType. */
    public actionType?: (google.protobuf.IStringValue|null);

    /** BattleActionPO seatScore. */
    public seatScore?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO actionScore. */
    public actionScore?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO card. */
    public card?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleActionPO seatNum. */
    public seatNum?: (google.protobuf.IInt64Value|null);

    /** BattleActionPO currentScore. */
    public currentScore?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO isShowAllin. */
    public isShowAllin?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO sex. */
    public sex?: (google.protobuf.IInt32Value|null);

    /** BattleActionPO thinkTime. */
    public thinkTime?: (google.protobuf.IInt64Value|null);

    /**
     * Creates a new BattleActionPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleActionPO instance
     */
    public static create(properties?: IBattleActionPO): BattleActionPO;

    /**
     * Encodes the specified BattleActionPO message. Does not implicitly {@link BattleActionPO.verify|verify} messages.
     * @param message BattleActionPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleActionPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleActionPO message, length delimited. Does not implicitly {@link BattleActionPO.verify|verify} messages.
     * @param message BattleActionPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleActionPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleActionPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleActionPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleActionPO;

    /**
     * Decodes a BattleActionPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleActionPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleActionPO;

    /**
     * Verifies a BattleActionPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleActionPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleActionPO
     */
    public static fromObject(object: { [k: string]: any }): BattleActionPO;

    /**
     * Creates a plain object from a BattleActionPO message. Also converts values to other types if specified.
     * @param message BattleActionPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleActionPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleActionPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleActionPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleDoubleThanResultMsg. */
export interface IBattleDoubleThanResultMsg {

    /** BattleDoubleThanResultMsg isDogfall */
    isDogfall?: (google.protobuf.IInt32Value|null);

    /** BattleDoubleThanResultMsg winUserId */
    winUserId?: (google.protobuf.IInt64Value|null);

    /** BattleDoubleThanResultMsg winNickname */
    winNickname?: (google.protobuf.IStringValue|null);

    /** BattleDoubleThanResultMsg oldPublicCards */
    oldPublicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleDoubleThanResultMsg drawNickname */
    drawNickname?: (google.protobuf.IStringValue|null);
}

/** Represents a BattleDoubleThanResultMsg. */
export class BattleDoubleThanResultMsg implements IBattleDoubleThanResultMsg {

    /**
     * Constructs a new BattleDoubleThanResultMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleDoubleThanResultMsg);

    /** BattleDoubleThanResultMsg isDogfall. */
    public isDogfall?: (google.protobuf.IInt32Value|null);

    /** BattleDoubleThanResultMsg winUserId. */
    public winUserId?: (google.protobuf.IInt64Value|null);

    /** BattleDoubleThanResultMsg winNickname. */
    public winNickname?: (google.protobuf.IStringValue|null);

    /** BattleDoubleThanResultMsg oldPublicCards. */
    public oldPublicCards: google.protobuf.IInt32Value[];

    /** BattleDoubleThanResultMsg drawNickname. */
    public drawNickname?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new BattleDoubleThanResultMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleDoubleThanResultMsg instance
     */
    public static create(properties?: IBattleDoubleThanResultMsg): BattleDoubleThanResultMsg;

    /**
     * Encodes the specified BattleDoubleThanResultMsg message. Does not implicitly {@link BattleDoubleThanResultMsg.verify|verify} messages.
     * @param message BattleDoubleThanResultMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleDoubleThanResultMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleDoubleThanResultMsg message, length delimited. Does not implicitly {@link BattleDoubleThanResultMsg.verify|verify} messages.
     * @param message BattleDoubleThanResultMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleDoubleThanResultMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleDoubleThanResultMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleDoubleThanResultMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleDoubleThanResultMsg;

    /**
     * Decodes a BattleDoubleThanResultMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleDoubleThanResultMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleDoubleThanResultMsg;

    /**
     * Verifies a BattleDoubleThanResultMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleDoubleThanResultMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleDoubleThanResultMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleDoubleThanResultMsg;

    /**
     * Creates a plain object from a BattleDoubleThanResultMsg message. Also converts values to other types if specified.
     * @param message BattleDoubleThanResultMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleDoubleThanResultMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleDoubleThanResultMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleDoubleThanResultMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleBankChangeMsg. */
export interface IBattleBankChangeMsg {

    /** BattleBankChangeMsg bBUserId */
    bBUserId?: (google.protobuf.IInt64Value|null);

    /** BattleBankChangeMsg sBUserId */
    sBUserId?: (google.protobuf.IInt64Value|null);

    /** BattleBankChangeMsg bankerUserId */
    bankerUserId?: (google.protobuf.IInt64Value|null);

    /** BattleBankChangeMsg bBSeatNum */
    bBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleBankChangeMsg sBSeatNum */
    sBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleBankChangeMsg bankerSeatNum */
    bankerSeatNum?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleBankChangeMsg. */
export class BattleBankChangeMsg implements IBattleBankChangeMsg {

    /**
     * Constructs a new BattleBankChangeMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleBankChangeMsg);

    /** BattleBankChangeMsg bBUserId. */
    public bBUserId?: (google.protobuf.IInt64Value|null);

    /** BattleBankChangeMsg sBUserId. */
    public sBUserId?: (google.protobuf.IInt64Value|null);

    /** BattleBankChangeMsg bankerUserId. */
    public bankerUserId?: (google.protobuf.IInt64Value|null);

    /** BattleBankChangeMsg bBSeatNum. */
    public bBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleBankChangeMsg sBSeatNum. */
    public sBSeatNum?: (google.protobuf.IInt32Value|null);

    /** BattleBankChangeMsg bankerSeatNum. */
    public bankerSeatNum?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleBankChangeMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleBankChangeMsg instance
     */
    public static create(properties?: IBattleBankChangeMsg): BattleBankChangeMsg;

    /**
     * Encodes the specified BattleBankChangeMsg message. Does not implicitly {@link BattleBankChangeMsg.verify|verify} messages.
     * @param message BattleBankChangeMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleBankChangeMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleBankChangeMsg message, length delimited. Does not implicitly {@link BattleBankChangeMsg.verify|verify} messages.
     * @param message BattleBankChangeMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleBankChangeMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleBankChangeMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleBankChangeMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleBankChangeMsg;

    /**
     * Decodes a BattleBankChangeMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleBankChangeMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleBankChangeMsg;

    /**
     * Verifies a BattleBankChangeMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleBankChangeMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleBankChangeMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleBankChangeMsg;

    /**
     * Creates a plain object from a BattleBankChangeMsg message. Also converts values to other types if specified.
     * @param message BattleBankChangeMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleBankChangeMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleBankChangeMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleBankChangeMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleActionMsg. */
export interface IBattleActionMsg {

    /** BattleActionMsg actionList */
    actionList?: (IBattleActionPO[]|null);

    /** BattleActionMsg totalPot */
    totalPot?: (google.protobuf.IInt32Value|null);

    /** BattleActionMsg selfInfo */
    selfInfo?: (IBattleRoomUserMsg|null);
}

/** Represents a BattleActionMsg. */
export class BattleActionMsg implements IBattleActionMsg {

    /**
     * Constructs a new BattleActionMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleActionMsg);

    /** BattleActionMsg actionList. */
    public actionList: IBattleActionPO[];

    /** BattleActionMsg totalPot. */
    public totalPot?: (google.protobuf.IInt32Value|null);

    /** BattleActionMsg selfInfo. */
    public selfInfo?: (IBattleRoomUserMsg|null);

    /**
     * Creates a new BattleActionMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleActionMsg instance
     */
    public static create(properties?: IBattleActionMsg): BattleActionMsg;

    /**
     * Encodes the specified BattleActionMsg message. Does not implicitly {@link BattleActionMsg.verify|verify} messages.
     * @param message BattleActionMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleActionMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleActionMsg message, length delimited. Does not implicitly {@link BattleActionMsg.verify|verify} messages.
     * @param message BattleActionMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleActionMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleActionMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleActionMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleActionMsg;

    /**
     * Decodes a BattleActionMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleActionMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleActionMsg;

    /**
     * Verifies a BattleActionMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleActionMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleActionMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleActionMsg;

    /**
     * Creates a plain object from a BattleActionMsg message. Also converts values to other types if specified.
     * @param message BattleActionMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleActionMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleActionMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleActionMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleUserOptMsg. */
export interface IBattleUserOptMsg {

    /** BattleUserOptMsg userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleUserOptMsg minRaiseScore */
    minRaiseScore?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg maxRaiseScore */
    maxRaiseScore?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg callScore */
    callScore?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg canActionList */
    canActionList?: (google.protobuf.IStringValue[]|null);

    /** BattleUserOptMsg countDown */
    countDown?: (google.protobuf.IInt64Value|null);

    /** BattleUserOptMsg totalCountDown */
    totalCountDown?: (google.protobuf.IInt64Value|null);

    /** BattleUserOptMsg lastBet */
    lastBet?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg seatScore */
    seatScore?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg prolongCfg */
    prolongCfg?: (IBattleProlongPO|null);

    /** BattleUserOptMsg handCards */
    handCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleUserOptMsg cardType */
    cardType?: (google.protobuf.IStringValue|null);

    /** BattleUserOptMsg isCanRaise */
    isCanRaise?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg openRetainSeat */
    openRetainSeat?: (google.protobuf.IBoolValue|null);
}

/** Represents a BattleUserOptMsg. */
export class BattleUserOptMsg implements IBattleUserOptMsg {

    /**
     * Constructs a new BattleUserOptMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleUserOptMsg);

    /** BattleUserOptMsg userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleUserOptMsg minRaiseScore. */
    public minRaiseScore?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg maxRaiseScore. */
    public maxRaiseScore?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg callScore. */
    public callScore?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg canActionList. */
    public canActionList: google.protobuf.IStringValue[];

    /** BattleUserOptMsg countDown. */
    public countDown?: (google.protobuf.IInt64Value|null);

    /** BattleUserOptMsg totalCountDown. */
    public totalCountDown?: (google.protobuf.IInt64Value|null);

    /** BattleUserOptMsg lastBet. */
    public lastBet?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg seatScore. */
    public seatScore?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg prolongCfg. */
    public prolongCfg?: (IBattleProlongPO|null);

    /** BattleUserOptMsg handCards. */
    public handCards: google.protobuf.IInt32Value[];

    /** BattleUserOptMsg cardType. */
    public cardType?: (google.protobuf.IStringValue|null);

    /** BattleUserOptMsg isCanRaise. */
    public isCanRaise?: (google.protobuf.IInt32Value|null);

    /** BattleUserOptMsg openRetainSeat. */
    public openRetainSeat?: (google.protobuf.IBoolValue|null);

    /**
     * Creates a new BattleUserOptMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleUserOptMsg instance
     */
    public static create(properties?: IBattleUserOptMsg): BattleUserOptMsg;

    /**
     * Encodes the specified BattleUserOptMsg message. Does not implicitly {@link BattleUserOptMsg.verify|verify} messages.
     * @param message BattleUserOptMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleUserOptMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleUserOptMsg message, length delimited. Does not implicitly {@link BattleUserOptMsg.verify|verify} messages.
     * @param message BattleUserOptMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleUserOptMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleUserOptMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleUserOptMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleUserOptMsg;

    /**
     * Decodes a BattleUserOptMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleUserOptMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleUserOptMsg;

    /**
     * Verifies a BattleUserOptMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleUserOptMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleUserOptMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleUserOptMsg;

    /**
     * Creates a plain object from a BattleUserOptMsg message. Also converts values to other types if specified.
     * @param message BattleUserOptMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleUserOptMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleUserOptMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleUserOptMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleRoundChangeMsg. */
export interface IBattleRoundChangeMsg {

    /** BattleRoundChangeMsg totalPot */
    totalPot?: (google.protobuf.IInt32Value|null);

    /** BattleRoundChangeMsg potList */
    potList?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoundChangeMsg dealPublicCards */
    dealPublicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleRoundChangeMsg cardType */
    cardType?: (google.protobuf.IStringValue|null);

    /** BattleRoundChangeMsg userCardsList */
    userCardsList?: (IBattleUserCardInfoMsg[]|null);

    /** BattleRoundChangeMsg isDoubleRound */
    isDoubleRound?: (google.protobuf.IInt32Value|null);

    /** BattleRoundChangeMsg userAciton */
    userAciton?: (IBattleUserOptMsg|null);

    /** BattleRoundChangeMsg round */
    round?: (google.protobuf.IStringValue|null);
}

/** Represents a BattleRoundChangeMsg. */
export class BattleRoundChangeMsg implements IBattleRoundChangeMsg {

    /**
     * Constructs a new BattleRoundChangeMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleRoundChangeMsg);

    /** BattleRoundChangeMsg totalPot. */
    public totalPot?: (google.protobuf.IInt32Value|null);

    /** BattleRoundChangeMsg potList. */
    public potList: google.protobuf.IInt32Value[];

    /** BattleRoundChangeMsg dealPublicCards. */
    public dealPublicCards: google.protobuf.IInt32Value[];

    /** BattleRoundChangeMsg cardType. */
    public cardType?: (google.protobuf.IStringValue|null);

    /** BattleRoundChangeMsg userCardsList. */
    public userCardsList: IBattleUserCardInfoMsg[];

    /** BattleRoundChangeMsg isDoubleRound. */
    public isDoubleRound?: (google.protobuf.IInt32Value|null);

    /** BattleRoundChangeMsg userAciton. */
    public userAciton?: (IBattleUserOptMsg|null);

    /** BattleRoundChangeMsg round. */
    public round?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new BattleRoundChangeMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleRoundChangeMsg instance
     */
    public static create(properties?: IBattleRoundChangeMsg): BattleRoundChangeMsg;

    /**
     * Encodes the specified BattleRoundChangeMsg message. Does not implicitly {@link BattleRoundChangeMsg.verify|verify} messages.
     * @param message BattleRoundChangeMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleRoundChangeMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleRoundChangeMsg message, length delimited. Does not implicitly {@link BattleRoundChangeMsg.verify|verify} messages.
     * @param message BattleRoundChangeMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleRoundChangeMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleRoundChangeMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleRoundChangeMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleRoundChangeMsg;

    /**
     * Decodes a BattleRoundChangeMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleRoundChangeMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleRoundChangeMsg;

    /**
     * Verifies a BattleRoundChangeMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleRoundChangeMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleRoundChangeMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleRoundChangeMsg;

    /**
     * Creates a plain object from a BattleRoundChangeMsg message. Also converts values to other types if specified.
     * @param message BattleRoundChangeMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleRoundChangeMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleRoundChangeMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleRoundChangeMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleAddScoreStatusResp. */
export interface IBattleAddScoreStatusResp {

    /** BattleAddScoreStatusResp status */
    status?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleAddScoreStatusResp. */
export class BattleAddScoreStatusResp implements IBattleAddScoreStatusResp {

    /**
     * Constructs a new BattleAddScoreStatusResp.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleAddScoreStatusResp);

    /** BattleAddScoreStatusResp status. */
    public status?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleAddScoreStatusResp instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleAddScoreStatusResp instance
     */
    public static create(properties?: IBattleAddScoreStatusResp): BattleAddScoreStatusResp;

    /**
     * Encodes the specified BattleAddScoreStatusResp message. Does not implicitly {@link BattleAddScoreStatusResp.verify|verify} messages.
     * @param message BattleAddScoreStatusResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleAddScoreStatusResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleAddScoreStatusResp message, length delimited. Does not implicitly {@link BattleAddScoreStatusResp.verify|verify} messages.
     * @param message BattleAddScoreStatusResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleAddScoreStatusResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleAddScoreStatusResp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleAddScoreStatusResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleAddScoreStatusResp;

    /**
     * Decodes a BattleAddScoreStatusResp message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleAddScoreStatusResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleAddScoreStatusResp;

    /**
     * Verifies a BattleAddScoreStatusResp message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleAddScoreStatusResp message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleAddScoreStatusResp
     */
    public static fromObject(object: { [k: string]: any }): BattleAddScoreStatusResp;

    /**
     * Creates a plain object from a BattleAddScoreStatusResp message. Also converts values to other types if specified.
     * @param message BattleAddScoreStatusResp
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleAddScoreStatusResp, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleAddScoreStatusResp to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleAddScoreStatusResp
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleThanResp. */
export interface IBattleThanResp {

    /** BattleThanResp isAllUserAllin */
    isAllUserAllin?: (google.protobuf.IInt32Value|null);

    /** BattleThanResp isDealDouble */
    isDealDouble?: (google.protobuf.IInt32Value|null);

    /** BattleThanResp thanList */
    thanList?: (IBattleThanPO[]|null);

    /** BattleThanResp roomDetail */
    roomDetail?: (IBattleRoomDetailResp|null);

    /** BattleThanResp highPublicCards */
    highPublicCards?: (google.protobuf.IInt32Value[]|null);
}

/** Represents a BattleThanResp. */
export class BattleThanResp implements IBattleThanResp {

    /**
     * Constructs a new BattleThanResp.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleThanResp);

    /** BattleThanResp isAllUserAllin. */
    public isAllUserAllin?: (google.protobuf.IInt32Value|null);

    /** BattleThanResp isDealDouble. */
    public isDealDouble?: (google.protobuf.IInt32Value|null);

    /** BattleThanResp thanList. */
    public thanList: IBattleThanPO[];

    /** BattleThanResp roomDetail. */
    public roomDetail?: (IBattleRoomDetailResp|null);

    /** BattleThanResp highPublicCards. */
    public highPublicCards: google.protobuf.IInt32Value[];

    /**
     * Creates a new BattleThanResp instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleThanResp instance
     */
    public static create(properties?: IBattleThanResp): BattleThanResp;

    /**
     * Encodes the specified BattleThanResp message. Does not implicitly {@link BattleThanResp.verify|verify} messages.
     * @param message BattleThanResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleThanResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleThanResp message, length delimited. Does not implicitly {@link BattleThanResp.verify|verify} messages.
     * @param message BattleThanResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleThanResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleThanResp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleThanResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleThanResp;

    /**
     * Decodes a BattleThanResp message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleThanResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleThanResp;

    /**
     * Verifies a BattleThanResp message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleThanResp message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleThanResp
     */
    public static fromObject(object: { [k: string]: any }): BattleThanResp;

    /**
     * Creates a plain object from a BattleThanResp message. Also converts values to other types if specified.
     * @param message BattleThanResp
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleThanResp, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleThanResp to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleThanResp
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleProlongMsg. */
export interface IBattleProlongMsg {

    /** BattleProlongMsg userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleProlongMsg countDown */
    countDown?: (google.protobuf.IInt64Value|null);

    /** BattleProlongMsg balanceDiamond */
    balanceDiamond?: (google.protobuf.IInt64Value|null);

    /** BattleProlongMsg btnType */
    btnType?: (google.protobuf.IInt32Value|null);

    /** BattleProlongMsg needDiamond */
    needDiamond?: (google.protobuf.IInt32Value|null);

    /** BattleProlongMsg canNextProlong */
    canNextProlong?: (google.protobuf.IInt32Value|null);

    /** BattleProlongMsg freeTimes */
    freeTimes?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleProlongMsg. */
export class BattleProlongMsg implements IBattleProlongMsg {

    /**
     * Constructs a new BattleProlongMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleProlongMsg);

    /** BattleProlongMsg userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleProlongMsg countDown. */
    public countDown?: (google.protobuf.IInt64Value|null);

    /** BattleProlongMsg balanceDiamond. */
    public balanceDiamond?: (google.protobuf.IInt64Value|null);

    /** BattleProlongMsg btnType. */
    public btnType?: (google.protobuf.IInt32Value|null);

    /** BattleProlongMsg needDiamond. */
    public needDiamond?: (google.protobuf.IInt32Value|null);

    /** BattleProlongMsg canNextProlong. */
    public canNextProlong?: (google.protobuf.IInt32Value|null);

    /** BattleProlongMsg freeTimes. */
    public freeTimes?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleProlongMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleProlongMsg instance
     */
    public static create(properties?: IBattleProlongMsg): BattleProlongMsg;

    /**
     * Encodes the specified BattleProlongMsg message. Does not implicitly {@link BattleProlongMsg.verify|verify} messages.
     * @param message BattleProlongMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleProlongMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleProlongMsg message, length delimited. Does not implicitly {@link BattleProlongMsg.verify|verify} messages.
     * @param message BattleProlongMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleProlongMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleProlongMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleProlongMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleProlongMsg;

    /**
     * Decodes a BattleProlongMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleProlongMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleProlongMsg;

    /**
     * Verifies a BattleProlongMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleProlongMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleProlongMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleProlongMsg;

    /**
     * Creates a plain object from a BattleProlongMsg message. Also converts values to other types if specified.
     * @param message BattleProlongMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleProlongMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleProlongMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleProlongMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a ChatMsgPO. */
export interface IChatMsgPO {

    /** ChatMsgPO userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** ChatMsgPO type */
    type?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO content */
    content?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO avatar */
    avatar?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO nikename */
    nikename?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO second */
    second?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO receiverId */
    receiverId?: (google.protobuf.IInt64Value|null);

    /** ChatMsgPO num */
    num?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO emojiId */
    emojiId?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO aniType */
    aniType?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO createTime */
    createTime?: (google.protobuf.IInt64Value|null);

    /** ChatMsgPO nickname */
    nickname?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO availableFreeEmojiNum */
    availableFreeEmojiNum?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO id */
    id?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO regFreeEmoji */
    regFreeEmoji?: (google.protobuf.IInt32Value|null);
}

/** Represents a ChatMsgPO. */
export class ChatMsgPO implements IChatMsgPO {

    /**
     * Constructs a new ChatMsgPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IChatMsgPO);

    /** ChatMsgPO userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** ChatMsgPO type. */
    public type?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO content. */
    public content?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO avatar. */
    public avatar?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO nikename. */
    public nikename?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO second. */
    public second?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO receiverId. */
    public receiverId?: (google.protobuf.IInt64Value|null);

    /** ChatMsgPO num. */
    public num?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO emojiId. */
    public emojiId?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO aniType. */
    public aniType?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO createTime. */
    public createTime?: (google.protobuf.IInt64Value|null);

    /** ChatMsgPO nickname. */
    public nickname?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO availableFreeEmojiNum. */
    public availableFreeEmojiNum?: (google.protobuf.IInt32Value|null);

    /** ChatMsgPO id. */
    public id?: (google.protobuf.IStringValue|null);

    /** ChatMsgPO regFreeEmoji. */
    public regFreeEmoji?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new ChatMsgPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ChatMsgPO instance
     */
    public static create(properties?: IChatMsgPO): ChatMsgPO;

    /**
     * Encodes the specified ChatMsgPO message. Does not implicitly {@link ChatMsgPO.verify|verify} messages.
     * @param message ChatMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IChatMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ChatMsgPO message, length delimited. Does not implicitly {@link ChatMsgPO.verify|verify} messages.
     * @param message ChatMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IChatMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ChatMsgPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ChatMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ChatMsgPO;

    /**
     * Decodes a ChatMsgPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ChatMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ChatMsgPO;

    /**
     * Verifies a ChatMsgPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ChatMsgPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ChatMsgPO
     */
    public static fromObject(object: { [k: string]: any }): ChatMsgPO;

    /**
     * Creates a plain object from a ChatMsgPO message. Also converts values to other types if specified.
     * @param message ChatMsgPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ChatMsgPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ChatMsgPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ChatMsgPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleSendDoubleOptMsg. */
export interface IBattleSendDoubleOptMsg {

    /** BattleSendDoubleOptMsg validPotScore */
    validPotScore?: (google.protobuf.IInt32Value|null);

    /** BattleSendDoubleOptMsg publicCards */
    publicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleSendDoubleOptMsg battleUserList */
    battleUserList?: (IBattleSendDoubleUserMsg[]|null);

    /** BattleSendDoubleOptMsg countDown */
    countDown?: (google.protobuf.IInt64Value|null);
}

/** Represents a BattleSendDoubleOptMsg. */
export class BattleSendDoubleOptMsg implements IBattleSendDoubleOptMsg {

    /**
     * Constructs a new BattleSendDoubleOptMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleSendDoubleOptMsg);

    /** BattleSendDoubleOptMsg validPotScore. */
    public validPotScore?: (google.protobuf.IInt32Value|null);

    /** BattleSendDoubleOptMsg publicCards. */
    public publicCards: google.protobuf.IInt32Value[];

    /** BattleSendDoubleOptMsg battleUserList. */
    public battleUserList: IBattleSendDoubleUserMsg[];

    /** BattleSendDoubleOptMsg countDown. */
    public countDown?: (google.protobuf.IInt64Value|null);

    /**
     * Creates a new BattleSendDoubleOptMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleSendDoubleOptMsg instance
     */
    public static create(properties?: IBattleSendDoubleOptMsg): BattleSendDoubleOptMsg;

    /**
     * Encodes the specified BattleSendDoubleOptMsg message. Does not implicitly {@link BattleSendDoubleOptMsg.verify|verify} messages.
     * @param message BattleSendDoubleOptMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleSendDoubleOptMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleSendDoubleOptMsg message, length delimited. Does not implicitly {@link BattleSendDoubleOptMsg.verify|verify} messages.
     * @param message BattleSendDoubleOptMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleSendDoubleOptMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleSendDoubleOptMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleSendDoubleOptMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleSendDoubleOptMsg;

    /**
     * Decodes a BattleSendDoubleOptMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleSendDoubleOptMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleSendDoubleOptMsg;

    /**
     * Verifies a BattleSendDoubleOptMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleSendDoubleOptMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleSendDoubleOptMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleSendDoubleOptMsg;

    /**
     * Creates a plain object from a BattleSendDoubleOptMsg message. Also converts values to other types if specified.
     * @param message BattleSendDoubleOptMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleSendDoubleOptMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleSendDoubleOptMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleSendDoubleOptMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleCSDResultMsg. */
export interface IBattleCSDResultMsg {

    /** BattleCSDResultMsg userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleCSDResultMsg nickname */
    nickname?: (google.protobuf.IStringValue|null);

    /** BattleCSDResultMsg avatar */
    avatar?: (google.protobuf.IStringValue|null);

    /** BattleCSDResultMsg chooseStatus */
    chooseStatus?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleCSDResultMsg. */
export class BattleCSDResultMsg implements IBattleCSDResultMsg {

    /**
     * Constructs a new BattleCSDResultMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleCSDResultMsg);

    /** BattleCSDResultMsg userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleCSDResultMsg nickname. */
    public nickname?: (google.protobuf.IStringValue|null);

    /** BattleCSDResultMsg avatar. */
    public avatar?: (google.protobuf.IStringValue|null);

    /** BattleCSDResultMsg chooseStatus. */
    public chooseStatus?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleCSDResultMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleCSDResultMsg instance
     */
    public static create(properties?: IBattleCSDResultMsg): BattleCSDResultMsg;

    /**
     * Encodes the specified BattleCSDResultMsg message. Does not implicitly {@link BattleCSDResultMsg.verify|verify} messages.
     * @param message BattleCSDResultMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleCSDResultMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleCSDResultMsg message, length delimited. Does not implicitly {@link BattleCSDResultMsg.verify|verify} messages.
     * @param message BattleCSDResultMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleCSDResultMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleCSDResultMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleCSDResultMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleCSDResultMsg;

    /**
     * Decodes a BattleCSDResultMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleCSDResultMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleCSDResultMsg;

    /**
     * Verifies a BattleCSDResultMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleCSDResultMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleCSDResultMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleCSDResultMsg;

    /**
     * Creates a plain object from a BattleCSDResultMsg message. Also converts values to other types if specified.
     * @param message BattleCSDResultMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleCSDResultMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleCSDResultMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleCSDResultMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an AllinOpenCardMsg. */
export interface IAllinOpenCardMsg {

    /** AllinOpenCardMsg userCardInfoList */
    userCardInfoList?: (IBattleUserCardInfoMsg[]|null);

    /** AllinOpenCardMsg totalPot */
    totalPot?: (google.protobuf.IInt32Value|null);

    /** AllinOpenCardMsg potList */
    potList?: (google.protobuf.IInt32Value[]|null);
}

/** Represents an AllinOpenCardMsg. */
export class AllinOpenCardMsg implements IAllinOpenCardMsg {

    /**
     * Constructs a new AllinOpenCardMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IAllinOpenCardMsg);

    /** AllinOpenCardMsg userCardInfoList. */
    public userCardInfoList: IBattleUserCardInfoMsg[];

    /** AllinOpenCardMsg totalPot. */
    public totalPot?: (google.protobuf.IInt32Value|null);

    /** AllinOpenCardMsg potList. */
    public potList: google.protobuf.IInt32Value[];

    /**
     * Creates a new AllinOpenCardMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns AllinOpenCardMsg instance
     */
    public static create(properties?: IAllinOpenCardMsg): AllinOpenCardMsg;

    /**
     * Encodes the specified AllinOpenCardMsg message. Does not implicitly {@link AllinOpenCardMsg.verify|verify} messages.
     * @param message AllinOpenCardMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IAllinOpenCardMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified AllinOpenCardMsg message, length delimited. Does not implicitly {@link AllinOpenCardMsg.verify|verify} messages.
     * @param message AllinOpenCardMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IAllinOpenCardMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an AllinOpenCardMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns AllinOpenCardMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): AllinOpenCardMsg;

    /**
     * Decodes an AllinOpenCardMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns AllinOpenCardMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): AllinOpenCardMsg;

    /**
     * Verifies an AllinOpenCardMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an AllinOpenCardMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns AllinOpenCardMsg
     */
    public static fromObject(object: { [k: string]: any }): AllinOpenCardMsg;

    /**
     * Creates a plain object from an AllinOpenCardMsg message. Also converts values to other types if specified.
     * @param message AllinOpenCardMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: AllinOpenCardMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this AllinOpenCardMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for AllinOpenCardMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleOpenCardMsg. */
export interface IBattleOpenCardMsg {

    /** BattleOpenCardMsg userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleOpenCardMsg actionCard */
    actionCard?: (google.protobuf.IInt32Value|null);

    /** BattleOpenCardMsg publicCards */
    publicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleOpenCardMsg optType */
    optType?: (google.protobuf.IInt32Value|null);

    /** BattleOpenCardMsg cardType */
    cardType?: (google.protobuf.IStringValue|null);
}

/** Represents a BattleOpenCardMsg. */
export class BattleOpenCardMsg implements IBattleOpenCardMsg {

    /**
     * Constructs a new BattleOpenCardMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleOpenCardMsg);

    /** BattleOpenCardMsg userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleOpenCardMsg actionCard. */
    public actionCard?: (google.protobuf.IInt32Value|null);

    /** BattleOpenCardMsg publicCards. */
    public publicCards: google.protobuf.IInt32Value[];

    /** BattleOpenCardMsg optType. */
    public optType?: (google.protobuf.IInt32Value|null);

    /** BattleOpenCardMsg cardType. */
    public cardType?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new BattleOpenCardMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleOpenCardMsg instance
     */
    public static create(properties?: IBattleOpenCardMsg): BattleOpenCardMsg;

    /**
     * Encodes the specified BattleOpenCardMsg message. Does not implicitly {@link BattleOpenCardMsg.verify|verify} messages.
     * @param message BattleOpenCardMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleOpenCardMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleOpenCardMsg message, length delimited. Does not implicitly {@link BattleOpenCardMsg.verify|verify} messages.
     * @param message BattleOpenCardMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleOpenCardMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleOpenCardMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleOpenCardMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleOpenCardMsg;

    /**
     * Decodes a BattleOpenCardMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleOpenCardMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleOpenCardMsg;

    /**
     * Verifies a BattleOpenCardMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleOpenCardMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleOpenCardMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleOpenCardMsg;

    /**
     * Creates a plain object from a BattleOpenCardMsg message. Also converts values to other types if specified.
     * @param message BattleOpenCardMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleOpenCardMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleOpenCardMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleOpenCardMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a MessageDoorPO. */
export interface IMessageDoorPO {

    /** MessageDoorPO doorId */
    doorId?: (google.protobuf.IInt64Value|null);

    /** MessageDoorPO title */
    title?: (google.protobuf.IStringValue|null);

    /** MessageDoorPO icon */
    icon?: (google.protobuf.IStringValue|null);

    /** MessageDoorPO content */
    content?: (google.protobuf.IStringValue|null);

    /** MessageDoorPO contentParam */
    contentParam?: (google.protobuf.IStringValue|null);

    /** MessageDoorPO updateTime */
    updateTime?: (google.protobuf.IInt64Value|null);

    /** MessageDoorPO unReadCount */
    unReadCount?: (google.protobuf.IInt32Value|null);

    /** MessageDoorPO doorType */
    doorType?: (google.protobuf.IInt32Value|null);

    /** MessageDoorPO unDealCount */
    unDealCount?: (google.protobuf.IInt32Value|null);

    /** MessageDoorPO isShowTip */
    isShowTip?: (google.protobuf.IBoolValue|null);
}

/** Represents a MessageDoorPO. */
export class MessageDoorPO implements IMessageDoorPO {

    /**
     * Constructs a new MessageDoorPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IMessageDoorPO);

    /** MessageDoorPO doorId. */
    public doorId?: (google.protobuf.IInt64Value|null);

    /** MessageDoorPO title. */
    public title?: (google.protobuf.IStringValue|null);

    /** MessageDoorPO icon. */
    public icon?: (google.protobuf.IStringValue|null);

    /** MessageDoorPO content. */
    public content?: (google.protobuf.IStringValue|null);

    /** MessageDoorPO contentParam. */
    public contentParam?: (google.protobuf.IStringValue|null);

    /** MessageDoorPO updateTime. */
    public updateTime?: (google.protobuf.IInt64Value|null);

    /** MessageDoorPO unReadCount. */
    public unReadCount?: (google.protobuf.IInt32Value|null);

    /** MessageDoorPO doorType. */
    public doorType?: (google.protobuf.IInt32Value|null);

    /** MessageDoorPO unDealCount. */
    public unDealCount?: (google.protobuf.IInt32Value|null);

    /** MessageDoorPO isShowTip. */
    public isShowTip?: (google.protobuf.IBoolValue|null);

    /**
     * Creates a new MessageDoorPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns MessageDoorPO instance
     */
    public static create(properties?: IMessageDoorPO): MessageDoorPO;

    /**
     * Encodes the specified MessageDoorPO message. Does not implicitly {@link MessageDoorPO.verify|verify} messages.
     * @param message MessageDoorPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IMessageDoorPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified MessageDoorPO message, length delimited. Does not implicitly {@link MessageDoorPO.verify|verify} messages.
     * @param message MessageDoorPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IMessageDoorPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a MessageDoorPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MessageDoorPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): MessageDoorPO;

    /**
     * Decodes a MessageDoorPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns MessageDoorPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): MessageDoorPO;

    /**
     * Verifies a MessageDoorPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a MessageDoorPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns MessageDoorPO
     */
    public static fromObject(object: { [k: string]: any }): MessageDoorPO;

    /**
     * Creates a plain object from a MessageDoorPO message. Also converts values to other types if specified.
     * @param message MessageDoorPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: MessageDoorPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this MessageDoorPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for MessageDoorPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a RoomPlayRecordOneHand. */
export interface IRoomPlayRecordOneHand {

    /** RoomPlayRecordOneHand handNum */
    handNum?: (google.protobuf.IInt32Value|null);

    /** RoomPlayRecordOneHand frequency */
    frequency?: (google.protobuf.IInt32Value|null);

    /** RoomPlayRecordOneHand handList1 */
    handList1?: (IBattlePlayLog[]|null);

    /** RoomPlayRecordOneHand handList2 */
    handList2?: (IBattlePlayLog[]|null);

    /** RoomPlayRecordOneHand insuranceGain */
    insuranceGain?: (google.protobuf.IInt32Value|null);
}

/** Represents a RoomPlayRecordOneHand. */
export class RoomPlayRecordOneHand implements IRoomPlayRecordOneHand {

    /**
     * Constructs a new RoomPlayRecordOneHand.
     * @param [properties] Properties to set
     */
    constructor(properties?: IRoomPlayRecordOneHand);

    /** RoomPlayRecordOneHand handNum. */
    public handNum?: (google.protobuf.IInt32Value|null);

    /** RoomPlayRecordOneHand frequency. */
    public frequency?: (google.protobuf.IInt32Value|null);

    /** RoomPlayRecordOneHand handList1. */
    public handList1: IBattlePlayLog[];

    /** RoomPlayRecordOneHand handList2. */
    public handList2: IBattlePlayLog[];

    /** RoomPlayRecordOneHand insuranceGain. */
    public insuranceGain?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new RoomPlayRecordOneHand instance using the specified properties.
     * @param [properties] Properties to set
     * @returns RoomPlayRecordOneHand instance
     */
    public static create(properties?: IRoomPlayRecordOneHand): RoomPlayRecordOneHand;

    /**
     * Encodes the specified RoomPlayRecordOneHand message. Does not implicitly {@link RoomPlayRecordOneHand.verify|verify} messages.
     * @param message RoomPlayRecordOneHand message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IRoomPlayRecordOneHand, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified RoomPlayRecordOneHand message, length delimited. Does not implicitly {@link RoomPlayRecordOneHand.verify|verify} messages.
     * @param message RoomPlayRecordOneHand message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IRoomPlayRecordOneHand, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a RoomPlayRecordOneHand message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RoomPlayRecordOneHand
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): RoomPlayRecordOneHand;

    /**
     * Decodes a RoomPlayRecordOneHand message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns RoomPlayRecordOneHand
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): RoomPlayRecordOneHand;

    /**
     * Verifies a RoomPlayRecordOneHand message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a RoomPlayRecordOneHand message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns RoomPlayRecordOneHand
     */
    public static fromObject(object: { [k: string]: any }): RoomPlayRecordOneHand;

    /**
     * Creates a plain object from a RoomPlayRecordOneHand message. Also converts values to other types if specified.
     * @param message RoomPlayRecordOneHand
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: RoomPlayRecordOneHand, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this RoomPlayRecordOneHand to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for RoomPlayRecordOneHand
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a LookComCardsMsg. */
export interface ILookComCardsMsg {

    /** LookComCardsMsg userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** LookComCardsMsg handNum */
    handNum?: (google.protobuf.IInt32Value|null);

    /** LookComCardsMsg needDiamond */
    needDiamond?: (google.protobuf.IInt32Value|null);

    /** LookComCardsMsg seeComCards */
    seeComCards?: (google.protobuf.IInt32Value[]|null);

    /** LookComCardsMsg nickname */
    nickname?: (google.protobuf.IStringValue|null);

    /** LookComCardsMsg isShow */
    isShow?: (google.protobuf.IInt32Value|null);

    /** LookComCardsMsg freeSeeComs */
    freeSeeComs?: (google.protobuf.IInt32Value|null);
}

/** Represents a LookComCardsMsg. */
export class LookComCardsMsg implements ILookComCardsMsg {

    /**
     * Constructs a new LookComCardsMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: ILookComCardsMsg);

    /** LookComCardsMsg userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** LookComCardsMsg handNum. */
    public handNum?: (google.protobuf.IInt32Value|null);

    /** LookComCardsMsg needDiamond. */
    public needDiamond?: (google.protobuf.IInt32Value|null);

    /** LookComCardsMsg seeComCards. */
    public seeComCards: google.protobuf.IInt32Value[];

    /** LookComCardsMsg nickname. */
    public nickname?: (google.protobuf.IStringValue|null);

    /** LookComCardsMsg isShow. */
    public isShow?: (google.protobuf.IInt32Value|null);

    /** LookComCardsMsg freeSeeComs. */
    public freeSeeComs?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new LookComCardsMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns LookComCardsMsg instance
     */
    public static create(properties?: ILookComCardsMsg): LookComCardsMsg;

    /**
     * Encodes the specified LookComCardsMsg message. Does not implicitly {@link LookComCardsMsg.verify|verify} messages.
     * @param message LookComCardsMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ILookComCardsMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified LookComCardsMsg message, length delimited. Does not implicitly {@link LookComCardsMsg.verify|verify} messages.
     * @param message LookComCardsMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ILookComCardsMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a LookComCardsMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns LookComCardsMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LookComCardsMsg;

    /**
     * Decodes a LookComCardsMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns LookComCardsMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LookComCardsMsg;

    /**
     * Verifies a LookComCardsMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a LookComCardsMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns LookComCardsMsg
     */
    public static fromObject(object: { [k: string]: any }): LookComCardsMsg;

    /**
     * Creates a plain object from a LookComCardsMsg message. Also converts values to other types if specified.
     * @param message LookComCardsMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: LookComCardsMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this LookComCardsMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for LookComCardsMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a WaitHandResp. */
export interface IWaitHandResp {

    /** WaitHandResp waitHandNum */
    waitHandNum?: (google.protobuf.IInt32Value|null);
}

/** Represents a WaitHandResp. */
export class WaitHandResp implements IWaitHandResp {

    /**
     * Constructs a new WaitHandResp.
     * @param [properties] Properties to set
     */
    constructor(properties?: IWaitHandResp);

    /** WaitHandResp waitHandNum. */
    public waitHandNum?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new WaitHandResp instance using the specified properties.
     * @param [properties] Properties to set
     * @returns WaitHandResp instance
     */
    public static create(properties?: IWaitHandResp): WaitHandResp;

    /**
     * Encodes the specified WaitHandResp message. Does not implicitly {@link WaitHandResp.verify|verify} messages.
     * @param message WaitHandResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IWaitHandResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified WaitHandResp message, length delimited. Does not implicitly {@link WaitHandResp.verify|verify} messages.
     * @param message WaitHandResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IWaitHandResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a WaitHandResp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns WaitHandResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): WaitHandResp;

    /**
     * Decodes a WaitHandResp message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns WaitHandResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): WaitHandResp;

    /**
     * Verifies a WaitHandResp message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a WaitHandResp message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns WaitHandResp
     */
    public static fromObject(object: { [k: string]: any }): WaitHandResp;

    /**
     * Creates a plain object from a WaitHandResp message. Also converts values to other types if specified.
     * @param message WaitHandResp
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: WaitHandResp, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this WaitHandResp to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for WaitHandResp
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattlePlayLog. */
export interface IBattlePlayLog {

    /** BattlePlayLog recordId */
    recordId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog roomId */
    roomId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog publicCard */
    publicCard?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog handCard */
    handCard?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog changeScore */
    changeScore?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog createTime */
    createTime?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog lookbackId */
    lookbackId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog winUserId */
    winUserId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog winAvatar */
    winAvatar?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog winerChangeScore */
    winerChangeScore?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog ante */
    ante?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog grade */
    grade?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog pooling */
    pooling?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog showdown */
    showdown?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog folpRaise */
    folpRaise?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog reraise */
    reraise?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog continuationBet */
    continuationBet?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog win */
    win?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog handNum */
    handNum?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog seatNum */
    seatNum?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog cardType */
    cardType?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog betList */
    betList?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog keyCard */
    keyCard?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog seatPos */
    seatPos?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog dealIndex */
    dealIndex?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog avatar */
    avatar?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog showcardType */
    showcardType?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog publicCardAll */
    publicCardAll?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog playType */
    playType?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog omahaShow */
    omahaShow?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog seeComCards */
    seeComCards?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog seeAuCards */
    seeAuCards?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog isBigPot */
    isBigPot?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog squidWin */
    squidWin?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog nickname */
    nickname?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog insuranceWin */
    insuranceWin?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog insuranceInvest */
    insuranceInvest?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog insuranceRiverInvest */
    insuranceRiverInvest?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattlePlayLog. */
export class BattlePlayLog implements IBattlePlayLog {

    /**
     * Constructs a new BattlePlayLog.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattlePlayLog);

    /** BattlePlayLog recordId. */
    public recordId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog roomId. */
    public roomId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog publicCard. */
    public publicCard?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog handCard. */
    public handCard?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog changeScore. */
    public changeScore?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog createTime. */
    public createTime?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog lookbackId. */
    public lookbackId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog winUserId. */
    public winUserId?: (google.protobuf.IInt64Value|null);

    /** BattlePlayLog winAvatar. */
    public winAvatar?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog winerChangeScore. */
    public winerChangeScore?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog ante. */
    public ante?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog grade. */
    public grade?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog pooling. */
    public pooling?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog showdown. */
    public showdown?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog folpRaise. */
    public folpRaise?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog reraise. */
    public reraise?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog continuationBet. */
    public continuationBet?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog win. */
    public win?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog handNum. */
    public handNum?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog seatNum. */
    public seatNum?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog cardType. */
    public cardType?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog betList. */
    public betList?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog keyCard. */
    public keyCard?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog seatPos. */
    public seatPos?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog dealIndex. */
    public dealIndex?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog avatar. */
    public avatar?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog showcardType. */
    public showcardType?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog publicCardAll. */
    public publicCardAll?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog playType. */
    public playType?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog omahaShow. */
    public omahaShow?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog seeComCards. */
    public seeComCards?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog seeAuCards. */
    public seeAuCards?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog isBigPot. */
    public isBigPot?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog squidWin. */
    public squidWin?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog nickname. */
    public nickname?: (google.protobuf.IStringValue|null);

    /** BattlePlayLog insuranceWin. */
    public insuranceWin?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog insuranceInvest. */
    public insuranceInvest?: (google.protobuf.IInt32Value|null);

    /** BattlePlayLog insuranceRiverInvest. */
    public insuranceRiverInvest?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattlePlayLog instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattlePlayLog instance
     */
    public static create(properties?: IBattlePlayLog): BattlePlayLog;

    /**
     * Encodes the specified BattlePlayLog message. Does not implicitly {@link BattlePlayLog.verify|verify} messages.
     * @param message BattlePlayLog message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattlePlayLog, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattlePlayLog message, length delimited. Does not implicitly {@link BattlePlayLog.verify|verify} messages.
     * @param message BattlePlayLog message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattlePlayLog, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattlePlayLog message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattlePlayLog
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattlePlayLog;

    /**
     * Decodes a BattlePlayLog message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattlePlayLog
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattlePlayLog;

    /**
     * Verifies a BattlePlayLog message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattlePlayLog message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattlePlayLog
     */
    public static fromObject(object: { [k: string]: any }): BattlePlayLog;

    /**
     * Creates a plain object from a BattlePlayLog message. Also converts values to other types if specified.
     * @param message BattlePlayLog
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattlePlayLog, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattlePlayLog to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattlePlayLog
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleUserCardInfoMsg. */
export interface IBattleUserCardInfoMsg {

    /** BattleUserCardInfoMsg userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleUserCardInfoMsg publicCards */
    publicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleUserCardInfoMsg cardType */
    cardType?: (google.protobuf.IStringValue|null);

    /** BattleUserCardInfoMsg isShowdown */
    isShowdown?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleUserCardInfoMsg. */
export class BattleUserCardInfoMsg implements IBattleUserCardInfoMsg {

    /**
     * Constructs a new BattleUserCardInfoMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleUserCardInfoMsg);

    /** BattleUserCardInfoMsg userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleUserCardInfoMsg publicCards. */
    public publicCards: google.protobuf.IInt32Value[];

    /** BattleUserCardInfoMsg cardType. */
    public cardType?: (google.protobuf.IStringValue|null);

    /** BattleUserCardInfoMsg isShowdown. */
    public isShowdown?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleUserCardInfoMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleUserCardInfoMsg instance
     */
    public static create(properties?: IBattleUserCardInfoMsg): BattleUserCardInfoMsg;

    /**
     * Encodes the specified BattleUserCardInfoMsg message. Does not implicitly {@link BattleUserCardInfoMsg.verify|verify} messages.
     * @param message BattleUserCardInfoMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleUserCardInfoMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleUserCardInfoMsg message, length delimited. Does not implicitly {@link BattleUserCardInfoMsg.verify|verify} messages.
     * @param message BattleUserCardInfoMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleUserCardInfoMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleUserCardInfoMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleUserCardInfoMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleUserCardInfoMsg;

    /**
     * Decodes a BattleUserCardInfoMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleUserCardInfoMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleUserCardInfoMsg;

    /**
     * Verifies a BattleUserCardInfoMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleUserCardInfoMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleUserCardInfoMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleUserCardInfoMsg;

    /**
     * Creates a plain object from a BattleUserCardInfoMsg message. Also converts values to other types if specified.
     * @param message BattleUserCardInfoMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleUserCardInfoMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleUserCardInfoMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleUserCardInfoMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleSendDoubleUserMsg. */
export interface IBattleSendDoubleUserMsg {

    /** BattleSendDoubleUserMsg userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleSendDoubleUserMsg betScore */
    betScore?: (google.protobuf.IInt32Value|null);

    /** BattleSendDoubleUserMsg handCards */
    handCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleSendDoubleUserMsg winProb */
    winProb?: (google.protobuf.IDoubleValue|null);

    /** BattleSendDoubleUserMsg nickname */
    nickname?: (google.protobuf.IStringValue|null);

    /** BattleSendDoubleUserMsg avatar */
    avatar?: (google.protobuf.IStringValue|null);
}

/** Represents a BattleSendDoubleUserMsg. */
export class BattleSendDoubleUserMsg implements IBattleSendDoubleUserMsg {

    /**
     * Constructs a new BattleSendDoubleUserMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleSendDoubleUserMsg);

    /** BattleSendDoubleUserMsg userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleSendDoubleUserMsg betScore. */
    public betScore?: (google.protobuf.IInt32Value|null);

    /** BattleSendDoubleUserMsg handCards. */
    public handCards: google.protobuf.IInt32Value[];

    /** BattleSendDoubleUserMsg winProb. */
    public winProb?: (google.protobuf.IDoubleValue|null);

    /** BattleSendDoubleUserMsg nickname. */
    public nickname?: (google.protobuf.IStringValue|null);

    /** BattleSendDoubleUserMsg avatar. */
    public avatar?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new BattleSendDoubleUserMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleSendDoubleUserMsg instance
     */
    public static create(properties?: IBattleSendDoubleUserMsg): BattleSendDoubleUserMsg;

    /**
     * Encodes the specified BattleSendDoubleUserMsg message. Does not implicitly {@link BattleSendDoubleUserMsg.verify|verify} messages.
     * @param message BattleSendDoubleUserMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleSendDoubleUserMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleSendDoubleUserMsg message, length delimited. Does not implicitly {@link BattleSendDoubleUserMsg.verify|verify} messages.
     * @param message BattleSendDoubleUserMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleSendDoubleUserMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleSendDoubleUserMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleSendDoubleUserMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleSendDoubleUserMsg;

    /**
     * Decodes a BattleSendDoubleUserMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleSendDoubleUserMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleSendDoubleUserMsg;

    /**
     * Verifies a BattleSendDoubleUserMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleSendDoubleUserMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleSendDoubleUserMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleSendDoubleUserMsg;

    /**
     * Creates a plain object from a BattleSendDoubleUserMsg message. Also converts values to other types if specified.
     * @param message BattleSendDoubleUserMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleSendDoubleUserMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleSendDoubleUserMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleSendDoubleUserMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleThanPO. */
export interface IBattleThanPO {

    /** BattleThanPO userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleThanPO changeScore */
    changeScore?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO changeAfterScore */
    changeAfterScore?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO isHighlight */
    isHighlight?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO highHandCards */
    highHandCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleThanPO publicCards */
    publicCards?: (google.protobuf.IInt32Value[]|null);

    /** BattleThanPO cardType */
    cardType?: (google.protobuf.IStringValue|null);

    /** BattleThanPO addScore */
    addScore?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO seatNum */
    seatNum?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO isShowdown */
    isShowdown?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO fund */
    fund?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO fundFirst */
    fundFirst?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO fundSecond */
    fundSecond?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO addScoreFirst */
    addScoreFirst?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO addScoreSecond */
    addScoreSecond?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO changeScoreFirst */
    changeScoreFirst?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO changeScoreSecond */
    changeScoreSecond?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO squidScore */
    squidScore?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO insuranceResult */
    insuranceResult?: (google.protobuf.IInt32Value|null);
}

/** Represents a BattleThanPO. */
export class BattleThanPO implements IBattleThanPO {

    /**
     * Constructs a new BattleThanPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleThanPO);

    /** BattleThanPO userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleThanPO changeScore. */
    public changeScore?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO changeAfterScore. */
    public changeAfterScore?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO isHighlight. */
    public isHighlight?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO highHandCards. */
    public highHandCards: google.protobuf.IInt32Value[];

    /** BattleThanPO publicCards. */
    public publicCards: google.protobuf.IInt32Value[];

    /** BattleThanPO cardType. */
    public cardType?: (google.protobuf.IStringValue|null);

    /** BattleThanPO addScore. */
    public addScore?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO seatNum. */
    public seatNum?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO isShowdown. */
    public isShowdown?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO fund. */
    public fund?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO fundFirst. */
    public fundFirst?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO fundSecond. */
    public fundSecond?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO addScoreFirst. */
    public addScoreFirst?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO addScoreSecond. */
    public addScoreSecond?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO changeScoreFirst. */
    public changeScoreFirst?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO changeScoreSecond. */
    public changeScoreSecond?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO squidScore. */
    public squidScore?: (google.protobuf.IInt32Value|null);

    /** BattleThanPO insuranceResult. */
    public insuranceResult?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BattleThanPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleThanPO instance
     */
    public static create(properties?: IBattleThanPO): BattleThanPO;

    /**
     * Encodes the specified BattleThanPO message. Does not implicitly {@link BattleThanPO.verify|verify} messages.
     * @param message BattleThanPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleThanPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleThanPO message, length delimited. Does not implicitly {@link BattleThanPO.verify|verify} messages.
     * @param message BattleThanPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleThanPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleThanPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleThanPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleThanPO;

    /**
     * Decodes a BattleThanPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleThanPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleThanPO;

    /**
     * Verifies a BattleThanPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleThanPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleThanPO
     */
    public static fromObject(object: { [k: string]: any }): BattleThanPO;

    /**
     * Creates a plain object from a BattleThanPO message. Also converts values to other types if specified.
     * @param message BattleThanPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleThanPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleThanPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleThanPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a RaiseBlindMsg. */
export interface IRaiseBlindMsg {

    /** RaiseBlindMsg nextSB */
    nextSB?: (google.protobuf.IInt32Value|null);

    /** RaiseBlindMsg curSB */
    curSB?: (google.protobuf.IInt32Value|null);

    /** RaiseBlindMsg residueSec */
    residueSec?: (google.protobuf.IInt64Value|null);
}

/** Represents a RaiseBlindMsg. */
export class RaiseBlindMsg implements IRaiseBlindMsg {

    /**
     * Constructs a new RaiseBlindMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IRaiseBlindMsg);

    /** RaiseBlindMsg nextSB. */
    public nextSB?: (google.protobuf.IInt32Value|null);

    /** RaiseBlindMsg curSB. */
    public curSB?: (google.protobuf.IInt32Value|null);

    /** RaiseBlindMsg residueSec. */
    public residueSec?: (google.protobuf.IInt64Value|null);

    /**
     * Creates a new RaiseBlindMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns RaiseBlindMsg instance
     */
    public static create(properties?: IRaiseBlindMsg): RaiseBlindMsg;

    /**
     * Encodes the specified RaiseBlindMsg message. Does not implicitly {@link RaiseBlindMsg.verify|verify} messages.
     * @param message RaiseBlindMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IRaiseBlindMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified RaiseBlindMsg message, length delimited. Does not implicitly {@link RaiseBlindMsg.verify|verify} messages.
     * @param message RaiseBlindMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IRaiseBlindMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a RaiseBlindMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RaiseBlindMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): RaiseBlindMsg;

    /**
     * Decodes a RaiseBlindMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns RaiseBlindMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): RaiseBlindMsg;

    /**
     * Verifies a RaiseBlindMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a RaiseBlindMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns RaiseBlindMsg
     */
    public static fromObject(object: { [k: string]: any }): RaiseBlindMsg;

    /**
     * Creates a plain object from a RaiseBlindMsg message. Also converts values to other types if specified.
     * @param message RaiseBlindMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: RaiseBlindMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this RaiseBlindMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for RaiseBlindMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a ForceSeeCardResp. */
export interface IForceSeeCardResp {

    /** ForceSeeCardResp userId */
    userId?: (number|Long|null);

    /** ForceSeeCardResp handNum */
    handNum?: (number|null);

    /** ForceSeeCardResp handCards */
    handCards?: (IBattleHandCardPO[]|null);

    /** ForceSeeCardResp forceSeeCardCfg */
    forceSeeCardCfg?: (IBattleForceSeeCardCfgPO|null);
}

/** Represents a ForceSeeCardResp. */
export class ForceSeeCardResp implements IForceSeeCardResp {

    /**
     * Constructs a new ForceSeeCardResp.
     * @param [properties] Properties to set
     */
    constructor(properties?: IForceSeeCardResp);

    /** ForceSeeCardResp userId. */
    public userId: (number|Long);

    /** ForceSeeCardResp handNum. */
    public handNum: number;

    /** ForceSeeCardResp handCards. */
    public handCards: IBattleHandCardPO[];

    /** ForceSeeCardResp forceSeeCardCfg. */
    public forceSeeCardCfg?: (IBattleForceSeeCardCfgPO|null);

    /**
     * Creates a new ForceSeeCardResp instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ForceSeeCardResp instance
     */
    public static create(properties?: IForceSeeCardResp): ForceSeeCardResp;

    /**
     * Encodes the specified ForceSeeCardResp message. Does not implicitly {@link ForceSeeCardResp.verify|verify} messages.
     * @param message ForceSeeCardResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IForceSeeCardResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ForceSeeCardResp message, length delimited. Does not implicitly {@link ForceSeeCardResp.verify|verify} messages.
     * @param message ForceSeeCardResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IForceSeeCardResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ForceSeeCardResp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ForceSeeCardResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ForceSeeCardResp;

    /**
     * Decodes a ForceSeeCardResp message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ForceSeeCardResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ForceSeeCardResp;

    /**
     * Verifies a ForceSeeCardResp message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ForceSeeCardResp message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ForceSeeCardResp
     */
    public static fromObject(object: { [k: string]: any }): ForceSeeCardResp;

    /**
     * Creates a plain object from a ForceSeeCardResp message. Also converts values to other types if specified.
     * @param message ForceSeeCardResp
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ForceSeeCardResp, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ForceSeeCardResp to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ForceSeeCardResp
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleHandCardPO. */
export interface IBattleHandCardPO {

    /** BattleHandCardPO userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** BattleHandCardPO handCards */
    handCards?: (google.protobuf.IInt32Value[]|null);
}

/** Represents a BattleHandCardPO. */
export class BattleHandCardPO implements IBattleHandCardPO {

    /**
     * Constructs a new BattleHandCardPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleHandCardPO);

    /** BattleHandCardPO userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** BattleHandCardPO handCards. */
    public handCards: google.protobuf.IInt32Value[];

    /**
     * Creates a new BattleHandCardPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleHandCardPO instance
     */
    public static create(properties?: IBattleHandCardPO): BattleHandCardPO;

    /**
     * Encodes the specified BattleHandCardPO message. Does not implicitly {@link BattleHandCardPO.verify|verify} messages.
     * @param message BattleHandCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleHandCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleHandCardPO message, length delimited. Does not implicitly {@link BattleHandCardPO.verify|verify} messages.
     * @param message BattleHandCardPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleHandCardPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleHandCardPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleHandCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleHandCardPO;

    /**
     * Decodes a BattleHandCardPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleHandCardPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleHandCardPO;

    /**
     * Verifies a BattleHandCardPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleHandCardPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleHandCardPO
     */
    public static fromObject(object: { [k: string]: any }): BattleHandCardPO;

    /**
     * Creates a plain object from a BattleHandCardPO message. Also converts values to other types if specified.
     * @param message BattleHandCardPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleHandCardPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleHandCardPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleHandCardPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a MarqueeMsgResp. */
export interface IMarqueeMsgResp {

    /** MarqueeMsgResp repeatCount */
    repeatCount?: (number|null);

    /** MarqueeMsgResp content */
    content?: (google.protobuf.IStringValue|null);

    /** MarqueeMsgResp specifyType */
    specifyType?: (number|null);
}

/** Represents a MarqueeMsgResp. */
export class MarqueeMsgResp implements IMarqueeMsgResp {

    /**
     * Constructs a new MarqueeMsgResp.
     * @param [properties] Properties to set
     */
    constructor(properties?: IMarqueeMsgResp);

    /** MarqueeMsgResp repeatCount. */
    public repeatCount: number;

    /** MarqueeMsgResp content. */
    public content?: (google.protobuf.IStringValue|null);

    /** MarqueeMsgResp specifyType. */
    public specifyType: number;

    /**
     * Creates a new MarqueeMsgResp instance using the specified properties.
     * @param [properties] Properties to set
     * @returns MarqueeMsgResp instance
     */
    public static create(properties?: IMarqueeMsgResp): MarqueeMsgResp;

    /**
     * Encodes the specified MarqueeMsgResp message. Does not implicitly {@link MarqueeMsgResp.verify|verify} messages.
     * @param message MarqueeMsgResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IMarqueeMsgResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified MarqueeMsgResp message, length delimited. Does not implicitly {@link MarqueeMsgResp.verify|verify} messages.
     * @param message MarqueeMsgResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IMarqueeMsgResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a MarqueeMsgResp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MarqueeMsgResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): MarqueeMsgResp;

    /**
     * Decodes a MarqueeMsgResp message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns MarqueeMsgResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): MarqueeMsgResp;

    /**
     * Verifies a MarqueeMsgResp message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a MarqueeMsgResp message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns MarqueeMsgResp
     */
    public static fromObject(object: { [k: string]: any }): MarqueeMsgResp;

    /**
     * Creates a plain object from a MarqueeMsgResp message. Also converts values to other types if specified.
     * @param message MarqueeMsgResp
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: MarqueeMsgResp, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this MarqueeMsgResp to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for MarqueeMsgResp
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BaseResp. */
export interface IBaseResp {

    /** BaseResp errorCode */
    errorCode?: (google.protobuf.IInt32Value|null);

    /** BaseResp errMsg */
    errMsg?: (google.protobuf.IStringValue|null);

    /** BaseResp sysTime */
    sysTime?: (google.protobuf.IInt64Value|null);
}

/** Represents a BaseResp. */
export class BaseResp implements IBaseResp {

    /**
     * Constructs a new BaseResp.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBaseResp);

    /** BaseResp errorCode. */
    public errorCode?: (google.protobuf.IInt32Value|null);

    /** BaseResp errMsg. */
    public errMsg?: (google.protobuf.IStringValue|null);

    /** BaseResp sysTime. */
    public sysTime?: (google.protobuf.IInt64Value|null);

    /**
     * Creates a new BaseResp instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BaseResp instance
     */
    public static create(properties?: IBaseResp): BaseResp;

    /**
     * Encodes the specified BaseResp message. Does not implicitly {@link BaseResp.verify|verify} messages.
     * @param message BaseResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBaseResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BaseResp message, length delimited. Does not implicitly {@link BaseResp.verify|verify} messages.
     * @param message BaseResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBaseResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BaseResp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BaseResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BaseResp;

    /**
     * Decodes a BaseResp message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BaseResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BaseResp;

    /**
     * Verifies a BaseResp message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BaseResp message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BaseResp
     */
    public static fromObject(object: { [k: string]: any }): BaseResp;

    /**
     * Creates a plain object from a BaseResp message. Also converts values to other types if specified.
     * @param message BaseResp
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BaseResp, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BaseResp to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BaseResp
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a PersonalData. */
export interface IPersonalData {

    /** PersonalData poolingHandNum */
    poolingHandNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData winNum */
    winNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData showdownNum */
    showdownNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData folpRaiseNum */
    folpRaiseNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData reraiseNum */
    reraiseNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData continuationBet */
    continuationBet?: (google.protobuf.IInt32Value|null);

    /** PersonalData userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** PersonalData totalHand */
    totalHand?: (google.protobuf.IInt32Value|null);

    /** PersonalData signature */
    signature?: (google.protobuf.IStringValue|null);
}

/** Represents a PersonalData. */
export class PersonalData implements IPersonalData {

    /**
     * Constructs a new PersonalData.
     * @param [properties] Properties to set
     */
    constructor(properties?: IPersonalData);

    /** PersonalData poolingHandNum. */
    public poolingHandNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData winNum. */
    public winNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData showdownNum. */
    public showdownNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData folpRaiseNum. */
    public folpRaiseNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData reraiseNum. */
    public reraiseNum?: (google.protobuf.IInt32Value|null);

    /** PersonalData continuationBet. */
    public continuationBet?: (google.protobuf.IInt32Value|null);

    /** PersonalData userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** PersonalData totalHand. */
    public totalHand?: (google.protobuf.IInt32Value|null);

    /** PersonalData signature. */
    public signature?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new PersonalData instance using the specified properties.
     * @param [properties] Properties to set
     * @returns PersonalData instance
     */
    public static create(properties?: IPersonalData): PersonalData;

    /**
     * Encodes the specified PersonalData message. Does not implicitly {@link PersonalData.verify|verify} messages.
     * @param message PersonalData message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IPersonalData, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified PersonalData message, length delimited. Does not implicitly {@link PersonalData.verify|verify} messages.
     * @param message PersonalData message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IPersonalData, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a PersonalData message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PersonalData
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): PersonalData;

    /**
     * Decodes a PersonalData message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns PersonalData
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): PersonalData;

    /**
     * Verifies a PersonalData message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a PersonalData message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns PersonalData
     */
    public static fromObject(object: { [k: string]: any }): PersonalData;

    /**
     * Creates a plain object from a PersonalData message. Also converts values to other types if specified.
     * @param message PersonalData
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: PersonalData, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this PersonalData to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for PersonalData
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleUserProfileResp. */
export interface IBattleUserProfileResp {

    /** BattleUserProfileResp listData */
    listData?: (IPersonalData[]|null);
}

/** Represents a BattleUserProfileResp. */
export class BattleUserProfileResp implements IBattleUserProfileResp {

    /**
     * Constructs a new BattleUserProfileResp.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleUserProfileResp);

    /** BattleUserProfileResp listData. */
    public listData: IPersonalData[];

    /**
     * Creates a new BattleUserProfileResp instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleUserProfileResp instance
     */
    public static create(properties?: IBattleUserProfileResp): BattleUserProfileResp;

    /**
     * Encodes the specified BattleUserProfileResp message. Does not implicitly {@link BattleUserProfileResp.verify|verify} messages.
     * @param message BattleUserProfileResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleUserProfileResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleUserProfileResp message, length delimited. Does not implicitly {@link BattleUserProfileResp.verify|verify} messages.
     * @param message BattleUserProfileResp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleUserProfileResp, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleUserProfileResp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleUserProfileResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleUserProfileResp;

    /**
     * Decodes a BattleUserProfileResp message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleUserProfileResp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleUserProfileResp;

    /**
     * Verifies a BattleUserProfileResp message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleUserProfileResp message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleUserProfileResp
     */
    public static fromObject(object: { [k: string]: any }): BattleUserProfileResp;

    /**
     * Creates a plain object from a BattleUserProfileResp message. Also converts values to other types if specified.
     * @param message BattleUserProfileResp
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleUserProfileResp, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleUserProfileResp to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleUserProfileResp
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a StarInfo. */
export interface IStarInfo {

    /** StarInfo pic */
    pic?: (google.protobuf.IStringValue|null);

    /** StarInfo topic */
    topic?: (google.protobuf.IStringValue[]|null);
}

/** Represents a StarInfo. */
export class StarInfo implements IStarInfo {

    /**
     * Constructs a new StarInfo.
     * @param [properties] Properties to set
     */
    constructor(properties?: IStarInfo);

    /** StarInfo pic. */
    public pic?: (google.protobuf.IStringValue|null);

    /** StarInfo topic. */
    public topic: google.protobuf.IStringValue[];

    /**
     * Creates a new StarInfo instance using the specified properties.
     * @param [properties] Properties to set
     * @returns StarInfo instance
     */
    public static create(properties?: IStarInfo): StarInfo;

    /**
     * Encodes the specified StarInfo message. Does not implicitly {@link StarInfo.verify|verify} messages.
     * @param message StarInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IStarInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified StarInfo message, length delimited. Does not implicitly {@link StarInfo.verify|verify} messages.
     * @param message StarInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IStarInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a StarInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns StarInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): StarInfo;

    /**
     * Decodes a StarInfo message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns StarInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): StarInfo;

    /**
     * Verifies a StarInfo message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a StarInfo message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns StarInfo
     */
    public static fromObject(object: { [k: string]: any }): StarInfo;

    /**
     * Creates a plain object from a StarInfo message. Also converts values to other types if specified.
     * @param message StarInfo
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: StarInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this StarInfo to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for StarInfo
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a StarGameMsg. */
export interface IStarGameMsg {

    /** StarGameMsg roomId */
    roomId?: (number|Long|null);

    /** StarGameMsg star */
    star?: (IStarInfo[]|null);

    /** StarGameMsg text */
    text?: (google.protobuf.IStringValue[]|null);
}

/** Represents a StarGameMsg. */
export class StarGameMsg implements IStarGameMsg {

    /**
     * Constructs a new StarGameMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IStarGameMsg);

    /** StarGameMsg roomId. */
    public roomId: (number|Long);

    /** StarGameMsg star. */
    public star: IStarInfo[];

    /** StarGameMsg text. */
    public text: google.protobuf.IStringValue[];

    /**
     * Creates a new StarGameMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns StarGameMsg instance
     */
    public static create(properties?: IStarGameMsg): StarGameMsg;

    /**
     * Encodes the specified StarGameMsg message. Does not implicitly {@link StarGameMsg.verify|verify} messages.
     * @param message StarGameMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IStarGameMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified StarGameMsg message, length delimited. Does not implicitly {@link StarGameMsg.verify|verify} messages.
     * @param message StarGameMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IStarGameMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a StarGameMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns StarGameMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): StarGameMsg;

    /**
     * Decodes a StarGameMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns StarGameMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): StarGameMsg;

    /**
     * Verifies a StarGameMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a StarGameMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns StarGameMsg
     */
    public static fromObject(object: { [k: string]: any }): StarGameMsg;

    /**
     * Creates a plain object from a StarGameMsg message. Also converts values to other types if specified.
     * @param message StarGameMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: StarGameMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this StarGameMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for StarGameMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a MttStartMsg. */
export interface IMttStartMsg {

    /** MttStartMsg roomId */
    roomId?: (number|Long|null);

    /** MttStartMsg content */
    content?: (google.protobuf.IStringValue|null);

    /** MttStartMsg name */
    name?: (google.protobuf.IStringValue|null);

    /** MttStartMsg seconds */
    seconds?: (number|null);
}

/** Represents a MttStartMsg. */
export class MttStartMsg implements IMttStartMsg {

    /**
     * Constructs a new MttStartMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IMttStartMsg);

    /** MttStartMsg roomId. */
    public roomId: (number|Long);

    /** MttStartMsg content. */
    public content?: (google.protobuf.IStringValue|null);

    /** MttStartMsg name. */
    public name?: (google.protobuf.IStringValue|null);

    /** MttStartMsg seconds. */
    public seconds: number;

    /**
     * Creates a new MttStartMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns MttStartMsg instance
     */
    public static create(properties?: IMttStartMsg): MttStartMsg;

    /**
     * Encodes the specified MttStartMsg message. Does not implicitly {@link MttStartMsg.verify|verify} messages.
     * @param message MttStartMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IMttStartMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified MttStartMsg message, length delimited. Does not implicitly {@link MttStartMsg.verify|verify} messages.
     * @param message MttStartMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IMttStartMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a MttStartMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MttStartMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): MttStartMsg;

    /**
     * Decodes a MttStartMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns MttStartMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): MttStartMsg;

    /**
     * Verifies a MttStartMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a MttStartMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns MttStartMsg
     */
    public static fromObject(object: { [k: string]: any }): MttStartMsg;

    /**
     * Creates a plain object from a MttStartMsg message. Also converts values to other types if specified.
     * @param message MttStartMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: MttStartMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this MttStartMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for MttStartMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an UpdateUserDiamondBalance. */
export interface IUpdateUserDiamondBalance {

    /** UpdateUserDiamondBalance diamondBalance */
    diamondBalance?: (google.protobuf.IDoubleValue|null);

    /** UpdateUserDiamondBalance userId */
    userId?: (google.protobuf.IInt64Value|null);

    /** UpdateUserDiamondBalance giftDiamondBalance */
    giftDiamondBalance?: (google.protobuf.IDoubleValue|null);

    /** UpdateUserDiamondBalance gift2Balance */
    gift2Balance?: (google.protobuf.IDoubleValue|null);
}

/** Represents an UpdateUserDiamondBalance. */
export class UpdateUserDiamondBalance implements IUpdateUserDiamondBalance {

    /**
     * Constructs a new UpdateUserDiamondBalance.
     * @param [properties] Properties to set
     */
    constructor(properties?: IUpdateUserDiamondBalance);

    /** UpdateUserDiamondBalance diamondBalance. */
    public diamondBalance?: (google.protobuf.IDoubleValue|null);

    /** UpdateUserDiamondBalance userId. */
    public userId?: (google.protobuf.IInt64Value|null);

    /** UpdateUserDiamondBalance giftDiamondBalance. */
    public giftDiamondBalance?: (google.protobuf.IDoubleValue|null);

    /** UpdateUserDiamondBalance gift2Balance. */
    public gift2Balance?: (google.protobuf.IDoubleValue|null);

    /**
     * Creates a new UpdateUserDiamondBalance instance using the specified properties.
     * @param [properties] Properties to set
     * @returns UpdateUserDiamondBalance instance
     */
    public static create(properties?: IUpdateUserDiamondBalance): UpdateUserDiamondBalance;

    /**
     * Encodes the specified UpdateUserDiamondBalance message. Does not implicitly {@link UpdateUserDiamondBalance.verify|verify} messages.
     * @param message UpdateUserDiamondBalance message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IUpdateUserDiamondBalance, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified UpdateUserDiamondBalance message, length delimited. Does not implicitly {@link UpdateUserDiamondBalance.verify|verify} messages.
     * @param message UpdateUserDiamondBalance message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IUpdateUserDiamondBalance, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an UpdateUserDiamondBalance message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns UpdateUserDiamondBalance
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): UpdateUserDiamondBalance;

    /**
     * Decodes an UpdateUserDiamondBalance message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns UpdateUserDiamondBalance
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): UpdateUserDiamondBalance;

    /**
     * Verifies an UpdateUserDiamondBalance message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an UpdateUserDiamondBalance message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns UpdateUserDiamondBalance
     */
    public static fromObject(object: { [k: string]: any }): UpdateUserDiamondBalance;

    /**
     * Creates a plain object from an UpdateUserDiamondBalance message. Also converts values to other types if specified.
     * @param message UpdateUserDiamondBalance
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: UpdateUserDiamondBalance, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this UpdateUserDiamondBalance to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for UpdateUserDiamondBalance
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a DiamondRespMsgPO. */
export interface IDiamondRespMsgPO {

    /** DiamondRespMsgPO sourceUserId */
    sourceUserId?: (google.protobuf.IInt64Value|null);

    /** DiamondRespMsgPO targetUserId */
    targetUserId?: (google.protobuf.IInt64Value|null);

    /** DiamondRespMsgPO diamondId */
    diamondId?: (google.protobuf.IInt32Value|null);

    /** DiamondRespMsgPO num */
    num?: (google.protobuf.IInt32Value|null);

    /** DiamondRespMsgPO remainCount */
    remainCount?: (google.protobuf.IInt32Value|null);

    /** DiamondRespMsgPO ext */
    ext?: (google.protobuf.IStringValue|null);
}

/** Represents a DiamondRespMsgPO. */
export class DiamondRespMsgPO implements IDiamondRespMsgPO {

    /**
     * Constructs a new DiamondRespMsgPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IDiamondRespMsgPO);

    /** DiamondRespMsgPO sourceUserId. */
    public sourceUserId?: (google.protobuf.IInt64Value|null);

    /** DiamondRespMsgPO targetUserId. */
    public targetUserId?: (google.protobuf.IInt64Value|null);

    /** DiamondRespMsgPO diamondId. */
    public diamondId?: (google.protobuf.IInt32Value|null);

    /** DiamondRespMsgPO num. */
    public num?: (google.protobuf.IInt32Value|null);

    /** DiamondRespMsgPO remainCount. */
    public remainCount?: (google.protobuf.IInt32Value|null);

    /** DiamondRespMsgPO ext. */
    public ext?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new DiamondRespMsgPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns DiamondRespMsgPO instance
     */
    public static create(properties?: IDiamondRespMsgPO): DiamondRespMsgPO;

    /**
     * Encodes the specified DiamondRespMsgPO message. Does not implicitly {@link DiamondRespMsgPO.verify|verify} messages.
     * @param message DiamondRespMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IDiamondRespMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified DiamondRespMsgPO message, length delimited. Does not implicitly {@link DiamondRespMsgPO.verify|verify} messages.
     * @param message DiamondRespMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IDiamondRespMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a DiamondRespMsgPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DiamondRespMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): DiamondRespMsgPO;

    /**
     * Decodes a DiamondRespMsgPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns DiamondRespMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): DiamondRespMsgPO;

    /**
     * Verifies a DiamondRespMsgPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a DiamondRespMsgPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns DiamondRespMsgPO
     */
    public static fromObject(object: { [k: string]: any }): DiamondRespMsgPO;

    /**
     * Creates a plain object from a DiamondRespMsgPO message. Also converts values to other types if specified.
     * @param message DiamondRespMsgPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: DiamondRespMsgPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this DiamondRespMsgPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for DiamondRespMsgPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a RedPackMsg. */
export interface IRedPackMsg {

    /** RedPackMsg sourceId */
    sourceId?: (number|Long|null);

    /** RedPackMsg targetId */
    targetId?: (number|Long|null);

    /** RedPackMsg type */
    type?: (number|null);

    /** RedPackMsg num */
    num?: (number|null);

    /** RedPackMsg remainCount */
    remainCount?: (number|null);

    /** RedPackMsg ext */
    ext?: (google.protobuf.IStringValue|null);
}

/** Represents a RedPackMsg. */
export class RedPackMsg implements IRedPackMsg {

    /**
     * Constructs a new RedPackMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IRedPackMsg);

    /** RedPackMsg sourceId. */
    public sourceId: (number|Long);

    /** RedPackMsg targetId. */
    public targetId: (number|Long);

    /** RedPackMsg type. */
    public type: number;

    /** RedPackMsg num. */
    public num: number;

    /** RedPackMsg remainCount. */
    public remainCount: number;

    /** RedPackMsg ext. */
    public ext?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new RedPackMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns RedPackMsg instance
     */
    public static create(properties?: IRedPackMsg): RedPackMsg;

    /**
     * Encodes the specified RedPackMsg message. Does not implicitly {@link RedPackMsg.verify|verify} messages.
     * @param message RedPackMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IRedPackMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified RedPackMsg message, length delimited. Does not implicitly {@link RedPackMsg.verify|verify} messages.
     * @param message RedPackMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IRedPackMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a RedPackMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RedPackMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): RedPackMsg;

    /**
     * Decodes a RedPackMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns RedPackMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): RedPackMsg;

    /**
     * Verifies a RedPackMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a RedPackMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns RedPackMsg
     */
    public static fromObject(object: { [k: string]: any }): RedPackMsg;

    /**
     * Creates a plain object from a RedPackMsg message. Also converts values to other types if specified.
     * @param message RedPackMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: RedPackMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this RedPackMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for RedPackMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a SportTicketsOptions. */
export interface ISportTicketsOptions {

    /** SportTicketsOptions matchId */
    matchId?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions sportId */
    sportId?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions matchName */
    matchName?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions matchState */
    matchState?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions kickOffTime */
    kickOffTime?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions tournamentId */
    tournamentId?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions tournamentName */
    tournamentName?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions tournamentLevel */
    tournamentLevel?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions homeScore */
    homeScore?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions awayScore */
    awayScore?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions marketId */
    marketId?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions marketType */
    marketType?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions marketGroup */
    marketGroup?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions marketStage */
    marketStage?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions betBar */
    betBar?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions sabaMarketId */
    sabaMarketId?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions sabaBetOption */
    sabaBetOption?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions odds */
    odds?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsOptions optionId */
    optionId?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions betOption */
    betOption?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions settleTime */
    settleTime?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions settleScore */
    settleScore?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions settleResult */
    settleResult?: (google.protobuf.IInt32Value|null);
}

/** Represents a SportTicketsOptions. */
export class SportTicketsOptions implements ISportTicketsOptions {

    /**
     * Constructs a new SportTicketsOptions.
     * @param [properties] Properties to set
     */
    constructor(properties?: ISportTicketsOptions);

    /** SportTicketsOptions matchId. */
    public matchId?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions sportId. */
    public sportId?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions matchName. */
    public matchName?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions matchState. */
    public matchState?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions kickOffTime. */
    public kickOffTime?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions tournamentId. */
    public tournamentId?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions tournamentName. */
    public tournamentName?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions tournamentLevel. */
    public tournamentLevel?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions homeScore. */
    public homeScore?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions awayScore. */
    public awayScore?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions marketId. */
    public marketId?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions marketType. */
    public marketType?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions marketGroup. */
    public marketGroup?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions marketStage. */
    public marketStage?: (google.protobuf.IInt32Value|null);

    /** SportTicketsOptions betBar. */
    public betBar?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions sabaMarketId. */
    public sabaMarketId?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions sabaBetOption. */
    public sabaBetOption?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions odds. */
    public odds?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsOptions optionId. */
    public optionId?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions betOption. */
    public betOption?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions settleTime. */
    public settleTime?: (google.protobuf.IInt64Value|null);

    /** SportTicketsOptions settleScore. */
    public settleScore?: (google.protobuf.IStringValue|null);

    /** SportTicketsOptions settleResult. */
    public settleResult?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new SportTicketsOptions instance using the specified properties.
     * @param [properties] Properties to set
     * @returns SportTicketsOptions instance
     */
    public static create(properties?: ISportTicketsOptions): SportTicketsOptions;

    /**
     * Encodes the specified SportTicketsOptions message. Does not implicitly {@link SportTicketsOptions.verify|verify} messages.
     * @param message SportTicketsOptions message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ISportTicketsOptions, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified SportTicketsOptions message, length delimited. Does not implicitly {@link SportTicketsOptions.verify|verify} messages.
     * @param message SportTicketsOptions message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ISportTicketsOptions, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a SportTicketsOptions message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns SportTicketsOptions
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): SportTicketsOptions;

    /**
     * Decodes a SportTicketsOptions message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns SportTicketsOptions
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): SportTicketsOptions;

    /**
     * Verifies a SportTicketsOptions message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a SportTicketsOptions message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns SportTicketsOptions
     */
    public static fromObject(object: { [k: string]: any }): SportTicketsOptions;

    /**
     * Creates a plain object from a SportTicketsOptions message. Also converts values to other types if specified.
     * @param message SportTicketsOptions
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: SportTicketsOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this SportTicketsOptions to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for SportTicketsOptions
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a SportTicketsBets. */
export interface ISportTicketsBets {

    /** SportTicketsBets betM */
    betM?: (google.protobuf.IInt32Value|null);

    /** SportTicketsBets betN */
    betN?: (google.protobuf.IInt32Value|null);

    /** SportTicketsBets betCount */
    betCount?: (google.protobuf.IInt32Value|null);

    /** SportTicketsBets betAmount */
    betAmount?: (google.protobuf.IDoubleValue|null);
}

/** Represents a SportTicketsBets. */
export class SportTicketsBets implements ISportTicketsBets {

    /**
     * Constructs a new SportTicketsBets.
     * @param [properties] Properties to set
     */
    constructor(properties?: ISportTicketsBets);

    /** SportTicketsBets betM. */
    public betM?: (google.protobuf.IInt32Value|null);

    /** SportTicketsBets betN. */
    public betN?: (google.protobuf.IInt32Value|null);

    /** SportTicketsBets betCount. */
    public betCount?: (google.protobuf.IInt32Value|null);

    /** SportTicketsBets betAmount. */
    public betAmount?: (google.protobuf.IDoubleValue|null);

    /**
     * Creates a new SportTicketsBets instance using the specified properties.
     * @param [properties] Properties to set
     * @returns SportTicketsBets instance
     */
    public static create(properties?: ISportTicketsBets): SportTicketsBets;

    /**
     * Encodes the specified SportTicketsBets message. Does not implicitly {@link SportTicketsBets.verify|verify} messages.
     * @param message SportTicketsBets message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ISportTicketsBets, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified SportTicketsBets message, length delimited. Does not implicitly {@link SportTicketsBets.verify|verify} messages.
     * @param message SportTicketsBets message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ISportTicketsBets, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a SportTicketsBets message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns SportTicketsBets
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): SportTicketsBets;

    /**
     * Decodes a SportTicketsBets message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns SportTicketsBets
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): SportTicketsBets;

    /**
     * Verifies a SportTicketsBets message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a SportTicketsBets message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns SportTicketsBets
     */
    public static fromObject(object: { [k: string]: any }): SportTicketsBets;

    /**
     * Creates a plain object from a SportTicketsBets message. Also converts values to other types if specified.
     * @param message SportTicketsBets
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: SportTicketsBets, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this SportTicketsBets to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for SportTicketsBets
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a SportTicketsRecords. */
export interface ISportTicketsRecords {

    /** SportTicketsRecords ticketId */
    ticketId?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords betAmount */
    betAmount?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsRecords ticketStatus */
    ticketStatus?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords sabaTicketId */
    sabaTicketId?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords compUserId */
    compUserId?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords betType */
    betType?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords currency */
    currency?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords betTime */
    betTime?: (google.protobuf.IInt64Value|null);

    /** SportTicketsRecords domain */
    domain?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords clientType */
    clientType?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords appId */
    appId?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords ip */
    ip?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords settleAmount */
    settleAmount?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsRecords settleResult */
    settleResult?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords settleTime */
    settleTime?: (google.protobuf.IInt64Value|null);

    /** SportTicketsRecords reSettle */
    reSettle?: (google.protobuf.IBoolValue|null);

    /** SportTicketsRecords beforeAmount */
    beforeAmount?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsRecords afterAmount */
    afterAmount?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsRecords options */
    options?: (ISportTicketsOptions[]|null);

    /** SportTicketsRecords bets */
    bets?: (ISportTicketsBets[]|null);
}

/** Represents a SportTicketsRecords. */
export class SportTicketsRecords implements ISportTicketsRecords {

    /**
     * Constructs a new SportTicketsRecords.
     * @param [properties] Properties to set
     */
    constructor(properties?: ISportTicketsRecords);

    /** SportTicketsRecords ticketId. */
    public ticketId?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords betAmount. */
    public betAmount?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsRecords ticketStatus. */
    public ticketStatus?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords sabaTicketId. */
    public sabaTicketId?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords compUserId. */
    public compUserId?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords betType. */
    public betType?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords currency. */
    public currency?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords betTime. */
    public betTime?: (google.protobuf.IInt64Value|null);

    /** SportTicketsRecords domain. */
    public domain?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords clientType. */
    public clientType?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords appId. */
    public appId?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords ip. */
    public ip?: (google.protobuf.IStringValue|null);

    /** SportTicketsRecords settleAmount. */
    public settleAmount?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsRecords settleResult. */
    public settleResult?: (google.protobuf.IInt32Value|null);

    /** SportTicketsRecords settleTime. */
    public settleTime?: (google.protobuf.IInt64Value|null);

    /** SportTicketsRecords reSettle. */
    public reSettle?: (google.protobuf.IBoolValue|null);

    /** SportTicketsRecords beforeAmount. */
    public beforeAmount?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsRecords afterAmount. */
    public afterAmount?: (google.protobuf.IDoubleValue|null);

    /** SportTicketsRecords options. */
    public options: ISportTicketsOptions[];

    /** SportTicketsRecords bets. */
    public bets: ISportTicketsBets[];

    /**
     * Creates a new SportTicketsRecords instance using the specified properties.
     * @param [properties] Properties to set
     * @returns SportTicketsRecords instance
     */
    public static create(properties?: ISportTicketsRecords): SportTicketsRecords;

    /**
     * Encodes the specified SportTicketsRecords message. Does not implicitly {@link SportTicketsRecords.verify|verify} messages.
     * @param message SportTicketsRecords message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ISportTicketsRecords, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified SportTicketsRecords message, length delimited. Does not implicitly {@link SportTicketsRecords.verify|verify} messages.
     * @param message SportTicketsRecords message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ISportTicketsRecords, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a SportTicketsRecords message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns SportTicketsRecords
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): SportTicketsRecords;

    /**
     * Decodes a SportTicketsRecords message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns SportTicketsRecords
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): SportTicketsRecords;

    /**
     * Verifies a SportTicketsRecords message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a SportTicketsRecords message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns SportTicketsRecords
     */
    public static fromObject(object: { [k: string]: any }): SportTicketsRecords;

    /**
     * Creates a plain object from a SportTicketsRecords message. Also converts values to other types if specified.
     * @param message SportTicketsRecords
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: SportTicketsRecords, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this SportTicketsRecords to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for SportTicketsRecords
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an UpdateUserBalance. */
export interface IUpdateUserBalance {

    /** UpdateUserBalance afterAmount */
    afterAmount?: (google.protobuf.IDoubleValue|null);

    /** UpdateUserBalance afterUSDTAmount */
    afterUSDTAmount?: (google.protobuf.IDoubleValue|null);

    /** UpdateUserBalance errorCode */
    errorCode?: (google.protobuf.IInt32Value|null);
}

/** Represents an UpdateUserBalance. */
export class UpdateUserBalance implements IUpdateUserBalance {

    /**
     * Constructs a new UpdateUserBalance.
     * @param [properties] Properties to set
     */
    constructor(properties?: IUpdateUserBalance);

    /** UpdateUserBalance afterAmount. */
    public afterAmount?: (google.protobuf.IDoubleValue|null);

    /** UpdateUserBalance afterUSDTAmount. */
    public afterUSDTAmount?: (google.protobuf.IDoubleValue|null);

    /** UpdateUserBalance errorCode. */
    public errorCode?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new UpdateUserBalance instance using the specified properties.
     * @param [properties] Properties to set
     * @returns UpdateUserBalance instance
     */
    public static create(properties?: IUpdateUserBalance): UpdateUserBalance;

    /**
     * Encodes the specified UpdateUserBalance message. Does not implicitly {@link UpdateUserBalance.verify|verify} messages.
     * @param message UpdateUserBalance message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IUpdateUserBalance, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified UpdateUserBalance message, length delimited. Does not implicitly {@link UpdateUserBalance.verify|verify} messages.
     * @param message UpdateUserBalance message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IUpdateUserBalance, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an UpdateUserBalance message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns UpdateUserBalance
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): UpdateUserBalance;

    /**
     * Decodes an UpdateUserBalance message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns UpdateUserBalance
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): UpdateUserBalance;

    /**
     * Verifies an UpdateUserBalance message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an UpdateUserBalance message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns UpdateUserBalance
     */
    public static fromObject(object: { [k: string]: any }): UpdateUserBalance;

    /**
     * Creates a plain object from an UpdateUserBalance message. Also converts values to other types if specified.
     * @param message UpdateUserBalance
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: UpdateUserBalance, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this UpdateUserBalance to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for UpdateUserBalance
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a QuickSportTicketsRespMsgPO. */
export interface IQuickSportTicketsRespMsgPO {

    /** QuickSportTicketsRespMsgPO targetUserId */
    targetUserId?: (google.protobuf.IInt64Value|null);

    /** QuickSportTicketsRespMsgPO ticketsInfo */
    ticketsInfo?: (ISportTicketsRecords|null);
}

/** Represents a QuickSportTicketsRespMsgPO. */
export class QuickSportTicketsRespMsgPO implements IQuickSportTicketsRespMsgPO {

    /**
     * Constructs a new QuickSportTicketsRespMsgPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IQuickSportTicketsRespMsgPO);

    /** QuickSportTicketsRespMsgPO targetUserId. */
    public targetUserId?: (google.protobuf.IInt64Value|null);

    /** QuickSportTicketsRespMsgPO ticketsInfo. */
    public ticketsInfo?: (ISportTicketsRecords|null);

    /**
     * Creates a new QuickSportTicketsRespMsgPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns QuickSportTicketsRespMsgPO instance
     */
    public static create(properties?: IQuickSportTicketsRespMsgPO): QuickSportTicketsRespMsgPO;

    /**
     * Encodes the specified QuickSportTicketsRespMsgPO message. Does not implicitly {@link QuickSportTicketsRespMsgPO.verify|verify} messages.
     * @param message QuickSportTicketsRespMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IQuickSportTicketsRespMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified QuickSportTicketsRespMsgPO message, length delimited. Does not implicitly {@link QuickSportTicketsRespMsgPO.verify|verify} messages.
     * @param message QuickSportTicketsRespMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IQuickSportTicketsRespMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a QuickSportTicketsRespMsgPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns QuickSportTicketsRespMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuickSportTicketsRespMsgPO;

    /**
     * Decodes a QuickSportTicketsRespMsgPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns QuickSportTicketsRespMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuickSportTicketsRespMsgPO;

    /**
     * Verifies a QuickSportTicketsRespMsgPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a QuickSportTicketsRespMsgPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns QuickSportTicketsRespMsgPO
     */
    public static fromObject(object: { [k: string]: any }): QuickSportTicketsRespMsgPO;

    /**
     * Creates a plain object from a QuickSportTicketsRespMsgPO message. Also converts values to other types if specified.
     * @param message QuickSportTicketsRespMsgPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: QuickSportTicketsRespMsgPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this QuickSportTicketsRespMsgPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for QuickSportTicketsRespMsgPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a DeskSportNoticeMsg. */
export interface IDeskSportNoticeMsg {

    /** DeskSportNoticeMsg moreOneHoursStr */
    moreOneHoursStr?: (google.protobuf.IStringValue|null);

    /** DeskSportNoticeMsg lessOneHoursStr */
    lessOneHoursStr?: (google.protobuf.IStringValue|null);

    /** DeskSportNoticeMsg startStr */
    startStr?: (google.protobuf.IStringValue|null);

    /** DeskSportNoticeMsg sportStart */
    sportStart?: (google.protobuf.IInt64Value|null);

    /** DeskSportNoticeMsg cdTime */
    cdTime?: (google.protobuf.IInt32Value|null);

    /** DeskSportNoticeMsg showTimes */
    showTimes?: (google.protobuf.IInt32Value|null);
}

/** Represents a DeskSportNoticeMsg. */
export class DeskSportNoticeMsg implements IDeskSportNoticeMsg {

    /**
     * Constructs a new DeskSportNoticeMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IDeskSportNoticeMsg);

    /** DeskSportNoticeMsg moreOneHoursStr. */
    public moreOneHoursStr?: (google.protobuf.IStringValue|null);

    /** DeskSportNoticeMsg lessOneHoursStr. */
    public lessOneHoursStr?: (google.protobuf.IStringValue|null);

    /** DeskSportNoticeMsg startStr. */
    public startStr?: (google.protobuf.IStringValue|null);

    /** DeskSportNoticeMsg sportStart. */
    public sportStart?: (google.protobuf.IInt64Value|null);

    /** DeskSportNoticeMsg cdTime. */
    public cdTime?: (google.protobuf.IInt32Value|null);

    /** DeskSportNoticeMsg showTimes. */
    public showTimes?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new DeskSportNoticeMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns DeskSportNoticeMsg instance
     */
    public static create(properties?: IDeskSportNoticeMsg): DeskSportNoticeMsg;

    /**
     * Encodes the specified DeskSportNoticeMsg message. Does not implicitly {@link DeskSportNoticeMsg.verify|verify} messages.
     * @param message DeskSportNoticeMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IDeskSportNoticeMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified DeskSportNoticeMsg message, length delimited. Does not implicitly {@link DeskSportNoticeMsg.verify|verify} messages.
     * @param message DeskSportNoticeMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IDeskSportNoticeMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a DeskSportNoticeMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DeskSportNoticeMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): DeskSportNoticeMsg;

    /**
     * Decodes a DeskSportNoticeMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns DeskSportNoticeMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): DeskSportNoticeMsg;

    /**
     * Verifies a DeskSportNoticeMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a DeskSportNoticeMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns DeskSportNoticeMsg
     */
    public static fromObject(object: { [k: string]: any }): DeskSportNoticeMsg;

    /**
     * Creates a plain object from a DeskSportNoticeMsg message. Also converts values to other types if specified.
     * @param message DeskSportNoticeMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: DeskSportNoticeMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this DeskSportNoticeMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for DeskSportNoticeMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BaseDataPO. */
export interface IBaseDataPO {

    /** BaseDataPO data */
    data?: (google.protobuf.IStringValue|null);
}

/** Represents a BaseDataPO. */
export class BaseDataPO implements IBaseDataPO {

    /**
     * Constructs a new BaseDataPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBaseDataPO);

    /** BaseDataPO data. */
    public data?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new BaseDataPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BaseDataPO instance
     */
    public static create(properties?: IBaseDataPO): BaseDataPO;

    /**
     * Encodes the specified BaseDataPO message. Does not implicitly {@link BaseDataPO.verify|verify} messages.
     * @param message BaseDataPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBaseDataPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BaseDataPO message, length delimited. Does not implicitly {@link BaseDataPO.verify|verify} messages.
     * @param message BaseDataPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBaseDataPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BaseDataPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BaseDataPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BaseDataPO;

    /**
     * Decodes a BaseDataPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BaseDataPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BaseDataPO;

    /**
     * Verifies a BaseDataPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BaseDataPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BaseDataPO
     */
    public static fromObject(object: { [k: string]: any }): BaseDataPO;

    /**
     * Creates a plain object from a BaseDataPO message. Also converts values to other types if specified.
     * @param message BaseDataPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BaseDataPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BaseDataPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BaseDataPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a PayMatchInfoPO. */
export interface IPayMatchInfoPO {

    /** PayMatchInfoPO status */
    status?: (google.protobuf.IInt32Value|null);

    /** PayMatchInfoPO endTime */
    endTime?: (google.protobuf.IInt64Value|null);

    /** PayMatchInfoPO amount */
    amount?: (google.protobuf.IDoubleValue|null);

    /** PayMatchInfoPO type */
    type?: (google.protobuf.IInt32Value|null);

    /** PayMatchInfoPO coins */
    coins?: (google.protobuf.IInt32Value|null);

    /** PayMatchInfoPO isRead */
    isRead?: (google.protobuf.IInt32Value|null);

    /** PayMatchInfoPO read */
    read?: (google.protobuf.IBoolValue|null);
}

/** Represents a PayMatchInfoPO. */
export class PayMatchInfoPO implements IPayMatchInfoPO {

    /**
     * Constructs a new PayMatchInfoPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IPayMatchInfoPO);

    /** PayMatchInfoPO status. */
    public status?: (google.protobuf.IInt32Value|null);

    /** PayMatchInfoPO endTime. */
    public endTime?: (google.protobuf.IInt64Value|null);

    /** PayMatchInfoPO amount. */
    public amount?: (google.protobuf.IDoubleValue|null);

    /** PayMatchInfoPO type. */
    public type?: (google.protobuf.IInt32Value|null);

    /** PayMatchInfoPO coins. */
    public coins?: (google.protobuf.IInt32Value|null);

    /** PayMatchInfoPO isRead. */
    public isRead?: (google.protobuf.IInt32Value|null);

    /** PayMatchInfoPO read. */
    public read?: (google.protobuf.IBoolValue|null);

    /**
     * Creates a new PayMatchInfoPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns PayMatchInfoPO instance
     */
    public static create(properties?: IPayMatchInfoPO): PayMatchInfoPO;

    /**
     * Encodes the specified PayMatchInfoPO message. Does not implicitly {@link PayMatchInfoPO.verify|verify} messages.
     * @param message PayMatchInfoPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IPayMatchInfoPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified PayMatchInfoPO message, length delimited. Does not implicitly {@link PayMatchInfoPO.verify|verify} messages.
     * @param message PayMatchInfoPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IPayMatchInfoPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a PayMatchInfoPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PayMatchInfoPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): PayMatchInfoPO;

    /**
     * Decodes a PayMatchInfoPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns PayMatchInfoPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): PayMatchInfoPO;

    /**
     * Verifies a PayMatchInfoPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a PayMatchInfoPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns PayMatchInfoPO
     */
    public static fromObject(object: { [k: string]: any }): PayMatchInfoPO;

    /**
     * Creates a plain object from a PayMatchInfoPO message. Also converts values to other types if specified.
     * @param message PayMatchInfoPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: PayMatchInfoPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this PayMatchInfoPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for PayMatchInfoPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a RetraceScoreMsgPO. */
export interface IRetraceScoreMsgPO {

    /** RetraceScoreMsgPO scene */
    scene?: (number|null);

    /** RetraceScoreMsgPO type */
    type?: (number|null);

    /** RetraceScoreMsgPO score */
    score?: (number|null);

    /** RetraceScoreMsgPO totalScore */
    totalScore?: (number|null);

    /** RetraceScoreMsgPO keepScore */
    keepScore?: (google.protobuf.IInt32Value|null);

    /** RetraceScoreMsgPO minKeepScore */
    minKeepScore?: (google.protobuf.IInt32Value|null);
}

/** Represents a RetraceScoreMsgPO. */
export class RetraceScoreMsgPO implements IRetraceScoreMsgPO {

    /**
     * Constructs a new RetraceScoreMsgPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: IRetraceScoreMsgPO);

    /** RetraceScoreMsgPO scene. */
    public scene: number;

    /** RetraceScoreMsgPO type. */
    public type: number;

    /** RetraceScoreMsgPO score. */
    public score: number;

    /** RetraceScoreMsgPO totalScore. */
    public totalScore: number;

    /** RetraceScoreMsgPO keepScore. */
    public keepScore?: (google.protobuf.IInt32Value|null);

    /** RetraceScoreMsgPO minKeepScore. */
    public minKeepScore?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new RetraceScoreMsgPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns RetraceScoreMsgPO instance
     */
    public static create(properties?: IRetraceScoreMsgPO): RetraceScoreMsgPO;

    /**
     * Encodes the specified RetraceScoreMsgPO message. Does not implicitly {@link RetraceScoreMsgPO.verify|verify} messages.
     * @param message RetraceScoreMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IRetraceScoreMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified RetraceScoreMsgPO message, length delimited. Does not implicitly {@link RetraceScoreMsgPO.verify|verify} messages.
     * @param message RetraceScoreMsgPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IRetraceScoreMsgPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a RetraceScoreMsgPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RetraceScoreMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): RetraceScoreMsgPO;

    /**
     * Decodes a RetraceScoreMsgPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns RetraceScoreMsgPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): RetraceScoreMsgPO;

    /**
     * Verifies a RetraceScoreMsgPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a RetraceScoreMsgPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns RetraceScoreMsgPO
     */
    public static fromObject(object: { [k: string]: any }): RetraceScoreMsgPO;

    /**
     * Creates a plain object from a RetraceScoreMsgPO message. Also converts values to other types if specified.
     * @param message RetraceScoreMsgPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: RetraceScoreMsgPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this RetraceScoreMsgPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for RetraceScoreMsgPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BattleSquidGameMsg. */
export interface IBattleSquidGameMsg {

    /** BattleSquidGameMsg scene */
    scene?: (number|null);

    /** BattleSquidGameMsg users */
    users?: (google.protobuf.IInt64Value[]|null);

    /** BattleSquidGameMsg sett */
    sett?: (ISquidGameSettPO[]|null);

    /** BattleSquidGameMsg ext */
    ext?: (google.protobuf.IStringValue|null);
}

/** Represents a BattleSquidGameMsg. */
export class BattleSquidGameMsg implements IBattleSquidGameMsg {

    /**
     * Constructs a new BattleSquidGameMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBattleSquidGameMsg);

    /** BattleSquidGameMsg scene. */
    public scene: number;

    /** BattleSquidGameMsg users. */
    public users: google.protobuf.IInt64Value[];

    /** BattleSquidGameMsg sett. */
    public sett: ISquidGameSettPO[];

    /** BattleSquidGameMsg ext. */
    public ext?: (google.protobuf.IStringValue|null);

    /**
     * Creates a new BattleSquidGameMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BattleSquidGameMsg instance
     */
    public static create(properties?: IBattleSquidGameMsg): BattleSquidGameMsg;

    /**
     * Encodes the specified BattleSquidGameMsg message. Does not implicitly {@link BattleSquidGameMsg.verify|verify} messages.
     * @param message BattleSquidGameMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBattleSquidGameMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BattleSquidGameMsg message, length delimited. Does not implicitly {@link BattleSquidGameMsg.verify|verify} messages.
     * @param message BattleSquidGameMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBattleSquidGameMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BattleSquidGameMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BattleSquidGameMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BattleSquidGameMsg;

    /**
     * Decodes a BattleSquidGameMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BattleSquidGameMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BattleSquidGameMsg;

    /**
     * Verifies a BattleSquidGameMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BattleSquidGameMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BattleSquidGameMsg
     */
    public static fromObject(object: { [k: string]: any }): BattleSquidGameMsg;

    /**
     * Creates a plain object from a BattleSquidGameMsg message. Also converts values to other types if specified.
     * @param message BattleSquidGameMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BattleSquidGameMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BattleSquidGameMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BattleSquidGameMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a SquidGameSettPO. */
export interface ISquidGameSettPO {

    /** SquidGameSettPO userId */
    userId?: (number|Long|null);

    /** SquidGameSettPO addScore */
    addScore?: (number|null);

    /** SquidGameSettPO curScore */
    curScore?: (number|null);
}

/** Represents a SquidGameSettPO. */
export class SquidGameSettPO implements ISquidGameSettPO {

    /**
     * Constructs a new SquidGameSettPO.
     * @param [properties] Properties to set
     */
    constructor(properties?: ISquidGameSettPO);

    /** SquidGameSettPO userId. */
    public userId: (number|Long);

    /** SquidGameSettPO addScore. */
    public addScore: number;

    /** SquidGameSettPO curScore. */
    public curScore: number;

    /**
     * Creates a new SquidGameSettPO instance using the specified properties.
     * @param [properties] Properties to set
     * @returns SquidGameSettPO instance
     */
    public static create(properties?: ISquidGameSettPO): SquidGameSettPO;

    /**
     * Encodes the specified SquidGameSettPO message. Does not implicitly {@link SquidGameSettPO.verify|verify} messages.
     * @param message SquidGameSettPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ISquidGameSettPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified SquidGameSettPO message, length delimited. Does not implicitly {@link SquidGameSettPO.verify|verify} messages.
     * @param message SquidGameSettPO message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ISquidGameSettPO, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a SquidGameSettPO message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns SquidGameSettPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): SquidGameSettPO;

    /**
     * Decodes a SquidGameSettPO message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns SquidGameSettPO
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): SquidGameSettPO;

    /**
     * Verifies a SquidGameSettPO message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a SquidGameSettPO message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns SquidGameSettPO
     */
    public static fromObject(object: { [k: string]: any }): SquidGameSettPO;

    /**
     * Creates a plain object from a SquidGameSettPO message. Also converts values to other types if specified.
     * @param message SquidGameSettPO
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: SquidGameSettPO, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this SquidGameSettPO to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for SquidGameSettPO
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of a BeghintMsg. */
export interface IBeghintMsg {

    /** BeghintMsg status */
    status?: (google.protobuf.IInt32Value|null);

    /** BeghintMsg hintType */
    hintType?: (google.protobuf.IInt32Value|null);

    /** BeghintMsg luckpoint */
    luckpoint?: (google.protobuf.IInt32Value|null);

    /** BeghintMsg richpoint */
    richpoint?: (google.protobuf.IInt32Value|null);

    /** BeghintMsg hintMsg */
    hintMsg?: (google.protobuf.IStringValue|null);

    /** BeghintMsg nextBegtime */
    nextBegtime?: (google.protobuf.IInt64Value|null);

    /** BeghintMsg price */
    price?: (google.protobuf.IInt32Value|null);
}

/** Represents a BeghintMsg. */
export class BeghintMsg implements IBeghintMsg {

    /**
     * Constructs a new BeghintMsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: IBeghintMsg);

    /** BeghintMsg status. */
    public status?: (google.protobuf.IInt32Value|null);

    /** BeghintMsg hintType. */
    public hintType?: (google.protobuf.IInt32Value|null);

    /** BeghintMsg luckpoint. */
    public luckpoint?: (google.protobuf.IInt32Value|null);

    /** BeghintMsg richpoint. */
    public richpoint?: (google.protobuf.IInt32Value|null);

    /** BeghintMsg hintMsg. */
    public hintMsg?: (google.protobuf.IStringValue|null);

    /** BeghintMsg nextBegtime. */
    public nextBegtime?: (google.protobuf.IInt64Value|null);

    /** BeghintMsg price. */
    public price?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new BeghintMsg instance using the specified properties.
     * @param [properties] Properties to set
     * @returns BeghintMsg instance
     */
    public static create(properties?: IBeghintMsg): BeghintMsg;

    /**
     * Encodes the specified BeghintMsg message. Does not implicitly {@link BeghintMsg.verify|verify} messages.
     * @param message BeghintMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IBeghintMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified BeghintMsg message, length delimited. Does not implicitly {@link BeghintMsg.verify|verify} messages.
     * @param message BeghintMsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IBeghintMsg, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a BeghintMsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BeghintMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): BeghintMsg;

    /**
     * Decodes a BeghintMsg message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns BeghintMsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): BeghintMsg;

    /**
     * Verifies a BeghintMsg message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a BeghintMsg message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns BeghintMsg
     */
    public static fromObject(object: { [k: string]: any }): BeghintMsg;

    /**
     * Creates a plain object from a BeghintMsg message. Also converts values to other types if specified.
     * @param message BeghintMsg
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: BeghintMsg, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this BeghintMsg to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for BeghintMsg
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an InsuranceMsgNotify. */
export interface IInsuranceMsgNotify {

    /** 状态为0表示正常，其他则表示存在问题,1，不是这个时机 ，无需购买，2，不确定是哪个分池 3，没有outs 匹配的前提下不用购买，4，最大底池有牌力相当的两个人领先，不提供保险 ，5，最大底池中人数超过3人。不提供保险，6，未达到购买条件， 赔率低于0.1，7，保险命中outs， 8，保险未命中outs， 9，保险底池低于50不需要购买保险 */
    status?: (google.protobuf.IInt32Value|null);

    /** InsuranceMsgNotify alreadyInsurance */
    alreadyInsurance?: (google.protobuf.IInt32Value|null);

    /** InsuranceMsgNotify info */
    info?: (IInsuranceInfo|null);
}

/** 投保信息通知,客户端判断是否需要展示购买项，已经已经购买的选中效果 */
export class InsuranceMsgNotify implements IInsuranceMsgNotify {

    /**
     * Constructs a new InsuranceMsgNotify.
     * @param [properties] Properties to set
     */
    constructor(properties?: IInsuranceMsgNotify);

    /** 状态为0表示正常，其他则表示存在问题,1，不是这个时机 ，无需购买，2，不确定是哪个分池 3，没有outs 匹配的前提下不用购买，4，最大底池有牌力相当的两个人领先，不提供保险 ，5，最大底池中人数超过3人。不提供保险，6，未达到购买条件， 赔率低于0.1，7，保险命中outs， 8，保险未命中outs， 9，保险底池低于50不需要购买保险 */
    public status?: (google.protobuf.IInt32Value|null);

    /** InsuranceMsgNotify alreadyInsurance. */
    public alreadyInsurance?: (google.protobuf.IInt32Value|null);

    /** InsuranceMsgNotify info. */
    public info?: (IInsuranceInfo|null);

    /**
     * Creates a new InsuranceMsgNotify instance using the specified properties.
     * @param [properties] Properties to set
     * @returns InsuranceMsgNotify instance
     */
    public static create(properties?: IInsuranceMsgNotify): InsuranceMsgNotify;

    /**
     * Encodes the specified InsuranceMsgNotify message. Does not implicitly {@link InsuranceMsgNotify.verify|verify} messages.
     * @param message InsuranceMsgNotify message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IInsuranceMsgNotify, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified InsuranceMsgNotify message, length delimited. Does not implicitly {@link InsuranceMsgNotify.verify|verify} messages.
     * @param message InsuranceMsgNotify message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IInsuranceMsgNotify, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an InsuranceMsgNotify message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns InsuranceMsgNotify
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): InsuranceMsgNotify;

    /**
     * Decodes an InsuranceMsgNotify message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns InsuranceMsgNotify
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): InsuranceMsgNotify;

    /**
     * Verifies an InsuranceMsgNotify message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an InsuranceMsgNotify message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns InsuranceMsgNotify
     */
    public static fromObject(object: { [k: string]: any }): InsuranceMsgNotify;

    /**
     * Creates a plain object from an InsuranceMsgNotify message. Also converts values to other types if specified.
     * @param message InsuranceMsgNotify
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: InsuranceMsgNotify, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this InsuranceMsgNotify to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for InsuranceMsgNotify
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an InsuranceInfo. */
export interface IInsuranceInfo {

    /** InsuranceInfo insuranceId */
    insuranceId?: (google.protobuf.IInt64Value|null);

    /** InsuranceInfo buyType */
    buyType?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo maxInsurancePot */
    maxInsurancePot?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo canInsurancePot */
    canInsurancePot?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo betInsurance */
    betInsurance?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo insuranceOdds */
    insuranceOdds?: (google.protobuf.IDoubleValue|null);

    /** InsuranceInfo userIds */
    userIds?: (google.protobuf.IInt64Value[]|null);

    /** InsuranceInfo otherOutsNum */
    otherOutsNum?: (google.protobuf.IInt32Value[]|null);

    /** InsuranceInfo insuranceMoney */
    insuranceMoney?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo insuranceTarget */
    insuranceTarget?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo insuranceTax */
    insuranceTax?: (google.protobuf.IDoubleValue|null);

    /** InsuranceInfo turnInsuranceBuy */
    turnInsuranceBuy?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo cards */
    cards?: (IInsuranceCardType[]|null);

    /** InsuranceInfo nextOpTime */
    nextOpTime?: (google.protobuf.IInt64Value|null);

    /** InsuranceInfo totalOpTime */
    totalOpTime?: (google.protobuf.IInt64Value|null);

    /** InsuranceInfo prolongCfg */
    prolongCfg?: (IBattleProlongPO|null);

    /** InsuranceInfo insuranceScore */
    insuranceScore?: (google.protobuf.IInt32Value|null);
}

/** 投保基础信息 */
export class InsuranceInfo implements IInsuranceInfo {

    /**
     * Constructs a new InsuranceInfo.
     * @param [properties] Properties to set
     */
    constructor(properties?: IInsuranceInfo);

    /** InsuranceInfo insuranceId. */
    public insuranceId?: (google.protobuf.IInt64Value|null);

    /** InsuranceInfo buyType. */
    public buyType?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo maxInsurancePot. */
    public maxInsurancePot?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo canInsurancePot. */
    public canInsurancePot?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo betInsurance. */
    public betInsurance?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo insuranceOdds. */
    public insuranceOdds?: (google.protobuf.IDoubleValue|null);

    /** InsuranceInfo userIds. */
    public userIds: google.protobuf.IInt64Value[];

    /** InsuranceInfo otherOutsNum. */
    public otherOutsNum: google.protobuf.IInt32Value[];

    /** InsuranceInfo insuranceMoney. */
    public insuranceMoney?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo insuranceTarget. */
    public insuranceTarget?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo insuranceTax. */
    public insuranceTax?: (google.protobuf.IDoubleValue|null);

    /** InsuranceInfo turnInsuranceBuy. */
    public turnInsuranceBuy?: (google.protobuf.IInt32Value|null);

    /** InsuranceInfo cards. */
    public cards: IInsuranceCardType[];

    /** InsuranceInfo nextOpTime. */
    public nextOpTime?: (google.protobuf.IInt64Value|null);

    /** InsuranceInfo totalOpTime. */
    public totalOpTime?: (google.protobuf.IInt64Value|null);

    /** InsuranceInfo prolongCfg. */
    public prolongCfg?: (IBattleProlongPO|null);

    /** InsuranceInfo insuranceScore. */
    public insuranceScore?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new InsuranceInfo instance using the specified properties.
     * @param [properties] Properties to set
     * @returns InsuranceInfo instance
     */
    public static create(properties?: IInsuranceInfo): InsuranceInfo;

    /**
     * Encodes the specified InsuranceInfo message. Does not implicitly {@link InsuranceInfo.verify|verify} messages.
     * @param message InsuranceInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IInsuranceInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified InsuranceInfo message, length delimited. Does not implicitly {@link InsuranceInfo.verify|verify} messages.
     * @param message InsuranceInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IInsuranceInfo, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an InsuranceInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns InsuranceInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): InsuranceInfo;

    /**
     * Decodes an InsuranceInfo message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns InsuranceInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): InsuranceInfo;

    /**
     * Verifies an InsuranceInfo message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an InsuranceInfo message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns InsuranceInfo
     */
    public static fromObject(object: { [k: string]: any }): InsuranceInfo;

    /**
     * Creates a plain object from an InsuranceInfo message. Also converts values to other types if specified.
     * @param message InsuranceInfo
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: InsuranceInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this InsuranceInfo to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for InsuranceInfo
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Properties of an InsuranceCardType. */
export interface IInsuranceCardType {

    /** InsuranceCardType card */
    card?: (google.protobuf.IInt32Value|null);

    /** InsuranceCardType type */
    type?: (google.protobuf.IInt32Value|null);
}

/**
 * 卡牌类型枚举
 * 1.投保玩家手牌，2-5张
 * 2.其他玩家手牌，每个人 2-5张，按照玩家名称顺序显示
 * 3，弃牌玩家手牌，有out效果
 * 4，弃牌玩家手牌，无out效果
 * 5.公牌
 * 6.剩余牌 有out效果
 * 7。剩余牌 无out效果
 * 8.参与玩家手牌最大牌型（高亮牌）
 * 9.剩余牌 equal outs效果牌
 * 10.投保玩家授牌最大牌型 （高亮牌）
 */
export class InsuranceCardType implements IInsuranceCardType {

    /**
     * Constructs a new InsuranceCardType.
     * @param [properties] Properties to set
     */
    constructor(properties?: IInsuranceCardType);

    /** InsuranceCardType card. */
    public card?: (google.protobuf.IInt32Value|null);

    /** InsuranceCardType type. */
    public type?: (google.protobuf.IInt32Value|null);

    /**
     * Creates a new InsuranceCardType instance using the specified properties.
     * @param [properties] Properties to set
     * @returns InsuranceCardType instance
     */
    public static create(properties?: IInsuranceCardType): InsuranceCardType;

    /**
     * Encodes the specified InsuranceCardType message. Does not implicitly {@link InsuranceCardType.verify|verify} messages.
     * @param message InsuranceCardType message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IInsuranceCardType, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified InsuranceCardType message, length delimited. Does not implicitly {@link InsuranceCardType.verify|verify} messages.
     * @param message InsuranceCardType message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IInsuranceCardType, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an InsuranceCardType message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns InsuranceCardType
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): InsuranceCardType;

    /**
     * Decodes an InsuranceCardType message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns InsuranceCardType
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): InsuranceCardType;

    /**
     * Verifies an InsuranceCardType message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an InsuranceCardType message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns InsuranceCardType
     */
    public static fromObject(object: { [k: string]: any }): InsuranceCardType;

    /**
     * Creates a plain object from an InsuranceCardType message. Also converts values to other types if specified.
     * @param message InsuranceCardType
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: InsuranceCardType, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this InsuranceCardType to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for InsuranceCardType
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Namespace google. */
export namespace google {

    /** Namespace protobuf. */
    namespace protobuf {

        /** Properties of a DoubleValue. */
        interface IDoubleValue {

            /** DoubleValue value */
            value?: (number|null);
        }

        /** Represents a DoubleValue. */
        class DoubleValue implements IDoubleValue {

            /**
             * Constructs a new DoubleValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IDoubleValue);

            /** DoubleValue value. */
            public value: number;

            /**
             * Creates a new DoubleValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns DoubleValue instance
             */
            public static create(properties?: google.protobuf.IDoubleValue): google.protobuf.DoubleValue;

            /**
             * Encodes the specified DoubleValue message. Does not implicitly {@link google.protobuf.DoubleValue.verify|verify} messages.
             * @param message DoubleValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IDoubleValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified DoubleValue message, length delimited. Does not implicitly {@link google.protobuf.DoubleValue.verify|verify} messages.
             * @param message DoubleValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IDoubleValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a DoubleValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns DoubleValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DoubleValue;

            /**
             * Decodes a DoubleValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns DoubleValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.DoubleValue;

            /**
             * Verifies a DoubleValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a DoubleValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns DoubleValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.DoubleValue;

            /**
             * Creates a plain object from a DoubleValue message. Also converts values to other types if specified.
             * @param message DoubleValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.DoubleValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this DoubleValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for DoubleValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a FloatValue. */
        interface IFloatValue {

            /** FloatValue value */
            value?: (number|null);
        }

        /** Represents a FloatValue. */
        class FloatValue implements IFloatValue {

            /**
             * Constructs a new FloatValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFloatValue);

            /** FloatValue value. */
            public value: number;

            /**
             * Creates a new FloatValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns FloatValue instance
             */
            public static create(properties?: google.protobuf.IFloatValue): google.protobuf.FloatValue;

            /**
             * Encodes the specified FloatValue message. Does not implicitly {@link google.protobuf.FloatValue.verify|verify} messages.
             * @param message FloatValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFloatValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified FloatValue message, length delimited. Does not implicitly {@link google.protobuf.FloatValue.verify|verify} messages.
             * @param message FloatValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IFloatValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FloatValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FloatValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FloatValue;

            /**
             * Decodes a FloatValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns FloatValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.FloatValue;

            /**
             * Verifies a FloatValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FloatValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FloatValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FloatValue;

            /**
             * Creates a plain object from a FloatValue message. Also converts values to other types if specified.
             * @param message FloatValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FloatValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FloatValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FloatValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an Int64Value. */
        interface IInt64Value {

            /** Int64Value value */
            value?: (number|Long|null);
        }

        /** Represents an Int64Value. */
        class Int64Value implements IInt64Value {

            /**
             * Constructs a new Int64Value.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IInt64Value);

            /** Int64Value value. */
            public value: (number|Long);

            /**
             * Creates a new Int64Value instance using the specified properties.
             * @param [properties] Properties to set
             * @returns Int64Value instance
             */
            public static create(properties?: google.protobuf.IInt64Value): google.protobuf.Int64Value;

            /**
             * Encodes the specified Int64Value message. Does not implicitly {@link google.protobuf.Int64Value.verify|verify} messages.
             * @param message Int64Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IInt64Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified Int64Value message, length delimited. Does not implicitly {@link google.protobuf.Int64Value.verify|verify} messages.
             * @param message Int64Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IInt64Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an Int64Value message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Int64Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.Int64Value;

            /**
             * Decodes an Int64Value message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns Int64Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.Int64Value;

            /**
             * Verifies an Int64Value message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an Int64Value message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Int64Value
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.Int64Value;

            /**
             * Creates a plain object from an Int64Value message. Also converts values to other types if specified.
             * @param message Int64Value
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.Int64Value, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Int64Value to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for Int64Value
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a UInt64Value. */
        interface IUInt64Value {

            /** UInt64Value value */
            value?: (number|Long|null);
        }

        /** Represents a UInt64Value. */
        class UInt64Value implements IUInt64Value {

            /**
             * Constructs a new UInt64Value.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IUInt64Value);

            /** UInt64Value value. */
            public value: (number|Long);

            /**
             * Creates a new UInt64Value instance using the specified properties.
             * @param [properties] Properties to set
             * @returns UInt64Value instance
             */
            public static create(properties?: google.protobuf.IUInt64Value): google.protobuf.UInt64Value;

            /**
             * Encodes the specified UInt64Value message. Does not implicitly {@link google.protobuf.UInt64Value.verify|verify} messages.
             * @param message UInt64Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IUInt64Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified UInt64Value message, length delimited. Does not implicitly {@link google.protobuf.UInt64Value.verify|verify} messages.
             * @param message UInt64Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IUInt64Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a UInt64Value message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UInt64Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UInt64Value;

            /**
             * Decodes a UInt64Value message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns UInt64Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.UInt64Value;

            /**
             * Verifies a UInt64Value message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a UInt64Value message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UInt64Value
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.UInt64Value;

            /**
             * Creates a plain object from a UInt64Value message. Also converts values to other types if specified.
             * @param message UInt64Value
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.UInt64Value, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UInt64Value to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for UInt64Value
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an Int32Value. */
        interface IInt32Value {

            /** Int32Value value */
            value?: (number|null);
        }

        /** Represents an Int32Value. */
        class Int32Value implements IInt32Value {

            /**
             * Constructs a new Int32Value.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IInt32Value);

            /** Int32Value value. */
            public value: number;

            /**
             * Creates a new Int32Value instance using the specified properties.
             * @param [properties] Properties to set
             * @returns Int32Value instance
             */
            public static create(properties?: google.protobuf.IInt32Value): google.protobuf.Int32Value;

            /**
             * Encodes the specified Int32Value message. Does not implicitly {@link google.protobuf.Int32Value.verify|verify} messages.
             * @param message Int32Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IInt32Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified Int32Value message, length delimited. Does not implicitly {@link google.protobuf.Int32Value.verify|verify} messages.
             * @param message Int32Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IInt32Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an Int32Value message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Int32Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.Int32Value;

            /**
             * Decodes an Int32Value message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns Int32Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.Int32Value;

            /**
             * Verifies an Int32Value message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an Int32Value message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Int32Value
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.Int32Value;

            /**
             * Creates a plain object from an Int32Value message. Also converts values to other types if specified.
             * @param message Int32Value
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.Int32Value, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Int32Value to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for Int32Value
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a UInt32Value. */
        interface IUInt32Value {

            /** UInt32Value value */
            value?: (number|null);
        }

        /** Represents a UInt32Value. */
        class UInt32Value implements IUInt32Value {

            /**
             * Constructs a new UInt32Value.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IUInt32Value);

            /** UInt32Value value. */
            public value: number;

            /**
             * Creates a new UInt32Value instance using the specified properties.
             * @param [properties] Properties to set
             * @returns UInt32Value instance
             */
            public static create(properties?: google.protobuf.IUInt32Value): google.protobuf.UInt32Value;

            /**
             * Encodes the specified UInt32Value message. Does not implicitly {@link google.protobuf.UInt32Value.verify|verify} messages.
             * @param message UInt32Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IUInt32Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified UInt32Value message, length delimited. Does not implicitly {@link google.protobuf.UInt32Value.verify|verify} messages.
             * @param message UInt32Value message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IUInt32Value, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a UInt32Value message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UInt32Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UInt32Value;

            /**
             * Decodes a UInt32Value message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns UInt32Value
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.UInt32Value;

            /**
             * Verifies a UInt32Value message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a UInt32Value message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UInt32Value
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.UInt32Value;

            /**
             * Creates a plain object from a UInt32Value message. Also converts values to other types if specified.
             * @param message UInt32Value
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.UInt32Value, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UInt32Value to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for UInt32Value
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a BoolValue. */
        interface IBoolValue {

            /** BoolValue value */
            value?: (boolean|null);
        }

        /** Represents a BoolValue. */
        class BoolValue implements IBoolValue {

            /**
             * Constructs a new BoolValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IBoolValue);

            /** BoolValue value. */
            public value: boolean;

            /**
             * Creates a new BoolValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns BoolValue instance
             */
            public static create(properties?: google.protobuf.IBoolValue): google.protobuf.BoolValue;

            /**
             * Encodes the specified BoolValue message. Does not implicitly {@link google.protobuf.BoolValue.verify|verify} messages.
             * @param message BoolValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IBoolValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified BoolValue message, length delimited. Does not implicitly {@link google.protobuf.BoolValue.verify|verify} messages.
             * @param message BoolValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IBoolValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a BoolValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns BoolValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.BoolValue;

            /**
             * Decodes a BoolValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns BoolValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.BoolValue;

            /**
             * Verifies a BoolValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a BoolValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns BoolValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.BoolValue;

            /**
             * Creates a plain object from a BoolValue message. Also converts values to other types if specified.
             * @param message BoolValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.BoolValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this BoolValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for BoolValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a StringValue. */
        interface IStringValue {

            /** StringValue value */
            value?: (string|null);
        }

        /** Represents a StringValue. */
        class StringValue implements IStringValue {

            /**
             * Constructs a new StringValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IStringValue);

            /** StringValue value. */
            public value: string;

            /**
             * Creates a new StringValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns StringValue instance
             */
            public static create(properties?: google.protobuf.IStringValue): google.protobuf.StringValue;

            /**
             * Encodes the specified StringValue message. Does not implicitly {@link google.protobuf.StringValue.verify|verify} messages.
             * @param message StringValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IStringValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified StringValue message, length delimited. Does not implicitly {@link google.protobuf.StringValue.verify|verify} messages.
             * @param message StringValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IStringValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a StringValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns StringValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.StringValue;

            /**
             * Decodes a StringValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns StringValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.StringValue;

            /**
             * Verifies a StringValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a StringValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns StringValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.StringValue;

            /**
             * Creates a plain object from a StringValue message. Also converts values to other types if specified.
             * @param message StringValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.StringValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this StringValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for StringValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a BytesValue. */
        interface IBytesValue {

            /** BytesValue value */
            value?: (Uint8Array|null);
        }

        /** Represents a BytesValue. */
        class BytesValue implements IBytesValue {

            /**
             * Constructs a new BytesValue.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IBytesValue);

            /** BytesValue value. */
            public value: Uint8Array;

            /**
             * Creates a new BytesValue instance using the specified properties.
             * @param [properties] Properties to set
             * @returns BytesValue instance
             */
            public static create(properties?: google.protobuf.IBytesValue): google.protobuf.BytesValue;

            /**
             * Encodes the specified BytesValue message. Does not implicitly {@link google.protobuf.BytesValue.verify|verify} messages.
             * @param message BytesValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IBytesValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified BytesValue message, length delimited. Does not implicitly {@link google.protobuf.BytesValue.verify|verify} messages.
             * @param message BytesValue message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IBytesValue, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a BytesValue message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns BytesValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.BytesValue;

            /**
             * Decodes a BytesValue message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns BytesValue
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.BytesValue;

            /**
             * Verifies a BytesValue message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a BytesValue message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns BytesValue
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.BytesValue;

            /**
             * Creates a plain object from a BytesValue message. Also converts values to other types if specified.
             * @param message BytesValue
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.BytesValue, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this BytesValue to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for BytesValue
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }
    }
}
