import { logging as globalLogging } from 'shared';

export function createLogger(name: string) {
    return {
        log: (msg: string, data?: any) => globalLogging.info(`[${name}] ${msg}`, data),
        info: (msg: string, data?: any) => globalLogging.info(`[${name}] ${msg}`, data),
        warn: (msg: string, data?: any) => globalLogging.warn(`[${name}] ${msg}`, data),
        error: (msg: string, error: any, data?: any) => globalLogging.error(`[${name}] ${msg}`, error, data),

    };
}

export function setLoggingContext(context: Record<string, any>) {
    globalLogging.init({
        ...globalLogging['_ctx'],
        ...context,
    });
}
