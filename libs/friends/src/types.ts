import { PlatformType, PlatformUserData, UserData } from "shared";

export const MESSAGE_TYPES = {
	C_imMsg: "MsgTypeMsgPO",
	WP_raise: "BattleRaisePO",
	BattleSitMsg: "BattleSitMsg",
	C_checkSit:"BattleSitMsg",
	C_checkSitV2:"BattleSitMsg",
	C_sit:"BattleSitMsg",
	WP_openCard: "BattleOpenCardReq",
	WP_autoOpt: "BattleAutoOptPO",

	WP_addScore: "BattleScorePO",
	WP_addScoreOtherPay: "BattleScorePO",

	WP_chooseDouble: "BattleDoubleChooseReq",

	C_emoji: "EmojiMsgPO",
	C_diamondSend: "DiamondSendMsgPO",
	WP_seeComCard: "ForceStandReq",
	C_forceStand: "ForceStandReq",
	WP_forceSeeCard: "LookComCardsReq",
	C_shieldUserMsg: "ShieldUserMsgReq",
	C_redPack: "OptTargetPB",
	WP_guessHand: "GuessHandBetPO",
	WP_retraceScore: "RetraceScorePO",
	WP_dealNotify: "BattleDealCardPOList",
	C_updateRoomNotify: "BattleRoomMsg",
	C_updateRoomUserNotify: "BattleRoomMsg",
	C_updateUserInfoNotify: "BattleRoomUserMsg",
	WP_bankerChangeNotify: "BattleBankChangeMsg",
	WP_actionNotify: "BattleActionMsg",
	WP_userOptNotify: "BattleUserOptMsg",
	WP_roundChangeNotify: "BattleRoundChangeMsg",
	WP_addScoreStatusNotify: "BattleAddScoreStatusResp",
	WP_playResultNotify: "BattleThanResp",
	WP_prolongNotify: "BattleProlongMsg",
	C_imMsgNotify: "ChatMsgPO",
	C_emojiNotify: "ChatMsgPO",
	C_diamondSendNotify: "DiamondRespMsgPO",
	WP_userChooseDoubleNotify: "BattleSendDoubleOptMsg",
	WP_userCSDResultNotify: "BattleCSDResultMsg",
	WP_openSendDoubleNotify: "AllinOpenCardMsg",
	WP_sendDoubleThanResultNotify: "BattleDoubleThanResultMsg",
	WP_showCardNotify: "BattleDealCardPO",
	WP_openCardNotify: "BattleOpenCardMsg",
	WP_openCardByAllinNotify: "AllinOpenCardMsg",
	AM_sendGameMsg: "MessageDoorPO",
	C_playLogNotify: "RoomPlayRecordOneHand",
	WP_seeComCardNotify: "LookComCardsMsg",
	C_updateRoomDetail: "BattleRoomDetailResp",
	WP_waitHandsNotify: "WaitHandResp",
	WP_raiseBlind: "RaiseBlindMsg",
	WP_forceSeeCardNotify: "ForceSeeCardResp",
	C_marqueeMsgNotify: "MarqueeMsgResp",
	C_request: "BaseResp",
	WP_updateUserProfileNotify: "BattleUserProfileResp",
	C_mttStartNotify: "MttStartMsg",
	C_starGame: "StarGameMsg",
	WP_updateDiamondBalanceNotify: "UpdateUserDiamondBalance",
	C_redPackNotify: "RedPackMsg",
	C_quickSportTicketsNotify: "QuickSportTicketsRespMsgPO",
	WP_deskSportNotify: "DeskSportNoticeMsg",
	C_updateUserBalance: "UpdateUserBalance",
	C_checkSitNotify: "BaseDataPO",
	WP_guessHandBetNotify: "BaseDataPO",
	WP_guessHandSettleNotify: "BaseDataPO",
	C_payMatchNotify: "PayMatchInfoPO",
	WP_retraceScoreNotify: "RetraceScoreMsgPO",
	C_closeRoomNotify: undefined,
	C_joinRoomNotify: "BattleRoomMsg",
	WP_beghintMsgUpdate: undefined,
	WP_lookUserJoinExitNotify: "BattleCSDResultMsg",
	C_cleanNotify: undefined,
}

export enum POKER_ACTIONS {
    FOLD = "FOLD",
    CALL = "CALL",
    CHECK = "CHECK",
    RAISE = "RAISE",
    ALL_IN = "ALL_IN"
}

export class GameError extends Error {
    errorCode?: number;
    constructor(message: string, errorCode?: number) {
        super(message);
        this.name = 'GameError';
        this.errorCode = errorCode;
        Object.setPrototypeOf(this, GameError.prototype);
    }
}

export class User implements UserData {
    userId: number;
    username: string;
    token: string;
    aesKey?: string;
    gameAesKey?: string;
    password: string;
    deviceId: string;
	platformId: PlatformType;
	countryCode?: string;

    constructor(userId, username, password) {
        this.userId = userId;
        this.username = username;
        this.password = password;
    }
}

export interface CustomWebsocketResponse {
	errorCode: number,
	errMsg: string,
	sysTime: string,
	msgType: string | null,
	msgBody: any,
	originOpt: string,
	callbackId: string,
	optHandle: any,
	protoType: string | null,
	newToken: string | null,
}
