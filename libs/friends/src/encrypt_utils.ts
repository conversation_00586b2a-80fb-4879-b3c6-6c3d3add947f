import CryptoJS from 'crypto-js';

const u8arrayEncoder = {
    stringify: function (a) {
        var e, f, b = a.words, c = a.sigBytes, d = new Uint8Array(c);
        for (e = 0; c > e; e++)f = 255 & b[e >>> 2] >>> 24 - 8 * (e % 4), d[e] = f; return d
    },
    parse: function (a) {
        var d, b = a.length, c = [];
        for (d = 0; b > d; d++)c[d >>> 2] |= (255 & a[d]) << 24 - 8 * (d % 4);
        return CryptoJS.lib.WordArray.create(c, b)
    }
};

function convertWordArrayToUint8Array(a) {
    let arrayOfWords = a.hasOwnProperty("words") ? a.words : [];
    let length = a.hasOwnProperty("sigBytes") ? a.sigBytes : arrayOfWords.length * 4;
    let uInt8Array = new Uint8Array(length), index = 0, word;
    for (let i = 0; i < length; i++) {
        word = arrayOfWords[i];
        uInt8Array[index++] = word >> 24;
        uInt8Array[index++] = (word >> 16) & 255;
        uInt8Array[index++] = (word >> 8) & 255;
        uInt8Array[index++] = word & 255;
    }
    return uInt8Array;
}

export function decryptAESBytes(contentBytes: Buffer, sKey: string): Uint8Array {
    let u8array = new Uint8Array(contentBytes);
    let data = u8arrayEncoder.parse(u8array).toString(CryptoJS.enc.Base64);
    let realKey = CryptoJS.SHA1(CryptoJS.SHA1(sKey)).toString().substring(0, 32);
    let key = CryptoJS.enc.Hex.parse(realKey);
    let decrypted = CryptoJS.AES.decrypt(data, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });

    return convertWordArrayToUint8Array(decrypted);
}
