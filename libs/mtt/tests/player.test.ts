import { mock, test } from 'node:test';
import assert from 'node:assert';
import { GamePlayer, WorldPlayer } from '../src/player';
import { commonProto } from '../src/mtt/pb/commonProto';
import { holdem } from '../src/mtt/pb/holdem';
import { CONNECTION_STATUS } from '../src/enums';
import { MttConfigType } from 'shared';

class FakeWebSocket {
	handlers = {};
	constructor(url: string) {
		// console.log(`FakeWebSocket created with URL: ${url}`);
	}
	addMessageHandler(id: string, handler: (msg: any) => void) {
		// console.log(`FakeWebSocket addMessageHandler called with id: ${id}`);
		this.handlers[id] = handler;
		// console.log({ handlers: this.handlers });
	}
	removeMessageHandler(id: string, handler?: (msg: any) => void) {
		// console.log(`FakeWebSocket removeMessage<PERSON>and<PERSON> called with id: ${id}`);
		this.handlers[id] = undefined;
	}
	connect() {
		this.callHandler(-2)
	}
	callHandler = (id, msg?: any) => {
		// console.log(`FakeWebSocket callHandler called with id: ${id}, msg:`, msg);
		this.handlers[id](msg);
	}
	send() {}
	keepPing() {}
	forceClose() {}
}


test('BasicPlayer should connect and disconnect', () => {
	mock.method(WorldPlayer.prototype, 'createWebsocket', function () {
		console.log('Mocked createWebsocket called');
		this.websocket = new FakeWebSocket('ws://fake-url');
	});

	const player = new WorldPlayer('myPlayerId', 'mttId', 0, {} as MttConfigType);

	player.connect();

	assert.equal(player.connectionStatus, 1);

	player.disconnect();
	assert.equal(player.connectionStatus, 4);

	// assert.equal(mockOpen.mock.calls.length, 1);
});

test('WorldPlayer should try to login after', async () => {
	mock.method(WorldPlayer.prototype, 'createWebsocket', function () {
		console.log('Mocked createWebsocket called');
		this.websocket = new FakeWebSocket('ws://fake-url');
	});

	const player = new WorldPlayer({ token: 'myToken' }, 'tournament-1', 0, {} as MttConfigType);

	const mockSend = mock.fn();

	mock.method(player.websocket, 'send', mockSend);
	player.connect();

	assert.equal(mockSend.mock.calls[0].arguments[0], commonProto.SocketMessageId.Secure_Token_Check);
	assert.equal(player.connectionStatus, 1);

	(player.websocket as unknown as FakeWebSocket).callHandler.call(player, commonProto.SocketMessageId.User_Login_Response, {
		ErrorCode: 1,
		UserData: {
			Id: 'test-user-id',
		},
		Token: '0',
	});

	assert.equal(player.connectionStatus, 0);

	(player.websocket as unknown as FakeWebSocket).callHandler.call(player, commonProto.SocketMessageId.User_Login_Response, {
		ErrorCode: 0,
		UserData: {
			Id: 'test-user-id'
		},
		Token: 'myToken',
		JoinedTournaments: [],
	});

	assert.equal(player.dataManager.token, 'myToken');
	assert.equal(player.tournamentJoinStatus, 0);

	(player.websocket as unknown as FakeWebSocket).callHandler.call(player, commonProto.SocketMessageId.User_Login_Response, {
		ErrorCode: 0,
		UserData: 'test-user-id',
		Token: 'myToken',
		JoinedTournaments: [{
			TournamentId: 'tournament-1',
			JoinStatus: 666,
		}],
	});

	assert.equal(player.tournamentJoinStatus, 666);

	mock.method(player.httpApis, 'requestMttPlayerSignUp', function (inputData) {
		console.log('Mocked requestMttPlayerSignUp called with inputData:', inputData);
		return {
			ErrorCode: 0,
			PlayerId: 'test-player-id',
			ExternalErrorCode: 1,
			tournamentId: 'tournament-1',
		};
	});

	await player.signup();
	assert.equal(player.tournamentJoinStatus, 1);

	player.disconnect()

});


test('GamePlayer', async () => {
	mock.method(GamePlayer.prototype, 'createWebsocket', function () {
		console.log('Mocked createWebsocket called');
		this.websocket = new FakeWebSocket('ws://fake-url');
	});

	const player = new GamePlayer({ token: 1, userData: { Id: 42 } }, 'tournament-1', {} as MttConfigType);

	const mockSend = mock.fn();

	mock.method(player.websocket, 'send', mockSend);
	player.connect();

	assert.equal(mockSend.mock.calls[0].arguments[1].hideHole, false);

	(player.websocket as unknown as FakeWebSocket).callHandler.call(player, holdem.MessageId.UserTokenRes, {});

	assert.equal(player.connectionStatus, CONNECTION_STATUS.LOGGED_IN)
});
