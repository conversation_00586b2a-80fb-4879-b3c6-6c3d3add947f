import { commonProto } from './mtt/pb/commonProto';
import { holdem } from './mtt/pb/holdem';
import { mttPro } from './mtt/pb/mtt';
import { CustomWebsocket } from './customWebsocket';
import { WEBSOCKET_TYPE, WEBSOCKET_EVENT_ID, CONNECTION_STATUS } from './enums';
import { UnrecoverableError } from 'bullmq';
import { logging, MttConfigType } from 'shared';

import { HttpApis } from './httpApis';

const reconnectInterval = 3000;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 100;

interface DataManager {
    userData?: commonProto.IUserData;
    token: string;
}

class BasicPlayer {
    websocket: CustomWebsocket;
    connectionStatus: CONNECTION_STATUS = CONNECTION_STATUS.NONE;
    dataManager: DataManager;
    tag = '';
    signupTimeout: NodeJS.Timeout | undefined;
    onConnectionFailed: (msg: any) => void = () => { };

    constructor(dataManager: DataManager, type: WEBSOCKET_TYPE, urlConfig: MttConfigType, proxyUrl?: string) {
        this.dataManager = dataManager;
        this.createWebsocket(type, urlConfig, proxyUrl);
        this.bindMessageHandler();
    }

    // separate function so we can mock it in tests
    createWebsocket(type: WEBSOCKET_TYPE, urlConfig: MttConfigType, proxyUrl?: string) {
        const url = type === WEBSOCKET_TYPE.WORLD ?
            `${urlConfig.mttWorld}/world` :
            `${urlConfig.mttGame}/mtt/holdem`;
        this.websocket = new CustomWebsocket(type, url, proxyUrl);
    }

    connect = () => {
        logging.info(`${this.tag} player try to connect`);
        this.websocket.connect();
    };

    disconnect() {
        logging.info(`${this.tag} player disconnect`);
        this.unbindMessageHandler();
        this.connectionStatus = CONNECTION_STATUS.STOP;
        if (this.websocket) {
            this.websocket.forceClose();
        }
    }

    onConnected = () => {
        logging.info(`${this.tag} player onConnected`);
        this.connectionStatus = CONNECTION_STATUS.CONNECTED;
        this.requestUserLogin();

        this.websocket.keepPing();
    }

    onDisconnected = (msg) => {
        logging.info(`${this.tag} player disconnected`, msg);

        if (this.connectionStatus !== CONNECTION_STATUS.STOP) {
            reconnectAttempts++;
            if (reconnectAttempts > MAX_RECONNECT_ATTEMPTS) {
                logging.error(`${this.tag} player reached max reconnect attempts (${MAX_RECONNECT_ATTEMPTS}), stopping reconnect.`, null);
                this.disconnect();
                this.onConnectionFailed("Max reconnect attempts reached");
            }
            setTimeout(() => {
                logging.info(`${this.tag} player reconnecting after disconnect...`);
                this.connect();
            }, reconnectInterval * reconnectAttempts);
            this.connectionStatus = CONNECTION_STATUS.NONE;
        } else {
            logging.info(`${this.tag} player connection stopped, not reconnecting.`);
        }
    }

    requestUserLogin() { }

    bindMessageHandler = () => {
        this.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED, this.onConnected.bind(this));
        this.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CLOSE, this.onDisconnected.bind(this));
    }

    unbindMessageHandler = () => {
        this.websocket.removeMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED);
        this.websocket.removeMessageHandler(WEBSOCKET_EVENT_ID.ON_CLOSE);
    }
}

export class WorldPlayer extends BasicPlayer {
    gamePlayer;
    tournamentStatus = 0;
    tournamentJoinStatus = 0;
    mttId;
    ticketId;
    tournamentStatusInterval;
    httpApis;
    userId: number;
    onLoggedIn: (userId: number, errorCode: commonProto.ErrorCode) => void = () => { };
    constructor(
        dataManager,
        mttId,
        ticketId = 0,
        urlConfig: MttConfigType,
        proxyUrl?: string,
    ) {
        super(dataManager, WEBSOCKET_TYPE.WORLD, urlConfig, proxyUrl);
        this.tag = 'world';
        this.mttId = mttId;
        this.ticketId = ticketId;
        this.httpApis = new HttpApis(proxyUrl, urlConfig.mttApi);
        this.bindWorldMessageHandler();
    }

    disconnect() {
        this.unbindWorldMessageHandler();
        super.disconnect();
        clearTimeout(this.signupTimeout);
        clearInterval(this.tournamentStatusInterval);
    }

    async signup(onError = (_) => { }) {
        // await this.getTournamentStatus();
        if (this.tournamentJoinStatus === 1) {
            logging.info(`Already signed up for tournament ${this.mttId}`);
            return;
        }
        let inputData = {
            UserId: this.dataManager?.userData?.Id,
            UserToken: this.dataManager.token,
            TournamentId: this.mttId,
            TicketId: this.ticketId,
            PlatForm: commonProto.PLATFORM.TRIBAL_PIONEER,
            RegGoldType: 0,
        };
        logging.info('requestMttPlayerSignUp req', inputData);
        try {
            const data: commonProto.IMttPlayerSignupResponse = await this.httpApis.requestMttPlayerSignUp(inputData);
            logging.info('requestMttPlayerSignUp res', data);
            if (!data) {
                throw new UnrecoverableError('requestMttPlayerSignUp returned no data');
            }
            if (!data?.ErrorCode) {
                logging.info(`Signed up for tournament ${this.mttId}`);
                this.tournamentJoinStatus = 1;
                return;
            } else {
                if (data?.ErrorCode == commonProto.ErrorCode.Mtt_Timeout) {
                    clearTimeout(this.signupTimeout);
                    this.signupTimeout = setTimeout(() => this.signup(onError), reconnectInterval);

                } else if (data?.ErrorCode == commonProto.ErrorCode.Mtt_Tournament_Preparing) {
                    clearTimeout(this.signupTimeout);
                    this.signupTimeout = setTimeout(() => this.signup(onError), 60000);

                } else if (data?.ErrorCode == commonProto.ErrorCode.User_Operate_Frequent) {
                    clearTimeout(this.signupTimeout);
                    this.signupTimeout = setTimeout(() => this.signup(onError), 5000);

                } else if (data?.ErrorCode == commonProto.ErrorCode.Mtt_Tournament_Signup_Stopped) {
                    throw new UnrecoverableError('Sign up stopped');

                } else if (data?.ErrorCode == commonProto.ErrorCode.GOLD_INSUFFICENT) {
                    throw new UnrecoverableError('Gold insufficient');

                } else if (data?.ErrorCode == commonProto.ErrorCode.Mtt_Player_Already_SignUp) {
                    // already signed up
                    this.tournamentJoinStatus = 1;
                    logging.info(`Already signed up for tournament ${this.mttId}`);
                    return

                } else {
                    onError(new Error(`requestMttPlayerSignUp error: ${commonProto.ErrorCode[data.ErrorCode] ?? data.ErrorCode}`));
                }
            }
        } catch (err) {
            if (err instanceof UnrecoverableError) {
                throw err;
            }
            clearTimeout(this.signupTimeout);
            this.signupTimeout = setTimeout(() => this.signup(onError), reconnectInterval);
        }
    };

    async reenterTournament() {
        const data: commonProto.IMttReenterResponse = await this.httpApis.requestMttReenter(
            this.dataManager.token,
            this.mttId,
            this.dataManager.userData?.Id,
            this.ticketId
        );
        logging.info('requestMttReenter response', data);
        if (data && data.ErrorCode) {
            throw new UnrecoverableError(`Reenter tournament error: ${commonProto.ErrorCode[data.ErrorCode] ?? data.ErrorCode}`);
        }
        logging.info(`Reentered tournament ${this.mttId}`);
        this.tournamentJoinStatus = 1;
    }

    onUserLoginResponse = (msg) => {
        if (!msg.ErrorCode) {
            if (this.websocket) {
                this.dataManager.userData = msg.UserData;
                this.dataManager.token = msg.Token;
                this.connectionStatus = CONNECTION_STATUS.LOGGED_IN;
            }
            logging.setUserId(msg.UserData.Id);
            logging.info('WorldPlayer onUserLoginResponse', { ...msg, Token: '***' });

            this.tournamentJoinStatus = msg.JoinedTournaments.find(t => t.TournamentId === this.mttId)?.JoinStatus || 0;

            this.onLoggedIn(msg.UserData.Id, null);
        } else {
            logging.warn('onUserLoginResponse error', msg);
            this.connectionStatus = CONNECTION_STATUS.NONE;
            this.onLoggedIn(null, msg.ErrorCode);
        }
    }

    async getTournamentStatus() {
        if (this.dataManager) {
            try {
                const data = await this.httpApis.requestMttTournamentStatus(
                    this.dataManager.token,
                    this.mttId
                );
                logging.info('requestMttTournamentStatus response', data);
                this.tournamentJoinStatus = data.Status;
            } catch (err) {
                logging.error('requestMttTournamentStatus error', err);
            }
        }
    };

    requestUserLogin() {
        logging.info('WorldPlayer requestUserLogin', this.dataManager.userData?.Id);
        if (this.dataManager.token) {
            this.websocket.send(commonProto.SocketMessageId.Secure_Token_Check, {
                Token: this.dataManager.token,
                device: 'web',
                environment: '',
            });
        }
    }


    bindWorldMessageHandler = () => {
        this.websocket.addMessageHandler(commonProto.SocketMessageId.Secure_Token_Error_Response, this.onUserLoginResponse.bind(this))
        this.websocket.addMessageHandler(commonProto.SocketMessageId.User_Login_Response, this.onUserLoginResponse.bind(this));
    }

    unbindWorldMessageHandler = () => {
        this.websocket.removeMessageHandler(commonProto.SocketMessageId.User_Login_Response);
    }
}

export class GamePlayer extends BasicPlayer {
    ids = { ...holdem.MessageId, ...mttPro.MessageId };
    roomId = 0;

    mttId;
    onTournamentInfo: (msg: mttPro.IMttRealTimeRecordRes) => void;

    constructor(dataManager, mttId, urlConfig, proxyUrl?: string) {
        super(dataManager, WEBSOCKET_TYPE.GAME, urlConfig, proxyUrl);
        this.tag = 'game';
        this.mttId = mttId;
        this.bindGameMessageHandler();
    }

    disconnect() {
        this.unbindGameMessageHandler();
        super.disconnect();
    }

    requestTournamentInfo() {
        logging.info('GamePlayer requestTournamentInfo', this.mttId);
        this.websocket.send(mttPro.MessageId.MttRealTimeRecordReq, {
            mttId: this.mttId,
            roomId: this.roomId,
            fullData: true,
        });
    }

    requestUserLogin() {
        logging.info('GamePlayer requestUserLogin', this.dataManager.userData?.Id);
        if (this.dataManager.token) {
            this.websocket.send(this.ids.UserTokenReq, {
                token: this.dataManager.token,
                userId: this.dataManager.userData?.Id,
                hideHole: false,
            });
        }
    }

    MTTEnterRoom = (mttId: number) => {
        logging.info('GamePlayer MTTEnterRoom', mttId);
        if (this.websocket) {
            this.websocket.send(this.ids.MttEnterGameReq, { mttId });
        }
    };

    Action = (action, coin) => {
        logging
            .withTag('REQUEST_ACTION')
            .info(
                `ActionReq action: ${action}, coin: ${coin}, roomId: ${this.roomId}`,
            );
        if (this.websocket) {
            this.websocket.send(this.ids.ActionReq, { roomId: this.roomId, action, coin });
        }
    };

    private onRoomSnapshotMsg = (msg: holdem.IRoomSnapshotMsg) => {
        logging.info('GamePlayer onRoomSnapshotMsg', this.dataManager);
        if (msg.mttId == this.mttId) {
            this.roomId = msg.roomId;
            logging.setRoomId(this.roomId);
            this.cancelAutoPlay();
        }
    }

    private onUserTokenRes = (msg) => {
        logging.info('GamePlayer onUserTokenRes');
        if (!msg.code) {
            this.connectionStatus = CONNECTION_STATUS.LOGGED_IN;
        }
    };


    private onAutoPlayMsg = (msg) => {
        logging.info('onAutoPlayMsg', msg);
        if (msg.roomId === this.roomId && msg.userId === this.dataManager.userData?.Id && msg.autoPlay === true) {
            this.cancelAutoPlay();
        }
    };

    private onCancelAutoPlayRes = (msg: holdem.ICancelAutoPlayRes) => {
        logging.info('onCancelAutoPlayRes', msg);
        if (this.roomId === msg.roomId && msg.code) {
            throw new Error(`CancelAutoPlay error: ${holdem.Code[msg.code] ?? msg.code}`);
        }
    };

    private cancelAutoPlay() {
        if (this.websocket) {
            this.websocket.send(this.ids.CancelAutoPlayReq, { roomId: this.roomId });
        }
    };

    private onPlayerLeaveMsg = (msg) => {
        if (msg.userId === this.dataManager.userData?.Id && this.roomId === msg.roomId) {
            logging.info('GamePlayer onPlayerLeaveMsg', msg);
        }
    };

    private onMttRealTimeRecordRes = (msg: mttPro.IMttRealTimeRecordRes) => {
        logging.info('GamePlayer onMttRealTimeRecordRes', msg);
        this.onTournamentInfo(msg);
    }

    bindGameMessageHandler = () => {
        this.websocket.addMessageHandler(this.ids.UserTokenRes, this.onUserTokenRes.bind(this));
        this.websocket.addMessageHandler(this.ids.RoomSnapshotMsg, this.onRoomSnapshotMsg.bind(this));
        this.websocket.addMessageHandler(this.ids.AutoPlayMsg, this.onAutoPlayMsg.bind(this));
        this.websocket.addMessageHandler(this.ids.CancelAutoPlayRes, this.onCancelAutoPlayRes.bind(this));
        this.websocket.addMessageHandler(this.ids.PlayerLeaveMsg, this.onPlayerLeaveMsg.bind(this));
        this.websocket.addMessageHandler(this.ids.MttRealTimeRecordRes, this.onMttRealTimeRecordRes.bind(this));
    }

    unbindGameMessageHandler = () => {
        this.websocket.removeMessageHandler(this.ids.UserTokenRes);
        this.websocket.removeMessageHandler(this.ids.RoomSnapshotMsg);
        this.websocket.removeMessageHandler(this.ids.AutoPlayMsg);
        this.websocket.removeMessageHandler(this.ids.CancelAutoPlayRes);
        this.websocket.removeMessageHandler(this.ids.PlayerLeaveMsg);
        this.websocket.removeMessageHandler(this.ids.MttRealTimeRecordRes);
    }
}
