import { UnrecoverableError } from 'bullmq';

import {
    DelayService,
    fetchStrategy,
    GAME_PHASE,
    GameMode,
    GamePlayer as StrategyPlayerData,
    GameStateInterface,
    JobDataHandler,
    logging,
    MttConfigType,
    UserStatus,
} from 'shared';

import { CONNECTION_STATUS } from './enums';
import { HttpApis } from './httpApis';
import { GamePlayer, WorldPlayer } from './player';
import { commonProto } from './mtt/pb/commonProto';
import { holdem } from './mtt/pb/holdem';
import { mttPro } from './mtt/pb/mtt';
import { processStrategy } from './strategy';

const sleep = (duration: number): Promise<void> => new Promise((resolve) => setTimeout(resolve, duration));

export async function signupAndPlay(
    token: string,
    mttId: number,
    ticketId: number,
    updateProgress: JobDataHandler,
    fetchTournamentDetails: Function,
    urlConfig: MttConfigType,
    proxyUrl?: string,
    profileName?: string,
): Promise<void> {
    logging.setTournamentId(mttId);

    let resolve, reject;
    const gamePromise = new Promise<void>((res, rej) => {
        resolve = res;
        reject = rej;
    });

    const onError = (error) => {
        reject(error);
        cleanup();
    };
    const finish = () => {
        resolve();
        cleanup();
    };

    const delayService = new DelayService(GameMode.NORMAL);
    const httpApis = new HttpApis(proxyUrl, urlConfig.mttApi);

    let tournamentConnectTimeout = null;
    let tournamentStatusTimeout = null;

    let dataManager = { token: token };

    let gamePlayer = new GamePlayer(dataManager, mttId, urlConfig, proxyUrl);
    let worldPlayer = new WorldPlayer(dataManager, mttId, ticketId, urlConfig, proxyUrl);

    let roomId = 0;
    let userId = -1;
    let seatNum = -1;

    let tournament: commonProto.IMttTournamentDetail = null;
    let payoutStructure = [];
    let tournamentPrizePool = 0;
    let tournamentPlayers = [];
    let tournamentPlayersSize = 0;

    let state: GameStateInterface = {
        gameuuid: '',
        game_type: 'nlhe',
        game_mode_code: GameMode.NORMAL,
        roomid: mttId.toString(),
        big_blind: 0,
        ante: 0,
        actions: { entries: [] },
        players: [],
        dealer_seat: -1,
        sb_seat: -1,
        bb_seat: -1,
        straddle_seat: -1, // straddle seat is not taken into account for most of the logic
    };
    let pot = 0;
    let currentLevel = 1;

    // Cards in your hand
    let holeCards = [];
    // Common cards on the board
    let boardCards = [];
    // Players that were forced to all-in
    let allinedPlayers = new Set();
    // Limit players actions when some actions are missing
    let limitedGameMode = false;

    let stats = null;

    let checkAndConnectToTournament = async (userId) => {
        const data = await httpApis.requestMttMultiTable(dataManager.token, userId);
        logging.info('checkAndConnectToTournament res', data);
        const isTournamentReady = data.UserGameInfo.some(
            (info: commonProto.IMtt_User_Current_Game_Info) =>
                info.SngMttLevelId === mttId &&
                (info.MttTournamentStatus > 0 || info.MttTournamentIsPreparing),
        );
        if (isTournamentReady) {
            clearInterval(tournamentConnectTimeout);
            gamePlayer.connect();
        }
        return isTournamentReady;
    };

    let onUserLoginResponse = async (newUserId: number, errorCode: commonProto.ErrorCode) => {
        logging.info('MTT: World player Logged in');
        userId = newUserId;
        if (errorCode) {
            reject(new Error(`WorldPlayer could not login: ${errorCode}`));
            return;
        }
        logging.setUserId(userId);

        clearInterval(tournamentStatusTimeout);
        tournamentStatusTimeout = setInterval(() => {
            if (seatNum >= 0 && gamePlayer.connectionStatus === CONNECTION_STATUS.LOGGED_IN) {
                updateProgress({ stats: stats, status: UserStatus.inGamePlay });
            } else {
                updateProgress({ stats: stats, status: UserStatus.waiting });
            }
        }, 5000);

        try {
            await updateTournamentDetails();
            const response = await httpApis.requestJoinedTournaments(token);
            const joinedTournament = response?.MttList?.find((t) => t.TournamentId === mttId);
            if (joinedTournament?.JoinStatus > 0) {
                logging.info(`Tournament ${mttId} already joined, status: ${joinedTournament.JoinStatus}`);
            } else if (joinedTournament?.JoinStatus < 0) {
                logging.info(`Reentering tournament ${mttId}, status: ${joinedTournament.JoinStatus}`);
                await worldPlayer.reenterTournament();
            } else {
                logging.info(`Signing up for tournament ${mttId}`);
                await worldPlayer.signup(onError);
            }
            clearInterval(tournamentConnectTimeout);
            if (!(await checkAndConnectToTournament(userId))) {
                tournamentConnectTimeout = setInterval(() => checkAndConnectToTournament(userId), 10_000);
            }
        } catch (error) {
            onError(error);
        }
    };

    const updateTournamentDetails = async (isRefresh = false) => {
        logging.info(`updateTournamentDetails: ${mttId} isRefresh: ${isRefresh}`);
        // feature: get table data

        const processTournamentDetails = (data: commonProto.IMttTournamentDetailResponse) => {
            if (!data) {
                throw new Error('Failed to fetch tournament details: no data');
            }
            if (!data?.TournamentDetail || data.ErrorCode) {
                logging.error(
                    `processTournamentDetails error: ${data.ErrorCode} - ${commonProto.ErrorCode[data.ErrorCode]}`,
                    data.ErrorCode,
                );
                throw new Error(
                    `Failed to fetch tournament details: ${commonProto.ErrorCode[data.ErrorCode] ?? data.ErrorCode}`
                );
            }
            tournament = data.TournamentDetail;
            if (tournament.PrizeMoney) {
                payoutStructure = tournament.PrizeMoney.map((p) => ({
                    position: p.Rank,
                    prize_value: parseInt(p.Money),
                })).filter((p) => p.prize_value > 0);
                tournamentPrizePool = payoutStructure.reduce((acc, p) => acc + p.prize_value, 0);
            } else {
                logging.warn('PrizeMoney not found in tournament details', data);
            }

            if (tournamentPlayers.length === 0) {
                tournamentPlayers = (tournament.PlayersDetail || []).map((player) => ({
                    stack: player.Coins,
                    rank: player.Rank || player.Index,
                }));
                tournamentPlayersSize = tournamentPlayers.length;

                const player = (tournament.PlayersDetail || []).find((player) => player.UserId === userId);
                if (player) {
                    stats = {
                        chips: player.Coins,
                        rank: player.Rank || player.Index,
                    };
                }
            }
        };

        try {
            let data = await fetchTournamentDetails(mttId);
            if (data != null) {
                logging.info(`Fetch tournament details successful processing: ${JSON.stringify(data)}`);
            } else if (!isRefresh) {
                logging.info(`Fetch tournament details failed, making a request`);
                data = await httpApis.requestMttTournamentDetail(dataManager.token, mttId);
            }
            processTournamentDetails(data);
        } catch (error) {
            if (!isRefresh) {
                onError(error);
            } else {
                logging.error('Skipping error: requestMttTournamentDetail error', error);
            }
        }
    };

    let cleanup = () => {
        clearInterval(tournamentConnectTimeout);
        clearInterval(tournamentStatusTimeout);
        worldPlayer.disconnect();
        gamePlayer.disconnect();
    };

    const onUserTokenRes = (msg: holdem.IUserTokenRes) => {
        logging.info('MTT onUserTokenRes');
        gamePlayer.MTTEnterRoom(mttId);
        worldPlayer.disconnect();
    };

    const onEnterGameRes = (msg: mttPro.IMttEnterGameRes) => {
        if (msg.code) {
            logging.info(`EnterGame: ${mttPro.Code[msg.code] ?? msg.code}`);
            if (msg.code === mttPro.Code.Mtt_End || msg.code === mttPro.Code.NO_MTT_ROOM) {
                throw new UnrecoverableError('Tournament ended');
            }
        }
    };

    const onEnterRoomRes = (msg: holdem.IEnterRoomRes) => {
        if (mttId === msg.mttId) {
            roomId = msg.roomId;
            logging.setRoomId(roomId);
            gamePlayer.roomId = roomId;
            state.roomid = roomId.toString();
            state.gameuuid = `${mttId}:${roomId}:${userId}:${toMillis(tournament.StartingTime)}`;
            state.big_blind = msg.bb;
            state.ante = msg.ante;
        }
    };

    const onRoomSnapshotMsg = (msg: holdem.IRoomSnapshotMsg) => {
        logging.info('onRoomSnapshotMsg', msg);
        if (mttId === msg.mttId) {
            holeCards = Object.values(msg.holeCards).map(decodeCard);
            boardCards = Object.values(msg.boardCards).map(decodeCard);
            pot = msg.pot && msg.pot.reduce((acc, val) => acc + val, 0);

            seatNum = -1;
            state.players = [];
            for (const playerData of msg.players) {
                const player: StrategyPlayerData = {
                    seat_no: playerData.seatNum - 1,
                    stack: playerData.leftCoin,
                };
                if (playerData.userId === userId) {
                    player.hole_cards = holeCards.join('');
                    seatNum = playerData.seatNum;
                }
                state.players.push(player);
            }

            state.bb_seat = msg.bbPos - 1;
            state.sb_seat = msg.sbPos - 1;
            state.dealer_seat = msg.dealerPos - 1;
            checkAndFixSeats();

            state.actions.entries = [];
            limitedGameMode = !!(msg.boardCards || msg.currAct);
            if (limitedGameMode) {
                logging.info('Setting limited game mode for the current hand');
            }
        }
    };

    const onMttRoomSnapshotRes = (msg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onMttRoomSnapshotRes', { payload: msg });

        currentLevel = msg.blindIndex;
        state.big_blind = tournament.HoldemBlindsConfig[currentLevel - 1]?.BigBlind;
        state.ante = tournament.HoldemBlindsConfig[currentLevel - 1]?.Ante;
    };

    const onNeedActionMsg = async (msg: holdem.INeedActionMsg) => {
        // If it's not current room - no need to act
        if (msg.roomId !== roomId) {
            return;
        }
        logging.info('[InGame] onNeedActionMsg', { msg });
        // add allined player's action if any
        // this covers the case when allined player is immediately after the bb
        if (allinedPlayers.size > 0) {
            const playerSeats = state.players.map((p) => p.seat_no);
            let playerSeatIdx = playerSeats.indexOf(msg.seatNum - 1);
            const prevSeat = playerSeats.at(playerSeatIdx - 1);
            addAllinedPlayerAction(prevSeat);
        }
        if (msg.seatNum === seatNum) {
            let stopWatch = new Date().getTime();
            let action =
                msg.optAction % 10 === holdem.Action.CHECK ? holdem.Action.CHECK : holdem.Action.FOLD;
            let amount: number;
            let mtt = mttData();
            let probability: number;

            if (!limitedGameMode) {
                const betProfileUserId = String(userId % 100);
                try {
                    let strategy = await fetchStrategy({
                        state,
                        mtt,
                        profileName,
                        betProfileUserId,
                    });
                    const result = processStrategy(strategy, msg);
                    action = result.action;
                    amount = result.amount;
                    probability = result.probability;
                } catch (error) {
                    logging.error('fetchStrategy error', error, { state, mtt });
                }
            }

            // game stage is of type GAME_PHASE which has value
            // according to the length of board cards
            // so we can assign it directly
            const gameStage: GAME_PHASE = boardCards.length;

            // if the player is the first to make action, do not take the probability into account
            // there is no actions or the last action was a board card(s) action
            if (state.actions.entries.length === 0 || /^[A-Z0-9]/.test(state.actions.entries.at(-1).action)) {
                probability = undefined;
            }

            const calculatedDelayMs = delayService.calculateDelayMs(gameStage, action, probability);
            const timePassed = new Date().getTime() - stopWatch;
            const delayLeftMs = calculatedDelayMs - timePassed;
            logging
                .withTag('DELAY')
                .info(
                    `Calculated delay: ${calculatedDelayMs} (ms) for ${holdem.Action[action]} on ${gameStage}, delay left: ${delayLeftMs} ms`,
                );

            if (delayLeftMs > 0) {
                await sleep(delayLeftMs);
            }

            gamePlayer.Action(action, amount);
        }
    };

    const mttData = () => {
        if (!tournament) {
            return undefined;
        }

        let blind = tournament.HoldemBlindsConfig[currentLevel - 1];

        // check if tournament players contains current room players
        // if not add them with artificial ranks
        let playersStacks = state.players.map((player) => player.stack).sort((a, b) => b - a);

        // remove from stacks players that are already in tournament players
        let i = 0,
            j = 0;
        while (i < tournamentPlayers.length && j < playersStacks.length) {
            if (tournamentPlayers[i].stack === playersStacks[j]) {
                playersStacks.splice(j, 1);
                i++;
            } else if (tournamentPlayers[i].stack > playersStacks[j]) {
                i++;
            } else {
                j++;
            }
        }

        // create artificial players with stacks that are not in tournament players
        let artificialPlayers = playersStacks.map((stack, i) => ({
            stack: stack,
            rank: tournamentPlayers.length + i + 1,
        }));

        // sort concatenated players by stack and set ranks accordingly
        let players = tournamentPlayers
            .concat(artificialPlayers)
            .sort((a, b) => b.stack - a.stack)
            .map((player, i) => ({
                stack: player.stack,
                rank: i + 1,
            }))
            .filter((player) => player.stack != null && player.stack > 0);

        let bounty;
        if (tournament.TournamentMode != commonProto.TOURNAMENT_MODE.NORMAL) {
            const bountyTypes = {
                [commonProto.TOURNAMENT_MODE.HUNTER]: 'normal_knockout_ko',
                [commonProto.TOURNAMENT_MODE.SUPER_HUNTER]: 'progressive_bounty_pko',
                [commonProto.TOURNAMENT_MODE.Mystery]: 'mystery_bounty',
            };
            bounty = {
                initial_bounty: tournament.BountyFee,
                bounty_pot: tournament.BountyPot,
                bounty_proportion: tournament.HunterModeBountyProportion,
                progressive_bounty_increasing_rate: tournament.HunterModeBountyIncreasingRate,
                bounty_type: bountyTypes[tournament.TournamentMode],
            };
        }

        let prizePool = (tournament.GamePot ?? 0) + (tournament.BountyPot ?? 0);
        return {
            initial_chips: tournament.StartingCoins,
            prize_fee: tournament.PrizeFee,
            bounty_fee: tournament.BountyFee,
            registration_fee: (tournament.PrizeFee ?? 0) + (tournament.BountyFee ?? 0),
            total_entries: tournament.JoinedCount,
            prize_pool: Math.max(prizePool, tournamentPrizePool),
            payout_structure: payoutStructure,
            total_registered_players: Math.max(
                tournament.PlayersCount,
                tournamentPlayersSize,
                players.length,
            ),
            players: players,
            current_level: currentLevel,
            current_blind_level: currentLevel,
            blind_structure: [
                {
                    level: blind.Level,
                    ante: blind.Ante,
                    small_blind: blind.SmallBlind,
                    big_blind: blind.BigBlind,
                },
            ],
            bounty_structure: bounty,
        };
    };

    const onPlayerActionMsg = (msg: holdem.IPlayerActionMsg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onPlayerActionMsg', { payload: msg });
        let action;
        let amount;
        switch (msg.action) {
            case holdem.Action.CHECK:
                action = 'check';
                break;
            case holdem.Action.CALL:
                action = 'call';
                break;
            case holdem.Action.BET:
                action = 'bet';
                amount = msg.deskCoin;
                break;
            case holdem.Action.FOLD:
                action = 'fold';
                break;
            case holdem.Action.RAISE:
                action = 'raise';
                amount = msg.deskCoin;
                break;
            case holdem.Action.ALL_IN:
                action = 'allin';
                amount = msg.deskCoin;
                break;
            default:
                logging.warn("unsupported player's action", msg);
                return;
        }
        let playerAction: { action: any; amount?: any; seat_no: number } = {
            action: action,
            seat_no: msg.seatNum - 1,
        };
        if (amount) {
            playerAction.amount = msg.deskCoin;
        }
        state.actions.entries.push(playerAction);
        pot += msg.deskCoin || 0;

        // add allined player's action if any
        // this covers most of the cases
        addAllinedPlayerAction(msg.seatNum % state.players.length);
    };

    const addAllinedPlayerAction = (seat: number) => {
        if (allinedPlayers.has(seat)) {
            logging.info(`adding allin action for seat ${seat}`);
            state.actions.entries.push({ action: 'allin', seat_no: seat });
            allinedPlayers.delete(seat);
        }
    };

    const onDealerPosMsg = (msg) => {
        if (roomId !== msg.roomId) return;
        logging.resetRoundValues();
        logging.info('[InGame] onDealerPosMsg', msg);

        state.big_blind = tournament.HoldemBlindsConfig[currentLevel - 1]?.BigBlind;
        state.ante = tournament.HoldemBlindsConfig[currentLevel - 1]?.Ante;
        state.players = msg.seats.map((seat) => ({
            seat_no: seat.seatNum - 1,
            stack: (seat.leftCoin || 0) + (seat.deskCoin || 0) + state.ante,
        }));
        state.bb_seat = msg.bbPos - 1;
        state.sb_seat = msg.sbPos - 1;
        state.dealer_seat = msg.dealerPos - 1;
        checkAndFixSeats();
        state.actions.entries = [];
        pot = msg.seats.reduce((acc, seat) => acc + (seat.deskCoin || 0), msg.pot);
        holeCards = [];
        boardCards = [];
        allinedPlayers = new Set();
        limitedGameMode = false;
        // get fresh tournament data before each round
        gamePlayer.requestTournamentInfo();
    };

    const checkAndFixSeats = () => {
        if (!state.players.find((player) => player.seat_no === state.sb_seat)) {
            state.sb_seat = -1;
        }
        if (!state.players.find((player) => player.seat_no === state.dealer_seat)) {
            state.dealer_seat = -1;
        }
    };

    const onHoleCardsMsg = (msg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onHoleCardsMsg', msg);

        for (const p of state.players) {
            if (p.seat_no === seatNum - 1) {
                const cards = Array.from(msg.cards).map(decodeCard);
                holeCards = cards;
                p.hole_cards = cards.join('');
            }
        }
    };

    const onBoardCardsMsg = (msg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onBoardCardsMsg', msg);

        let cards = Array.from(msg.cards).map(decodeCard);
        boardCards.push(...cards);
        state.actions.entries.push({ action: cards.join('') });
    };

    const onPlayerStateMsg = (msg: holdem.IPlayerStateMsg) => {
        if (roomId !== msg.roomId) return;
        logging.info('[InGame] onPlayerStateMsg', msg);
        if (msg.state === holdem.Action.ALL_IN) {
            let seat = msg.seatNum - 1;
            let player = state.players.find((player) => player.seat_no === seat);
            if (seat !== state.sb_seat && seat !== state.bb_seat && player?.stack > state.ante) {
                allinedPlayers.add(seat);
            }
        }
    };

    const onRiseBlindNotifyMsg = (msg) => {
        logging.info('onRiseBlindNotifyMsg', msg);
        if (roomId === msg.roomId) {
            currentLevel = msg.riseIndex;
        }
    };

    const onLeaveRoomRes = (msg) => {
        if (roomId === msg.roomId) {
            logging.info('LeaveRoomRes', msg);
        }
    };

    const onRoundResultMsg = (msg) => {
        if (roomId === msg.roomId) {
            logging.info('RoundResultMsg', msg);
        }
    };

    const onRewardMsg = (msg) => {
        if (mttId === msg.mttId && userId === msg.userId) {
            logging.info('RewardMsg', msg);
            updateProgress({ stats: { chips: 0, rank: msg.rank }, status: UserStatus.inGamePlay });
            finish();
        }
    };

    const onMttRealTimeRecordRes = (msg: mttPro.IMttRealTimeRecordRes) => {
        if (mttId === msg.mttId) {
            tournamentPlayers = msg.players.map((player) => ({
                stack: player.leftcoin,
                rank: player.rank,
            }));
            tournamentPlayersSize = msg.AllPlayerCount;

            if (msg.AllPlayerCount !== tournament.PlayersCount) {
                updateTournamentDetails(true);
            }

            const player = msg.players.find((player) => player.userId === userId);
            if (player) {
                stats = {
                    chips: player.leftcoin,
                    rank: player.rank,
                };
            } else {
                // if player is not in the list, compute stats from the last known state
                stats = {
                    chips: state.players.find((player) => player.seat_no === seatNum - 1)?.stack,
                    rank: msg.curPlayer.rank,
                };
            }
        }
    };

    const onSeatOccupiedMsg = (msg) => {
        logging.info('onSeatOccupiedMsg', msg);
        if (msg.roomId == roomId && msg.userId == userId) {
            seatNum = msg.seatNum;
        }
    };

    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.UserTokenRes, onUserTokenRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.MttEnterGameRes, onEnterGameRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.EnterRoomRes, onEnterRoomRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.RoomSnapshotMsg, onRoomSnapshotMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.MttRoomSnapshotRes, onMttRoomSnapshotRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.NeedActionMsg, onNeedActionMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.PlayerActionMsg, onPlayerActionMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.DealerPosMsg, onDealerPosMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.HoleCardsMsg, onHoleCardsMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.BoardCardsMsg, onBoardCardsMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.PlayerStateMsg, onPlayerStateMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.RiseBlindNotifyMsg, onRiseBlindNotifyMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.LeaveRoomRes, onLeaveRoomRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.RoundResultMsg, onRoundResultMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.SeatOccupiedMsg, onSeatOccupiedMsg);
    gamePlayer.websocket.addMessageHandler(mttPro.MessageId.RewardMsg, onRewardMsg);

    gamePlayer.onTournamentInfo = onMttRealTimeRecordRes;
    gamePlayer.onConnectionFailed = onError;

    worldPlayer.onLoggedIn = onUserLoginResponse;
    worldPlayer.onConnectionFailed = onError;
    worldPlayer.connect();

    return gamePromise;
}

const decodeCard = (card) => {
    let number = { 10: 'T', 11: 'J', 12: 'Q', 13: 'K', 14: 'A' }[card % 16] || card % 16;
    let suit = { 1: 'd', 2: 's', 4: 'h', 8: 'c' }[card >> 4];
    return number + suit;
};

const toMillis = (timestamp) => {
    if (timestamp instanceof Date) {
        return timestamp.getTime();
    } else if (timestamp && typeof timestamp.seconds === 'number' && typeof timestamp.nanos === 'number') {
        return timestamp.seconds * 1000 + timestamp.nanos / 1000000;
    } else {
        return 0;
    }
};
