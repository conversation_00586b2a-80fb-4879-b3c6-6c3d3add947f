import { holdem } from './mtt/pb/holdem';
import { GameAction, logging } from 'shared';

interface MTTAction {
    action: holdem.Action;
    amount?: number;
    probability: number | undefined;
}

export const processStrategy = (strategy: GameAction, msg: holdem.INeedActionMsg): MTTAction => {
    let action: holdem.Action;
    let amount: number;
    let probability: number = strategy.probability;

    switch (strategy.action) {
        case 'check':
            action = holdem.Action.CHECK;
            break;
        case 'call':
            action = holdem.Action.CALL;
            break;
        case 'bet':
            action = holdem.Action.BET;
            amount = strategy.amount;
            if (amount < msg.minBetCoin) {
                logging.warn('Fixing action: trying to bet but minimum bet is higher');
                amount = msg.minBetCoin;
            }
            break;
        case 'fold':
            action = holdem.Action.FOLD;
            break;
        case 'raise':
            action = holdem.Action.RAISE;
            amount = strategy.amount;
            if (amount < msg.minBetCoin) {
                amount = msg.minBetCoin;
            }
            break;
        case 'allin':
            action = holdem.Action.ALL_IN;
            break;
        default:
            throw new Error('Unsupported strategy action');
    }

    if (action === holdem.Action.CALL && msg.optAction === holdem.Action.CHECK) {
        logging.warn('Fixing action: trying to call but check is possible');
        action = holdem.Action.CHECK;
    }

    if (action === holdem.Action.CHECK && msg.optAction === holdem.Action.CALL) {
        logging.warn('Fixing action: trying to check but we have to call');
        action = holdem.Action.CALL;
    }

    if (action === holdem.Action.RAISE && msg.lastRaisePos === -1) {
        logging.warn('Fixing action: trying to raise but raise is not allowed');
        action = msg.optAction;
        amount = msg.optCoin;
        probability = undefined;
    }

    if (
        (action === holdem.Action.CALL || action === holdem.Action.RAISE) &&
        msg.optAction === holdem.Action.ALL_IN
    ) {
        logging.warn('Fixing action: trying to call/raise but have to all-in');
        action = holdem.Action.ALL_IN;
    }

    return { action, amount, probability };
};
